#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import sqlite3
import json
import traceback
from datetime import datetime
from database_config import get_database_path, get_database_connection
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from PyQt5.QtWebEngineWidgets import QWebEngineView
from PyQt5.QtSql import *
from sub100_window import ConfirmationDialogs

# تضمين دالة إشعار الشهادات الطبية من print6.py
try:
    from print6 import print_medical_certificate_notification
    PRINT6_AVAILABLE = True
    print("تم استيراد دالة إشعار الشهادات الطبية من print6.py بنجاح")
except ImportError:
    PRINT6_AVAILABLE = False
    print("تحذير: فشل استيراد print_medical_certificate_notification من print6.py")
    # Define dummy function if import fails
    def print_medical_certificate_notification(*args, **kwargs):
        print("خطأ: دالة الطباعة print_medical_certificate_notification غير متوفرة.")
        return False

class AbsenceJustificationViewerWindow(QMainWindow):
    """نافذة عرض سجلات تبريرات الغياب - تستخدم منهجية Python + HTML الحديثة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # المتغيرات الأساسية
        self.parent_window = parent
        self.justifications_data = []
        self.filtered_data = []
        
        # إعداد النافذة
        self.setupUI()

        # تحميل البيانات
        self.load_justifications_data()

        # التأكد من فتح النافذة في كامل الشاشة
        self.showMaximized()

    def showEvent(self, event):
        """التأكد من فتح النافذة في كامل الشاشة عند عرضها"""
        super().showEvent(event)
        self.showMaximized()

    def resizeEvent(self, event):
        """منع تصغير النافذة والحفاظ على كامل الشاشة"""
        super().resizeEvent(event)
    
    def setupUI(self):
        """إعداد واجهة المستخدم"""
        # إعدادات النافذة الأساسية
        self.setWindowTitle("📝 سجل تبريرات الغياب - عرض  ")
        self.setLayoutDirection(Qt.RightToLeft)
        
        # فتح النافذة في كامل الشاشة
        self.showMaximized()
        
        # إضافة أيقونة للنافذة
        try:
            self.setWindowIcon(QIcon("01.ico"))
        except:
            pass
        
        # تطبيق نمط احترافي للنافذة
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f0f4f8,
                    stop: 1 #e8f2f7
                );
            }
        """)
        
        # إنشاء الواجهة المركزية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # شريط الأدوات العلوي
        toolbar_frame = QFrame()
        toolbar_frame.setFrameStyle(QFrame.StyledPanel)
        toolbar_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #4CAF50,
                    stop: 1 #388E3C
                );
                border-radius: 10px;
                padding: 10px;
            }
        """)
        
        toolbar_layout = QHBoxLayout(toolbar_frame)
        toolbar_layout.setContentsMargins(15, 10, 15, 10)
        toolbar_layout.setSpacing(15)
        
        # شريط البحث برمز التلميذ فقط
        search_label = QLabel("🔍 البحث برمز التلميذ:")
        search_label.setStyleSheet("color: white; font-weight: bold; font-size: 14px;")
        toolbar_layout.addWidget(search_label)
        
        self.search_entry = QLineEdit()
        self.search_entry.setPlaceholderText("أدخل رمز التلميذ للبحث...")
        self.search_entry.setStyleSheet("""
            QLineEdit {
                background: white;
                border: 2px solid #2E7D32;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                min-width: 300px;
            }
            QLineEdit:focus {
                border-color: #1B5E20;
                box-shadow: 0 0 5px rgba(27, 94, 32, 0.5);
            }
        """)
        self.search_entry.textChanged.connect(self.on_search)
        toolbar_layout.addWidget(self.search_entry)
        
        # فلتر القسم
        section_label = QLabel("📚 القسم:")
        section_label.setStyleSheet("color: white; font-weight: bold; font-size: 14px;")
        toolbar_layout.addWidget(section_label)
        
        self.section_combo = QComboBox()
        self.section_combo.setStyleSheet("""
            QComboBox {
                background: white;
                border: 2px solid #2E7D32;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                min-width: 150px;
            }
            QComboBox:focus {
                border-color: #1B5E20;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
        """)
        self.section_combo.currentTextChanged.connect(self.on_filter)
        toolbar_layout.addWidget(self.section_combo)
        
        toolbar_layout.addStretch()
        
        # زر تحديث البيانات
        self.refresh_button = QPushButton("🔄 تحديث")
        self.refresh_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #FF9800,
                    stop: 1 #F57C00
                );
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #FFB74D,
                    stop: 1 #FF9800
                );
            }
            QPushButton:pressed {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #F57C00,
                    stop: 1 #E65100
                );
            }
        """)
        self.refresh_button.clicked.connect(self.load_justifications_data)
        toolbar_layout.addWidget(self.refresh_button)
        
        main_layout.addWidget(toolbar_frame)
        
        # منطقة عرض HTML
        self.web_view = QWebEngineView()
        self.web_view.setStyleSheet("""
            QWebEngineView {
                border: 2px solid #4CAF50;
                border-radius: 10px;
                background: white;
            }
        """)
        main_layout.addWidget(self.web_view)
        
        # شريط الأزرار السفلي
        buttons_frame = QFrame()
        buttons_frame.setFrameStyle(QFrame.StyledPanel)
        buttons_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #37474f,
                    stop: 1 #263238
                );
                border-radius: 10px;
                padding: 5px;
            }
        """)
        
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(15, 10, 15, 10)
        buttons_layout.setSpacing(15)
        
        # زر طباعة التقرير
        self.print_button = QPushButton("🖨️ طباعة التبرير المحدد")
        self.print_button.setFont(QFont("Calibri", 12, QFont.Bold))
        self.print_button.setMinimumHeight(40)
        self.print_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #2196F3,
                    stop: 1 #1976D2
                );
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #64B5F6,
                    stop: 1 #2196F3
                );
            }
            QPushButton:pressed {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #1976D2,
                    stop: 1 #0D47A1
                );
            }
        """)
        self.print_button.clicked.connect(self.print_report)
        buttons_layout.addWidget(self.print_button)
        
        # زر حذف التبريرات المحددة
        self.delete_button = QPushButton("🗑️ حذف المحددة")
        self.delete_button.setFont(QFont("Calibri", 12, QFont.Bold))
        self.delete_button.setMinimumHeight(40)
        self.delete_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f44336,
                    stop: 1 #d32f2f
                );
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f66356,
                    stop: 1 #f44336
                );
            }
            QPushButton:pressed {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #c62828,
                    stop: 1 #b71c1c
                );
            }
        """)
        self.delete_button.clicked.connect(self.delete_selected_justifications)
        buttons_layout.addWidget(self.delete_button)
        
        # زر إخبار بوضع شهادة طبية
        self.notify_cert_button = QPushButton("🏥 إخبار بوضع شهادة طبية")
        self.notify_cert_button.setFont(QFont("Calibri", 12, QFont.Bold))
        self.notify_cert_button.setMinimumHeight(40)
        self.notify_cert_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f39c12,
                    stop: 1 #e67e22
                );
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f5b342,
                    stop: 1 #f39c12
                );
            }
            QPushButton:pressed {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #e67e22,
                    stop: 1 #d35400
                );
            }
            QPushButton:disabled {
                background: #bdc3c7;
                color: #7f8c8d;
            }
        """)
        self.notify_cert_button.clicked.connect(self.notify_teachers_about_certificates)
        buttons_layout.addWidget(self.notify_cert_button)
        
        buttons_layout.addStretch()
        
        main_layout.addWidget(buttons_frame)
        
        # شريط الحالة
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.setStyleSheet("""
            QStatusBar {
                background-color: #f5f5f5;
                border-top: 1px solid #ddd;
                font-size: 12px;
                color: #666;
                padding: 5px;
            }
        """)
        self.status_bar.showMessage("جاهز...")
    
    def load_justifications_data(self):
        """تحميل بيانات تبريرات الغياب من قاعدة البيانات"""
        try:
            self.status_bar.showMessage("جاري تحميل البيانات...")
            
            conn = sqlite3.connect(get_database_path())
            cursor = conn.cursor()
            
            # تحميل جميع تبريرات الغياب
            cursor.execute('''
                SELECT id, رمز_التلميذ, اسم_التلميذ, ر_ت, القسم,
                       تاريخ_التبرير, تاريخ_البداية, تاريخ_النهاية, عدد_الأيام, سبب_الغياب
                FROM تبريرات_الغياب ORDER BY تاريخ_التبرير DESC
            ''')
            
            self.justifications_data = cursor.fetchall()
            
            # تحميل قائمة الأقسام للتصفية
            cursor.execute('SELECT DISTINCT القسم FROM تبريرات_الغياب WHERE القسم IS NOT NULL AND القسم != ""')
            sections = [row[0] for row in cursor.fetchall()]
            self.section_combo.clear()
            self.section_combo.addItems(['الكل'] + sorted(sections))
            
            conn.close()
            
            # تطبيق الفلاتر وإنشاء HTML
            self.apply_filters()
            
            self.status_bar.showMessage(f"تم تحميل {len(self.justifications_data)} تبرير")
            
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
            self.status_bar.showMessage(f"خطأ في تحميل البيانات: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل البيانات:\n{str(e)}")
    
    def apply_filters(self):
        """تطبيق الفلاتر على البيانات"""
        search_term = self.search_entry.text().strip()
        section_filter = self.section_combo.currentText()
        
        self.filtered_data = []
        for row in self.justifications_data:
            # تطبيق فلتر القسم (الفهرس 4)
            if section_filter != 'الكل' and str(row[4]) != section_filter:
                continue
                
            # تطبيق البحث برمز التلميذ فقط (الفهرس 1)
            if search_term:
                student_code = str(row[1] or '').strip()
                if search_term.lower() not in student_code.lower():
                    continue
                    
            self.filtered_data.append(row)
        
        # إنشاء وعرض HTML
        self.generate_html()
    
    def on_search(self):
        """معالج البحث"""
        self.apply_filters()
    
    def on_filter(self):
        """معالج التصفية"""
        self.apply_filters()
    
    def generate_html(self):
        """توليد HTML لعرض تبريرات الغياب"""
        data_to_display = self.filtered_data
        
        html_content = self.create_html_template(data_to_display)
        self.web_view.setHtml(html_content)
    
    def create_html_template(self, data):
        """إنشاء قالب HTML حديث"""
        html = f"""
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>سجل تبريرات الغياب</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        
        body {{
            font-family: 'Calibri', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);
            min-height: 100vh;
            padding: 20px;
            color: #1a1a1a;
            font-size: 17px;
            font-weight: bold;
        }}
        
        .container {{
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        
        .header {{
            background: linear-gradient(45deg, #4CAF50, #66BB6A);
            color: white;
            padding: 30px;
            text-align: center;
        }}
        
        .header h1 {{
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }}
        
        .stats {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            padding: 20px;
            background: #f8f9fa;
        }}
        
        .stat-card {{
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }}
        
        .stat-card:hover {{
            transform: translateY(-5px);
        }}
        
        .stat-number {{
            font-size: 2em;
            font-weight: bold;
            color: #4CAF50;
        }}
        
        .table-container {{
            padding: 20px;
            overflow-x: auto;
        }}
        
        table {{
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }}
        
        th {{
            background: linear-gradient(45deg, #4CAF50, #66BB6A);
            color: white;
            padding: 15px;
            text-align: center;
            font-weight: bold;
            position: sticky;
            top: 0;
            z-index: 10;
        }}
        
        td {{
            padding: 12px;
            text-align: center;
            border-bottom: 1px solid #eee;
            transition: background-color 0.3s ease;
        }}
        
        tr:hover td {{
            background-color: #f8f9fa;
        }}
        
        tr:nth-child(even) {{
            background-color: #f9f9f9;
        }}
        
        .justification-id {{
            background: #4CAF50;
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
            font-weight: bold;
        }}
        
        .student-name {{
            font-weight: bold;
            color: #2c3e50;
        }}
        
        .date {{
            color: #7f8c8d;
            font-size: 0.9em;
        }}
        
        .section {{
            background: #e74c3c;
            color: white;
            padding: 3px 8px;
            border-radius: 10px;
            font-size: 0.8em;
        }}
        
        .days {{
            background: #2196F3;
            color: white;
            padding: 3px 8px;
            border-radius: 10px;
            font-size: 0.8em;
            font-weight: bold;
        }}
        
        .reason {{
            max-width: 200px;
            word-wrap: break-word;
            font-size: 0.9em;
        }}
        
        .record-checkbox {{
            text-align: center;
            width: 60px;
        }}
        
        .record-select {{
            width: 18px;
            height: 18px;
            cursor: pointer;
            transform: scale(1.2);
        }}
        
        #select-all {{
            width: 18px;
            height: 18px;
            cursor: pointer;
            transform: scale(1.2);
        }}
        
        .record-row.selected {{
            background-color: #e8f5e8 !important;
            border-right: 4px solid #4CAF50;
        }}
        
        .empty-state {{
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }}
        
        .empty-icon {{
            font-size: 4em;
            margin-bottom: 20px;
            display: block;
            opacity: 0.5;
        }}
        
        @media (max-width: 768px) {{
            .header h1 {{
                font-size: 1.8em;
            }}
            
            table {{
                font-size: 0.8em;
            }}
            
            th, td {{
                padding: 8px;
            }}
        }}
    </style>
    
    <script>
        function toggleSelectAll() {{
            const selectAllBox = document.getElementById('select-all');
            const checkboxes = document.querySelectorAll('.record-select');
            
            checkboxes.forEach(checkbox => {{
                checkbox.checked = selectAllBox.checked;
                updateRowSelection(checkbox);
            }});
        }}
        
        function updateRowSelection(checkbox) {{
            const row = checkbox.closest('tr');
            if (checkbox.checked) {{
                row.classList.add('selected');
            }} else {{
                row.classList.remove('selected');
            }}
        }}
        
        function getSelectedJustifications() {{
            const selectedCheckboxes = document.querySelectorAll('.record-select:checked');
            const selectedJustifications = [];
            
            selectedCheckboxes.forEach(checkbox => {{
                selectedJustifications.push({{
                    id: checkbox.getAttribute('data-id'),
                    studentName: checkbox.getAttribute('data-student-name'),
                    date: checkbox.getAttribute('data-date')
                }});
            }});
            
            return selectedJustifications;
        }}
        
        document.addEventListener('DOMContentLoaded', function() {{
            const checkboxes = document.querySelectorAll('.record-select');
            checkboxes.forEach(checkbox => {{
                checkbox.addEventListener('change', function() {{
                    updateRowSelection(this);
                    
                    const allCheckboxes = document.querySelectorAll('.record-select');
                    const checkedBoxes = document.querySelectorAll('.record-select:checked');
                    const selectAllBox = document.getElementById('select-all');
                    
                    if (checkedBoxes.length === 0) {{
                        selectAllBox.indeterminate = false;
                        selectAllBox.checked = false;
                    }} else if (checkedBoxes.length === allCheckboxes.length) {{
                        selectAllBox.indeterminate = false;
                        selectAllBox.checked = true;
                    }} else {{
                        selectAllBox.indeterminate = true;
                    }}
                    
                    // تحديث حالة أزرار الطباعة والحذف
                    updateButtonStates();
                }});
            }});
            
            // تحديث حالة الأزرار عند التحميل الأولي
            updateButtonStates();
        }});
        
        function updateButtonStates() {{
            const checkedBoxes = document.querySelectorAll('.record-select:checked');
            const checkedCount = checkedBoxes.length;
            
            // يمكن إضافة منطق هنا لتحديث حالة الأزرار
            // لكن بما أن الأزرار في PyQt، سنتركها كما هي
        }}
    </script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📝 سجل تبريرات الغياب</h1>
            <p>تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M')}</p>
            <p style="font-size: 0.9em; opacity: 0.8;">💡 لطباعة تبرير: حدد تبرير واحد فقط ثم اضغط على زر "طباعة التبرير المحدد"</p>
            <p style="font-size: 0.9em; opacity: 0.8;">🏥 لإخبار الأساتذة: حدد الشهادات الطبية ثم اضغط على زر "إخبار بوضع شهادة طبية"</p>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">{len(data)}</div>
                <div>إجمالي التبريرات</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{len(set(row[2] for row in data if row[2]))}</div>
                <div>عدد التلاميذ</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{len(set(row[4] for row in data if row[4]))}</div>
                <div>الأقسام</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{sum(row[8] or 0 for row in data)}</div>
                <div>إجمالي الأيام</div>
            </div>
        </div>
        
        <div class="table-container">
        """
        
        if data:
            html += f"""
            <table>
                <thead>
                    <tr>
                        <th>
                            <input type="checkbox" id="select-all" onclick="toggleSelectAll()">
                            اختيار الكل
                        </th>
                        <th>الرقم</th>
                        <th>رمز التلميذ</th>
                        <th>اسم التلميذ</th>
                        <th>ر.ت</th>
                        <th>القسم</th>
                        <th>تاريخ التبرير</th>
                        <th>تاريخ البداية</th>
                        <th>تاريخ النهاية</th>
                        <th>عدد الأيام</th>
                        <th>سبب الغياب</th>
                    </tr>
                </thead>
                <tbody>
            """
            
            for row in data:
                html += f"""
                    <tr class="record-row">
                        <td class="record-checkbox">
                            <input type="checkbox" class="record-select" 
                                   data-id="{row[0] or ''}"
                                   data-student-name="{row[2] or ''}"
                                   data-date="{row[5] or ''}">
                        </td>
                        <td><span class="justification-id">{row[0] or 'غير محدد'}</span></td>
                        <td>{row[1] or 'غير محدد'}</td>
                        <td class="student-name">{row[2] or 'غير محدد'}</td>
                        <td>{row[3] or 'غير محدد'}</td>
                        <td><span class="section">{row[4] or 'غير محدد'}</span></td>
                        <td class="date">{row[5] or 'غير محدد'}</td>
                        <td class="date">{row[6] or 'غير محدد'}</td>
                        <td class="date">{row[7] or 'غير محدد'}</td>
                        <td><span class="days">{row[8] or 0}</span></td>
                        <td class="reason">{row[9] or 'غير محدد'}</td>
                    </tr>
                """
            
            html += """
                </tbody>
            </table>
            """
        else:
            html += """
            <div class="empty-state">
                <span class="empty-icon">📝</span>
                <h3>لا توجد تبريرات</h3>
                <p>لم يتم العثور على أي تبريرات تطابق معايير البحث</p>
            </div>
            """
        
        html += """
        </div>
    </div>
</body>
</html>
        """
        
        return html
    
    def delete_selected_justifications(self):
        """حذف التبريرات المحددة"""
        try:
            # تأكيد الحذف
            reply = QMessageBox.question(
                self, 
                "تأكيد الحذف", 
                "هل أنت متأكد من حذف تبريرات الغياب المحددة؟\n\n⚠️ هذا الإجراء لا يمكن التراجع عنه!",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply != QMessageBox.Yes:
                return
            
            # تنفيذ جافا سكريبت للحصول على التبريرات المحددة
            js_code = """
            (function() {
                const selectedJustifications = getSelectedJustifications();
                return JSON.stringify(selectedJustifications);
            })();
            """
            
            def handle_selected_justifications(result):
                try:
                    if not result:
                        QMessageBox.information(self, "تنبيه", "لم يتم تحديد أي تبريرات للحذف!")
                        return
                    
                    selected_justifications = json.loads(result)
                    if not selected_justifications:
                        QMessageBox.information(self, "تنبيه", "لم يتم تحديد أي تبريرات للحذف!")
                        return
                    
                    # حذف التبريرات من قاعدة البيانات
                    conn = sqlite3.connect(get_database_path())
                    cursor = conn.cursor()
                    deleted_count = 0
                    
                    for justification in selected_justifications:
                        cursor.execute(
                            "DELETE FROM تبريرات_الغياب WHERE id = ?",
                            (justification['id'],)
                        )
                        if cursor.rowcount > 0:
                            deleted_count += 1
                    
                    conn.commit()
                    conn.close()
                    
                    # إظهار نتيجة الحذف
                    if deleted_count > 0:
                        QMessageBox.information(
                            self, 
                            "نجح الحذف", 
                            f"تم حذف {deleted_count} تبرير بنجاح! ✅"
                        )
                        # إعادة تحميل البيانات
                        self.load_justifications_data()
                        self.status_bar.showMessage(f"تم حذف {deleted_count} تبرير")
                    else:
                        QMessageBox.warning(self, "خطأ", "فشل في حذف التبريرات!")
                        
                except Exception as e:
                    print(f"خطأ في معالجة التبريرات المحددة: {e}")
                    QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الحذف:\n{str(e)}")
            
            # تنفيذ الجافا سكريبت
            self.web_view.page().runJavaScript(js_code, handle_selected_justifications)
            
        except Exception as e:
            print(f"خطأ في حذف التبريرات: {e}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الحذف:\n{str(e)}")
    
    def print_report(self):
        """طباعة التقرير المحدد باستخدام print5.py"""
        try:
            # الحصول على التبرير المحدد من JavaScript
            js_code = """
            (function() {
                const selectedCheckboxes = document.querySelectorAll('.record-select:checked');
                if (selectedCheckboxes.length === 0) {
                    return null; // لا توجد تبريرات محددة
                }
                if (selectedCheckboxes.length > 1) {
                    return 'multiple'; // أكثر من تبرير محدد
                }
                
                // إرجاع معرف التبرير المحدد
                return selectedCheckboxes[0].getAttribute('data-id');
            })();
            """
            
            def handle_selected_justification(result):
                try:
                    if result is None:
                        QMessageBox.information(
                            self, 
                            "تنبيه", 
                            "⚠️ يرجى تحديد تبرير واحد فقط للطباعة!\n\nانقر على المربع بجانب التبرير المراد طباعته."
                        )
                        return
                    elif result == 'multiple':
                        QMessageBox.information(
                            self, 
                            "تنبيه", 
                            "⚠️ يرجى تحديد تبرير واحد فقط للطباعة!\n\nلا يمكن طباعة أكثر من تبرير في نفس الوقت."
                        )
                        return
                    
                    # الحصول على بيانات التبرير من قاعدة البيانات
                    justification_id = int(result)
                    self.print_single_justification(justification_id)
                    
                except Exception as e:
                    print(f"خطأ في معالجة التبرير المحدد: {e}")
                    QMessageBox.critical(self, "خطأ", f"حدث خطأ في معالجة التبرير:\n{str(e)}")
            
            self.web_view.page().runJavaScript(js_code, handle_selected_justification)
            
        except Exception as e:
            print(f"خطأ في طباعة التقرير: {e}")
            QMessageBox.warning(self, "خطأ", f"فشل في طباعة التقرير:\n{str(e)}")

    def print_single_justification(self, justification_id):
        """طباعة تبرير واحد باستخدام print5.py"""
        try:
            print(f"🖨️ بدء طباعة التبرير رقم: {justification_id}")
            
            # الحصول على بيانات التبرير من قاعدة البيانات
            conn = sqlite3.connect(get_database_path())
            cursor = conn.cursor()
            
            # استعلام شامل للحصول على جميع البيانات المطلوبة
            cursor.execute('''
                SELECT id, رمز_التلميذ, اسم_التلميذ, ر_ت, المستوى, القسم,
                       تاريخ_التبرير, تاريخ_البداية, تاريخ_النهاية, عدد_الأيام, 
                       سبب_الغياب, ملاحظات, السنة_الدراسية, الأسدس
                FROM تبريرات_الغياب WHERE id = ?
            ''', (justification_id,))
            
            result = cursor.fetchone()
            conn.close()
            
            if not result:
                QMessageBox.warning(self, "خطأ", f"لم يتم العثور على التبرير رقم {justification_id}")
                return
            
            # تحضير البيانات بالصيغة المطلوبة لـ print5.py
            record = {
                'student_code': str(result[1] or ''),
                'student_name': str(result[2] or ''),
                'student_id': str(result[3] or ''),  # ر_ت
                'level': str(result[4] or ''),
                'section': str(result[5] or ''),  # القسم
                'school_year': str(result[12] or ''),  # السنة_الدراسية
                'semester': str(result[13] or ''),  # الأسدس
                'justification_date': str(result[6] or ''),
                'start_date': str(result[7] or ''),
                'end_date': str(result[8] or ''),
                'days_count': str(result[9] or '1'),
                'reason': str(result[10] or ''),
                'notes': str(result[11] or '')
            }
            
            print(f"📋 البيانات المحضرة للطباعة: {record}")
            
            # استيراد وتنفيذ دالة الطباعة من print5.py
            try:
                import sys
                import os
                
                # إضافة مسار الملفات الحالي إلى sys.path
                current_dir = os.path.dirname(os.path.abspath(__file__))
                if current_dir not in sys.path:
                    sys.path.insert(0, current_dir)
                
                from print5 import print_absence_justification
                
                print("✅ تم استيراد دالة الطباعة من print5.py بنجاح")
                
                # تحديث شريط الحالة
                self.status_bar.showMessage("🖨️ جاري إنشاء وطباعة التقرير...")
                
                # تنفيذ الطباعة
                result_success = print_absence_justification(record, get_database_path())
                
                if result_success:
                    # عرض رسالة نجاح مفصلة
                    student_name = record.get('student_name', 'غير محدد')
                    success_message = f"✅ تم إنشاء وطباعة تبرير الغياب بنجاح!\n\n" \
                                    f"📋 تفاصيل التقرير:\n" \
                                    f"🆔 رقم التبرير: {justification_id}\n" \
                                    f"👤 التلميذ: {student_name}\n" \
                                    f"🔢 الرمز: {record.get('student_code', 'غير محدد')}\n" \
                                    f"🏫 المستوى: {record.get('level', 'غير محدد')}\n" \
                                    f"📚 القسم: {record.get('section', 'غير محدد')}\n" \
                                    f"📅 تاريخ البداية: {record.get('start_date', 'غير محدد')}\n" \
                                    f"📅 تاريخ النهاية: {record.get('end_date', 'غير محدد')}\n" \
                                    f"🗓️ عدد الأيام: {record.get('days_count', '1')}\n" \
                                    f"📝 السبب: {record.get('reason', 'غير محدد')}\n\n" \
                                    f"📁 تم حفظ التقرير في مجلد:\n" \
                                    f"'تقارير برنامج المعين في الحراسة العامة/تقارير تبرير الغياب'\n" \
                                    f"على سطح المكتب"
                    
                    QMessageBox.information(self, "نجح", success_message)
                    self.status_bar.showMessage(f"✅ تم طباعة التبرير رقم {justification_id} بنجاح")
                    
                    print(f"✅ تم إنشاء وطباعة التبرير رقم {justification_id} بنجاح")
                    
                else:
                    QMessageBox.warning(self, "تحذير", 
                        f"⚠️ تم إنشاء التقرير لكن قد تكون هناك مشكلة في الطباعة\n\nتحقق من مجلد التقارير على سطح المكتب")
                    self.status_bar.showMessage(f"⚠️ تم إنشاء التقرير رقم {justification_id} مع تحذيرات")
                
            except ImportError as import_error:
                print(f"❌ فشل في استيراد print5.py: {import_error}")
                QMessageBox.critical(self, "خطأ", 
                    f"❌ فشل في استيراد مكتبة الطباعة\n\nالخطأ:\n{str(import_error)}\n\nتأكد من وجود ملف print5.py")
                return
                
            except Exception as print_error:
                print(f"❌ خطأ في تنفيذ دالة الطباعة: {print_error}")
                import traceback
                traceback.print_exc()
                
                QMessageBox.critical(self, "خطأ في الطباعة", 
                    f"❌ فشل في إنشاء التقرير\n\nتفاصيل الخطأ:\n{str(print_error)}\n\n" \
                    f"الرجاء التأكد من:\n" \
                    f"• وجود مكتبات ReportLab\n" \
                    f"• صحة ملف print5.py\n" \
                    f"• الصلاحيات للكتابة على سطح المكتب")
                
                self.status_bar.showMessage("❌ فشل في إنشاء التقرير")
            
        except Exception as e:
            print(f"❌ خطأ عام في طباعة التبرير: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في عملية الطباعة:\n{str(e)}")
            self.status_bar.showMessage("❌ خطأ في الطباعة")
    
    def notify_teachers_about_certificates(self):
        """جمع بيانات الشهادات الطبية واختيار الأساتذة وطباعة الإشعار."""
        try:
            # الحصول على التبريرات المحددة من JavaScript
            js_code = """
            (function() {
                const selectedCheckboxes = document.querySelectorAll('.record-select:checked');
                const selectedJustifications = [];
                
                selectedCheckboxes.forEach(checkbox => {
                    // الحصول على صف البيانات
                    const row = checkbox.closest('tr');
                    const cells = row.querySelectorAll('td');
                    
                    // استخراج البيانات من الخلايا
                    if (cells.length >= 11) {
                        const reasonText = cells[10].textContent.trim(); // خلية سبب الغياب
                        
                        // التحقق من وجود "شهادة طبية" في السبب
                        if (reasonText.includes('شهادة طبية')) {
                            selectedJustifications.push({
                                id: cells[1].textContent.trim(),
                                studentCode: cells[2].textContent.trim(),
                                studentName: cells[3].textContent.trim(),
                                section: cells[5].textContent.trim(),
                                startDate: cells[7].textContent.trim(),
                                endDate: cells[8].textContent.trim(),
                                daysCount: cells[9].textContent.trim(),
                                reason: reasonText
                            });
                        }
                    }
                });
                
                return JSON.stringify(selectedJustifications);
            })();
            """
            
            def handle_selected_certificates(result):
                try:
                    if not result:
                        QMessageBox.warning(self, "تنبيه", "لم يتم تحديد أي تبريرات.")
                        return
                    
                    selected_certificates = json.loads(result)
                    if not selected_certificates:
                        QMessageBox.warning(self, "تنبيه", 
                            "لم يتم تحديد أي شهادات طبية صالحة.\n\n" +
                            "💡 تأكد من تحديد تبريرات تحتوي على 'شهادة طبية' في سبب الغياب.")
                        return
                    
                    # تنظيم البيانات
                    certificates_data = []
                    student_info = {}
                    class_name = None
                    
                    for i, cert in enumerate(selected_certificates):
                        certificates_data.append({
                            "number": i + 1,
                            "id": cert['id'],
                            "start_date": cert['startDate'],
                            "end_date": cert['endDate'],
                            "duration": cert['daysCount']
                        })
                        
                        if not student_info:
                            student_info['student_code'] = cert['studentCode']
                            student_info['student_name'] = cert['studentName']
                            class_name = cert['section']
                            student_info['class_name'] = class_name
                    
                    if not class_name:
                        QMessageBox.warning(self, "خطأ بيانات", "لم يتم العثور على اسم القسم للتلميذ.")
                        return
                    
                    # استخدام نافذة تأكيد مميزة
                    confirm_dialog = ConfirmationDialogs.create_teacher_selection_confirmation_dialog(self, class_name)
                    result = confirm_dialog.exec_()
                    
                    selected_teachers = None
                    if result == QDialog.Accepted:  # تم الضغط على زر "نعم"
                        # استخدام نافذة اختيار الأساتذة المحسنة
                        teachers_data = self.get_all_teachers()
                        if not teachers_data:
                            QMessageBox.warning(self, "تنبيه", "لم يتم العثور على بيانات الأساتذة.")
                            return
                        
                        tree_dialog = ConfirmationDialogs.create_tree_teacher_selection_dialog(self, class_name, get_database_path(), teachers_data)
                        if tree_dialog.exec_() == QDialog.Accepted:
                            selected_teachers = tree_dialog.get_selected_teachers()
                            if not selected_teachers:
                                print("لم يتم تحديد أي أستاذ.")
                                return
                        else:
                            print("تم إلغاء اختيار الأساتذة.")
                            return
                    else:  # تم الضغط على زر "لا" - إنشاء جدول أساتذة فارغ
                        print("سيتم إنشاء جدول أساتذة فارغ.")
                        selected_teachers = []
                    
                    notification_data = {
                        "student_name": student_info.get('student_name'),
                        "student_code": student_info.get('student_code'),
                        "class_name": student_info.get('class_name'),
                        "certificates": certificates_data,
                        "teachers": selected_teachers
                    }
                    
                    print(f"بيانات إشعار الشهادات الطبية للطباعة: {notification_data}")
                    
                    if PRINT6_AVAILABLE:
                        try:
                            # عرض نافذة تأكيد مميزة
                            confirm_dialog = ConfirmationDialogs.create_medical_certificate_notification_dialog(self, notification_data)
                            result = confirm_dialog.exec_()
                            
                            if result == QDialog.Accepted:  # تم الضغط على زر الطباعة
                                success = print_medical_certificate_notification(notification_data, get_database_path())
                                if success:
                                    QMessageBox.information(self, "نجح", "✅ تم إنشاء وطباعة إشعار الشهادات الطبية بنجاح!")
                                    self.status_bar.showMessage("✅ تم طباعة إشعار الشهادات الطبية بنجاح")
                                else:
                                    QMessageBox.warning(self, "فشل الطباعة", "حدث خطأ أثناء عملية الطباعة.")
                        except Exception as e:
                            QMessageBox.critical(self, "خطأ طباعة", f"حدث خطأ غير متوقع أثناء طباعة الإشعار:\n{e}")
                            traceback.print_exc()
                    else:
                        QMessageBox.critical(self, "خطأ", "وحدة طباعة الإشعارات (print6.py) غير متوفرة.")
                
                except Exception as e:
                    print(f"خطأ في معالجة الشهادات المحددة: {e}")
                    QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء معالجة الشهادات:\n{str(e)}")
                    traceback.print_exc()
            
            # تنفيذ الجافا سكريبت
            self.web_view.page().runJavaScript(js_code, handle_selected_certificates)
            
        except Exception as e:
            print(f"خطأ عام في إشعار الشهادات الطبية: {e}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في عملية الإشعار:\n{str(e)}")
            traceback.print_exc()
    
    def get_all_teachers(self):
        """استعلام لجلب بيانات جميع الأساتذة."""
        teachers = []
        conn = None
        try:
            conn = sqlite3.connect(get_database_path())
            cursor = conn.cursor()
            query = """
                SELECT المعرف, اسم_الأستاذ, المادة
                FROM الأساتذة
                WHERE اسم_الأستاذ IS NOT NULL AND اسم_الأستاذ != '' AND المادة IS NOT NULL AND المادة != ''
                ORDER BY المادة, اسم_الأستاذ
            """
            cursor.execute(query)
            results = cursor.fetchall()
            
            for teacher_id, teacher_name, subject in results:
                teachers.append({
                    "id": teacher_id,
                    "teacher_name": teacher_name,
                    "subject": subject
                })
            
            print(f"تم جلب {len(teachers)} أستاذ من قاعدة البيانات")
        except Exception as e:
            print(f"خطأ في جلب بيانات الأساتذة: {e}")
            traceback.print_exc()
        finally:
            if conn:
                conn.close()
        return teachers
    
    def closeEvent(self, event):
        """معالجة حدث إغلاق النافذة"""
        event.accept()

    def ensure_maximized(self):
        """دالة مساعدة لضمان فتح النافذة في كامل الشاشة"""
        self.showMaximized()
        self.activateWindow()
        self.raise_()


def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إنشاء النافذة
    window = AbsenceJustificationViewerWindow()
    window.ensure_maximized()
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
