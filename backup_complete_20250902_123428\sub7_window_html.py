"""
نافذة إعدادات الطابعة - Python + HTML
تم إعادة تصميم النافذة لتستخدم منهجية Python + HTML الحديثة

الميزات:
- واجهة HTML جميلة ومتجاوبة
- إعدادات شاملة للطابعات الحرارية والعادية
- تكامل كامل مع قاعدة البيانات
- دعم أنواع اتصال متعددة (Windows، IP، Serial)
- تصميم عصري ومرن
"""

import sys
import os
import json
import sqlite3
import platform
from datetime import datetime
from database_config import get_database_path, get_database_connection
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QMessageBox, QFileDialog
from PyQt5.QtWebEngineWidgets import QWebEngineView
from PyQt5.QtWebChannel import QWebChannel
from PyQt5.QtCore import QObject, pyqtSlot, pyqtSignal, Qt
from PyQt5.QtGui import QIcon

# محاولة استيراد مكتبات الطابعة
try:
    from PyQt5.QtPrintSupport import QPrinterInfo
except ImportError:
    QPrinterInfo = None

try:
    import win32print
except ImportError:
    win32print = None

import subprocess


class PrinterEngine(QObject):
    """محرك إدارة إعدادات الطابعة"""

    # إشارات للتواصل مع واجهة HTML
    logUpdated = pyqtSignal(str, str, str)  # message, status, timestamp
    settingsUpdated = pyqtSignal(str)  # settings data JSON
    printersUpdated = pyqtSignal(str)  # printers list JSON

    def __init__(self):
        super().__init__()
        self.db_path = get_database_path()
        self.settings_table = "إعدادات_الطابعة"
        self.setup_database()

    def emit_log(self, message, status="info"):
        """إرسال رسالة إلى واجهة HTML"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.logUpdated.emit(message, status, timestamp)

    def setup_database(self):
        """إعداد قاعدة البيانات وإنشاء جداول الطابعة"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # إنشاء جدول إعدادات الطابعة
            cursor.execute(f'''
                CREATE TABLE IF NOT EXISTS "{self.settings_table}" (
                    id INTEGER PRIMARY KEY,
                    الطابعة_الحرارية TEXT,
                    الطابعة_العادية TEXT,
                    عرض_الورق INTEGER DEFAULT 80,
                    نوع_الاتصال TEXT DEFAULT 'windows',
                    معرف_البائع TEXT,
                    معرف_المنتج TEXT,
                    عنوان_IP TEXT,
                    المنفذ INTEGER DEFAULT 9100,
                    المنفذ_التسلسلي TEXT,
                    معدل_الباود INTEGER DEFAULT 9600,
                    قص_الورق BOOLEAN DEFAULT 1,
                    عدد_الأحرف_في_السطر INTEGER DEFAULT 42,
                    " تاريخ_التحديث" TEXT
                )
            ''')
            
            conn.commit()
            conn.close()
            self.emit_log("✅ تم إعداد قاعدة بيانات الطابعة بنجاح", "success")
            
        except Exception as e:
            self.emit_log(f"❌ خطأ في إعداد قاعدة البيانات: {str(e)}", "error")

    @pyqtSlot(result=str)
    def getAvailablePrinters(self):
        """الحصول على قائمة الطابعات المتاحة"""
        try:
            printers = []
            
            # الحصول على طابعات Windows
            if QPrinterInfo:
                for printer in QPrinterInfo.availablePrinters():
                    printers.append({
                        "name": printer.printerName(),
                        "description": printer.description(),
                        "isDefault": printer.isDefault(),
                        "type": "windows"
                    })
            
            # إضافة طابعات تجريبية
            test_printers = [
                {"name": "طابعة حرارية تجريبية", "description": "للاختبار", "isDefault": False, "type": "thermal"},
                {"name": "طابعة عادية تجريبية", "description": "للاختبار", "isDefault": False, "type": "regular"}
            ]
            printers.extend(test_printers)
            
            return json.dumps(printers, ensure_ascii=False)
            
        except Exception as e:
            self.emit_log(f"❌ خطأ في جلب قائمة الطابعات: {str(e)}", "error")
            return json.dumps([], ensure_ascii=False)

    @pyqtSlot(result=str)
    def getPrinterSettings(self):
        """الحصول على إعدادات الطابعة"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute(f"SELECT * FROM \"{self.settings_table}\" WHERE id = 1")
            row = cursor.fetchone()
            
            if row:
                # الحصول على أسماء الأعمدة
                cursor.execute(f"PRAGMA table_info(\"{self.settings_table}\")")
                columns = [info[1] for info in cursor.fetchall()]
                
                # تحويل البيانات إلى قاموس
                settings = dict(zip(columns, row))
            else:
                # إعدادات افتراضية
                settings = {
                    "الطابعة_الحرارية": "",
                    "الطابعة_العادية": "",
                    "عرض_الورق": 80,
                    "نوع_الاتصال": "windows",
                    "معرف_البائع": "",
                    "معرف_المنتج": "",
                    "عنوان_IP": "",
                    "المنفذ": 9100,
                    "المنفذ_التسلسلي": "",
                    "معدل_الباود": 9600,
                    "قص_الورق": True,
                    "عدد_الأحرف_في_السطر": 42
                }
            
            conn.close()
            return json.dumps(settings, ensure_ascii=False)
            
        except Exception as e:
            self.emit_log(f"❌ خطأ في جلب إعدادات الطابعة: {str(e)}", "error")
            return json.dumps({}, ensure_ascii=False)

    @pyqtSlot(str)
    def savePrinterSettings(self, settings_json):
        """حفظ إعدادات الطابعة"""
        try:
            settings = json.loads(settings_json)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # التحقق من وجود إعدادات
            cursor.execute(f"SELECT COUNT(*) FROM \"{self.settings_table}\"")
            count = cursor.fetchone()[0]
            
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            if count > 0:
                # تحديث الإعدادات الموجودة
                cursor.execute(f'''
                    UPDATE "{self.settings_table}" SET
                    الطابعة_الحرارية = ?, الطابعة_العادية = ?, عرض_الورق = ?,
                    نوع_الاتصال = ?, معرف_البائع = ?, معرف_المنتج = ?,
                    عنوان_IP = ?, المنفذ = ?, المنفذ_التسلسلي = ?,
                    معدل_الباود = ?, قص_الورق = ?, عدد_الأحرف_في_السطر = ?,
                    " تاريخ_التحديث" = ?
                    WHERE id = 1
                ''', (
                    settings.get("الطابعة_الحرارية", ""),
                    settings.get("الطابعة_العادية", ""),
                    settings.get("عرض_الورق", 80),
                    settings.get("نوع_الاتصال", "windows"),
                    settings.get("معرف_البائع", ""),
                    settings.get("معرف_المنتج", ""),
                    settings.get("عنوان_IP", ""),
                    settings.get("المنفذ", 9100),
                    settings.get("المنفذ_التسلسلي", ""),
                    settings.get("معدل_الباود", 9600),
                    settings.get("قص_الورق", True),
                    settings.get("عدد_الأحرف_في_السطر", 42),
                    current_time
                ))
            else:
                # إدراج إعدادات جديدة
                cursor.execute(f'''
                    INSERT INTO "{self.settings_table}" (
                        الطابعة_الحرارية, الطابعة_العادية, عرض_الورق,
                        نوع_الاتصال, معرف_البائع, معرف_المنتج,
                        عنوان_IP, المنفذ, المنفذ_التسلسلي,
                        معدل_الباود, قص_الورق, عدد_الأحرف_في_السطر,
                        " تاريخ_التحديث"
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    settings.get("الطابعة_الحرارية", ""),
                    settings.get("الطابعة_العادية", ""),
                    settings.get("عرض_الورق", 80),
                    settings.get("نوع_الاتصال", "windows"),
                    settings.get("معرف_البائع", ""),
                    settings.get("معرف_المنتج", ""),
                    settings.get("عنوان_IP", ""),
                    settings.get("المنفذ", 9100),
                    settings.get("المنفذ_التسلسلي", ""),
                    settings.get("معدل_الباود", 9600),
                    settings.get("قص_الورق", True),
                    settings.get("عدد_الأحرف_في_السطر", 42),
                    current_time
                ))
            
            conn.commit()
            conn.close()
            
            # إرسال إشارة النجاح
            self.settingsUpdated.emit("success")
            self.emit_log("✅ تم حفظ إعدادات الطابعة بنجاح", "success")
            
        except Exception as e:
            self.settingsUpdated.emit(f"error:{str(e)}")
            self.emit_log(f"❌ خطأ في حفظ إعدادات الطابعة: {str(e)}", "error")

    @pyqtSlot(str)
    def setDefaultPrinter(self, printer_name):
        """تعيين طابعة افتراضية"""
        try:
            if win32print and platform.system() == "Windows":
                win32print.SetDefaultPrinter(printer_name)
                self.emit_log(f"✅ تم تعيين {printer_name} كطابعة افتراضية", "success")
            else:
                self.emit_log("⚠️ تعيين الطابعة الافتراضية غير متاح في هذا النظام", "warning")
                
        except Exception as e:
            self.emit_log(f"❌ خطأ في تعيين الطابعة الافتراضية: {str(e)}", "error")

    @pyqtSlot()
    def testThermalPrinter(self):
        """اختبار الطابعة الحرارية"""
        try:
            self.emit_log("🔄 جاري اختبار الطابعة الحرارية...", "info")
            # هنا يمكن إضافة كود اختبار الطابعة الحرارية
            self.emit_log("✅ تم اختبار الطابعة الحرارية بنجاح", "success")
            
        except Exception as e:
            self.emit_log(f"❌ خطأ في اختبار الطابعة الحرارية: {str(e)}", "error")

    @pyqtSlot()
    def testRegularPrinter(self):
        """اختبار الطابعة العادية"""
        try:
            self.emit_log("🔄 جاري اختبار الطابعة العادية...", "info")
            # هنا يمكن إضافة كود اختبار الطابعة العادية
            self.emit_log("✅ تم اختبار الطابعة العادية بنجاح", "success")
            
        except Exception as e:
            self.emit_log(f"❌ خطأ في اختبار الطابعة العادية: {str(e)}", "error")

    @pyqtSlot()
    def refreshPrinters(self):
        """تحديث قائمة الطابعات"""
        try:
            self.emit_log("🔄 جاري تحديث قائمة الطابعات...", "info")
            printers = self.getAvailablePrinters()
            self.printersUpdated.emit(printers)
            self.emit_log("✅ تم تحديث قائمة الطابعات بنجاح", "success")
            
        except Exception as e:
            self.emit_log(f"❌ خطأ في تحديث قائمة الطابعات: {str(e)}", "error")


class PrinterSettingsWindow(QMainWindow):
    """نافذة إعدادات الطابعة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("🖨️ إعدادات الطابعة")
        
        # إزالة أزرار التحكم وإظهار شريط العنوان فقط
        self.setWindowFlags(Qt.Window | Qt.CustomizeWindowHint | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
          # تحديد الحد الأدنى لحجم النافذة
        self.setMinimumSize(1200, 650)
        self.resize(1200, 650)

        # إضافة أيقونة البرنامج
        icon_path = "01.ico"
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))

        # إنشاء محرك الطابعة
        self.printer_engine = PrinterEngine()

        # إعداد الواجهة
        self.setup_ui()
        self.setup_web_channel()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(0, 0, 0, 0)

        # إنشاء عرض الويب
        self.web_view = QWebEngineView()
        layout.addWidget(self.web_view)

        # تحميل واجهة HTML
        html_content = self.get_complete_html()
        self.web_view.setHtml(html_content)

    def setup_web_channel(self):
        """إعداد قناة التواصل"""
        self.channel = QWebChannel()
        self.channel.registerObject("printerEngine", self.printer_engine)
        self.web_view.page().setWebChannel(self.channel)

    def on_page_loaded(self):
        """استدعاء عند انتهاء تحميل الصفحة"""
        # القناة جاهزة والكائن مسجل مسبقاً، لا حاجة لإعادة التسجيل
        pass

    def get_complete_html(self):
        """HTML كامل مع CSS و JavaScript"""
        return """<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="utf-8">
    <title>إعدادات الطابعة</title>
    <script src="qrc:///qtwebchannel/qwebchannel.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Calibri', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            color: #333;
            overflow: hidden;
        }

        /* إخفاء أشرطة التمرير */
        * {
            scrollbar-width: none;
            -ms-overflow-style: none;
        }
        
        *::-webkit-scrollbar {
            display: none;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 15px;
            height: 100vh;
            overflow: hidden;
        }

        .header {
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-family: 'Calibri', sans-serif;
            font-size: 24pt;
            font-weight: bold;
            color: #1e3a8a;
            margin-bottom: 10px;
        }

        .header p {
            font-family: 'Calibri', sans-serif;
            font-size: 16pt;
            color: #666;
        }        .main-content {
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
            height: calc(100vh - 150px);
        }

        .settings-panel {
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .panel-title {
            font-family: 'Calibri', sans-serif;
            font-size: 18pt;
            font-weight: bold;
            color: #1e3a8a;
            margin-bottom: 20px;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }        .form-group {
            display: grid;
            grid-template-columns: 200px 1fr;
            gap: 15px;
            align-items: center;
        }

        .form-group label {
            font-family: 'Calibri', sans-serif;
            font-size: 15pt;
            font-weight: bold;
            color: #1e3a8a;
        }

        .form-group input,
        .form-group select {
            font-family: 'Calibri', sans-serif;
            font-size: 14pt;
            font-weight: bold;
            color: #1a1a1a;
            padding: 10px 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 5px rgba(102, 126, 234, 0.3);
        }        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
            grid-column: 2;
        }

        .checkbox-group input[type="checkbox"] {
            width: 20px;
            height: 20px;
        }

        .button-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin-top: 20px;
        }        .btn {
            font-family: 'Calibri', sans-serif;
            font-size: 15pt;
            font-weight: bold;
            color: #1a1a1a;
            padding: 25px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            justify-content: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(86, 171, 47, 0.4);
        }

        .btn-info {
            background: linear-gradient(135deg, #3498db 0%, #85c1e9 100%);
        }

        .btn-info:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12 0%, #f8c471 100%);
        }

        .btn-warning:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(243, 156, 18, 0.4);
        }        .connection-settings {
            display: none;
            margin-top: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #ddd;
            grid-column: 1 / -1;
        }

        .connection-settings.show {
            display: block;
        }

        .message-box {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            font-family: 'Calibri', sans-serif;
            font-size: 14pt;
            font-weight: bold;
            color: white;
            display: none;
            z-index: 1000;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        }

        .message-box.success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }

        .message-box.error {
            background: linear-gradient(135deg, #e74c3c 0%, #f39c12 100%);
        }

        .message-box.show {
            display: block;
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }        @media (max-width: 900px) {
            .form-group {
                grid-template-columns: 1fr;
                gap: 5px;
            }
            
            .form-group label {
                margin-bottom: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- الرأس -->
        <div class="header">
            <h1>🖨️ إعدادات الطابعة</h1>
            <p>إدارة شاملة لإعدادات الطابعات الحرارية والعادية</p>
        </div>        <!-- المحتوى الرئيسي -->
        <div class="main-content">
            <!-- لوحة الإعدادات -->
            <div class="settings-panel">
                <h2 class="panel-title">⚙️ إعدادات الطابعة</h2>
                
                <form id="printerSettingsForm">
                    <div class="form-grid">
                        <!-- الطابعة الحرارية -->
                        <div class="form-group">
                            <label for="thermalPrinter">الطابعة الحرارية:</label>
                            <select id="thermalPrinter" name="الطابعة_الحرارية">
                                <option value="">اختر الطابعة الحرارية</option>
                            </select>
                        </div>

                        <!-- الطابعة العادية -->
                        <div class="form-group">
                            <label for="regularPrinter">الطابعة العادية:</label>
                            <select id="regularPrinter" name="الطابعة_العادية">
                                <option value="">اختر الطابعة العادية</option>
                            </select>
                        </div>

                        <!-- عرض الورق -->
                        <div class="form-group">
                            <label for="paperWidth">عرض الورق (مم):</label>
                            <input type="number" id="paperWidth" name="عرض_الورق" value="80" min="58" max="80">
                        </div>

                        <!-- نوع الاتصال -->
                        <div class="form-group">
                            <label for="connectionType">نوع الاتصال:</label>
                            <select id="connectionType" name="نوع_الاتصال" onchange="toggleConnectionSettings()">
                                <option value="windows">Windows</option>
                                <option value="network">شبكة (IP)</option>
                                <option value="serial">تسلسلي (Serial)</option>
                                <option value="usb">USB مباشر</option>
                            </select>
                        </div>

                        <!-- إعدادات الشبكة -->
                        <div id="networkSettings" class="connection-settings">
                            <div class="form-group">
                                <label for="ipAddress">عنوان IP:</label>
                                <input type="text" id="ipAddress" name="عنوان_IP" placeholder="*************">
                            </div>
                            <div class="form-group">
                                <label for="port">المنفذ:</label>
                                <input type="number" id="port" name="المنفذ" value="9100" min="1" max="65535">
                            </div>
                        </div>

                        <!-- إعدادات USB -->
                        <div id="usbSettings" class="connection-settings">
                            <div class="form-group">
                                <label for="vendorId">معرف البائع (Vendor ID):</label>
                                <input type="text" id="vendorId" name="معرف_البائع" placeholder="0x04b8">
                            </div>
                            <div class="form-group">
                                <label for="productId">معرف المنتج (Product ID):</label>
                                <input type="text" id="productId" name="معرف_المنتج" placeholder="0x0202">
                            </div>
                        </div>

                        <!-- إعدادات التسلسلي -->
                        <div id="serialSettings" class="connection-settings">
                            <div class="form-group">
                                <label for="serialPort">المنفذ التسلسلي:</label>
                                <input type="text" id="serialPort" name="المنفذ_التسلسلي" placeholder="COM1">
                            </div>
                            <div class="form-group">
                                <label for="baudRate">معدل الباود:</label>
                                <select id="baudRate" name="معدل_الباود">
                                    <option value="9600">9600</option>
                                    <option value="19200">19200</option>
                                    <option value="38400">38400</option>
                                    <option value="57600">57600</option>
                                    <option value="115200">115200</option>
                                </select>                            </div>
                        </div>

                    </div>

                    <div class="button-group">
                        <button type="button" class="btn btn-success" onclick="saveSettings()">
                            💾 حفظ الإعدادات
                        </button>
                        <button type="button" class="btn btn-info" onclick="loadSettings()">
                            🔄 تحديث الإعدادات
                        </button>
                        <button type="button" class="btn btn-primary" onclick="refreshPrinters()">
                            🔍 تحديث الطابعات
                        </button>
                        <button type="button" class="btn btn-warning" onclick="testThermalPrinter()">
                            🖨️ اختبار حرارية
                        </button>
                        <button type="button" class="btn btn-warning" onclick="testRegularPrinter()">
                            🖨️ اختبار عادية
                        </button>                    </div>
                </form>
            </div>
        </div>

        <!-- رسائل النجاح والخطأ -->
        <div class="message-box" id="messageBox"></div>
    </div>

    <script>
        let printerEngine = null;
        let isChannelReady = false;

        // إعداد قناة التواصل مع Python
        function initializeChannel() {
            if (typeof qt !== 'undefined' && qt.webChannelTransport) {
                try {
                    new QWebChannel(qt.webChannelTransport, function(channel) {
                        printerEngine = channel.objects.printerEngine;
                        
                        if (printerEngine) {
                            isChannelReady = true;
                            console.log('🚀 QWebChannel initialized successfully');
                            
                            // ربط الإشارات
                            printerEngine.settingsUpdated.connect(handleSettingsUpdate);
                            printerEngine.printersUpdated.connect(updatePrintersList);

                            // تحميل البيانات الأولية
                            loadSettings();
                            refreshPrinters();

                            console.log('✅ تم تهيئة نظام إعدادات الطابعة بنجاح');
                        } else {
                            console.log('❌ فشل في الحصول على كائن printerEngine');
                            setTimeout(initializeChannel, 500);
                        }
                    });
                } catch (error) {
                    console.error('خطأ في تهيئة QWebChannel:', error);
                    setTimeout(initializeChannel, 500);
                }
            } else {
                console.log('⚠️ QWebChannel not available, retrying...');
                setTimeout(initializeChannel, 100);
            }
        }

        // تحميل إعدادات الطابعة
        function loadSettings() {
            if (!isChannelReady || !printerEngine) {
                console.log('⚠️ القناة غير جاهزة لتحميل الإعدادات');
                return;
            }
            
            try {
                printerEngine.getPrinterSettings(function(result) {
                    try {
                        let settings;
                        if (typeof result === 'string') {
                            settings = JSON.parse(result);
                        } else {
                            settings = result;
                        }
                        
                        fillSettingsForm(settings);
                        console.log('✅ تم تحميل الإعدادات بنجاح');
                    } catch (error) {
                        console.error('خطأ في تحليل إعدادات الطابعة:', error);
                        showMessage('❌ خطأ في تحميل الإعدادات', 'error');
                    }
                });
            } catch (error) {
                console.error('خطأ في استدعاء getPrinterSettings:', error);
            }
        }

        // تحديث قائمة الطابعات
        function refreshPrinters() {
            if (!isChannelReady || !printerEngine) {
                console.log('⚠️ القناة غير جاهزة لتحديث الطابعات');
                showMessage('❌ النظام غير جاهز بعد', 'error');
                return;
            }
            
            try {
                console.log('🔄 جاري تحديث قائمة الطابعات...');
                printerEngine.refreshPrinters();
            } catch (error) {
                console.error('خطأ في تحديث الطابعات:', error);
                showMessage('❌ خطأ في تحديث قائمة الطابعات', 'error');
            }
        }        // ملء نموذج الإعدادات
        function fillSettingsForm(settings) {
            document.getElementById('thermalPrinter').value = settings.الطابعة_الحرارية || '';
            document.getElementById('regularPrinter').value = settings.الطابعة_العادية || '';
            document.getElementById('paperWidth').value = settings.عرض_الورق || 80;
            document.getElementById('connectionType').value = settings.نوع_الاتصال || 'windows';
            document.getElementById('ipAddress').value = settings.عنوان_IP || '';
            document.getElementById('port').value = settings.المنفذ || 9100;
            document.getElementById('vendorId').value = settings.معرف_البائع || '';
            document.getElementById('productId').value = settings.معرف_المنتج || '';
            document.getElementById('serialPort').value = settings.المنفذ_التسلسلي || '';
            document.getElementById('baudRate').value = settings.معدل_الباود || 9600;
            
            toggleConnectionSettings();
        }        // حفظ الإعدادات
        function saveSettings() {
            if (!isChannelReady || !printerEngine) {
                console.log('⚠️ القناة غير جاهزة لحفظ الإعدادات');
                showMessage('❌ النظام غير جاهز بعد', 'error');
                return;
            }
            
            try {
                const settings = {
                    الطابعة_الحرارية: document.getElementById('thermalPrinter').value,
                    الطابعة_العادية: document.getElementById('regularPrinter').value,
                    عرض_الورق: parseInt(document.getElementById('paperWidth').value),
                    نوع_الاتصال: document.getElementById('connectionType').value,
                    عنوان_IP: document.getElementById('ipAddress').value,
                    المنفذ: parseInt(document.getElementById('port').value),
                    معرف_البائع: document.getElementById('vendorId').value,
                    معرف_المنتج: document.getElementById('productId').value,
                    المنفذ_التسلسلي: document.getElementById('serialPort').value,
                    معدل_الباود: parseInt(document.getElementById('baudRate').value)
                };

                console.log('💾 جاري حفظ الإعدادات...');
                printerEngine.savePrinterSettings(JSON.stringify(settings));
            } catch (error) {
                console.error('خطأ في حفظ الإعدادات:', error);
                showMessage('❌ خطأ في حفظ الإعدادات', 'error');
            }
        }

        // اختبار الطابعة الحرارية
        function testThermalPrinter() {
            if (printerEngine) {
                printerEngine.testThermalPrinter();
            } else {
                showMessage('❌ النظام غير جاهز بعد', 'error');
            }
        }

        // اختبار الطابعة العادية
        function testRegularPrinter() {
            if (printerEngine) {
                printerEngine.testRegularPrinter();
            } else {
                showMessage('❌ النظام غير جاهز بعد', 'error');
            }
        }

        // تبديل إعدادات الاتصال
        function toggleConnectionSettings() {
            const connectionType = document.getElementById('connectionType').value;
            
            // إخفاء جميع إعدادات الاتصال
            document.getElementById('networkSettings').classList.remove('show');
            document.getElementById('usbSettings').classList.remove('show');
            document.getElementById('serialSettings').classList.remove('show');
            
            // إظهار الإعدادات المناسبة
            if (connectionType === 'network') {
                document.getElementById('networkSettings').classList.add('show');
            } else if (connectionType === 'usb') {
                document.getElementById('usbSettings').classList.add('show');
            } else if (connectionType === 'serial') {
                document.getElementById('serialSettings').classList.add('show');
            }
        }

        // تحديث قائمة الطابعات
        function updatePrintersList(printersJson) {
            try {
                let printers;
                if (typeof printersJson === 'string') {
                    printers = JSON.parse(printersJson);
                } else {
                    printers = printersJson;
                }

                const thermalSelect = document.getElementById('thermalPrinter');
                const regularSelect = document.getElementById('regularPrinter');
                
                // حفظ القيم الحالية
                const currentThermal = thermalSelect.value;
                const currentRegular = regularSelect.value;
                
                // تفريغ القوائم
                thermalSelect.innerHTML = '<option value="">اختر الطابعة الحرارية</option>';
                regularSelect.innerHTML = '<option value="">اختر الطابعة العادية</option>';
                
                // إضافة الطابعات
                printers.forEach(printer => {
                    const thermalOption = document.createElement('option');
                    thermalOption.value = printer.name;
                    thermalOption.textContent = printer.name + (printer.isDefault ? ' (افتراضية)' : '');
                    thermalSelect.appendChild(thermalOption);
                    
                    const regularOption = document.createElement('option');
                    regularOption.value = printer.name;
                    regularOption.textContent = printer.name + (printer.isDefault ? ' (افتراضية)' : '');
                    regularSelect.appendChild(regularOption);
                });
                
                // استعادة القيم
                thermalSelect.value = currentThermal;
                regularSelect.value = currentRegular;
                
            } catch (error) {
                console.error('خطأ في تحديث قائمة الطابعات:', error);
            }
        }

        // معالجة تحديث الإعدادات
        function handleSettingsUpdate(result) {
            if (result === "success") {
                showMessage("✅ تم حفظ الإعدادات بنجاح", "success");
            } else if (result.startsWith("error:")) {
                const errorMsg = result.substring(6);
                showMessage("❌ خطأ في حفظ الإعدادات: " + errorMsg, "error");
            }
        }

        // عرض الرسائل
        function showMessage(message, type) {
            const messageBox = document.getElementById('messageBox');
            messageBox.textContent = message;
            messageBox.className = `message-box ${type} show`;
            
            setTimeout(() => {
                messageBox.classList.remove('show');
            }, 3000);
        }

        // تهيئة النظام عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            initializeChannel();
        });
    </script>
</body>
</html>"""


def main():
    """تشغيل نافذة إعدادات الطابعة"""
    app = QApplication(sys.argv)

    # إعداد التطبيق
    app.setApplicationName("إعدادات الطابعة")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("Modern Education Systems")

    # إنشاء النافذة
    window = PrinterSettingsWindow()
    window.show()

    # تشغيل التطبيق
    sys.exit(app.exec_())


if __name__ == '__main__':
    print("🌐 بدء تشغيل نظام إعدادات الطابعة...")
    print("=" * 60)
    print("📋 الميزات:")
    print("   🔹 واجهة HTML جميلة ومتجاوبة")
    print("   🔹 إعدادات شاملة للطابعات")
    print("   🔹 دعم أنواع اتصال متعددة")
    print("   🔹 اختبار الطابعات")
    print("   🔹 تكامل كامل مع قاعدة البيانات")
    print("=" * 60)
    print("🚀 جاري تشغيل النظام...")
    print("💡 ملاحظة: تجاهل رسالة 'Registered new object' - هذا أمر طبيعي")
    print("=" * 60)

    try:
        main()
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        print("💡 تأكد من تثبيت PyQt5:")
        print("   pip install PyQt5 PyQtWebEngine")
        import traceback
        traceback.print_exc()
