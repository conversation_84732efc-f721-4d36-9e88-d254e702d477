#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف اختبار لتجربة إضافة الصفوف الفارغة في تقارير الخميس
"""

import datetime
import os
from split_attendance_report import SplitAttendanceReport
from split_attendance_report_second_half_10 import SplitAttendanceReportSecondHalf10

def test_thursday_reports():
    """اختبار إضافة الصفوف الفارغة في تقارير الخميس"""
    
    # بيانات وهمية للاختبار
    students = [
        {"id": 1, "name": "أحمد محمد", "rt": "1"},
        {"id": 2, "name": "فاطمة علي", "rt": "2"},
        {"id": 3, "name": "محمد حسن", "rt": "3"},
        {"id": 4, "name": "عائشة أحمد", "rt": "4"},
        {"id": 5, "name": "يوسف إبراهيم", "rt": "5"},
    ]
    
    class_name = "1/1"
    institution_data = {
        "academy": "الأكاديمية الجهوية للتربية والتكوين",
        "directorate": "المديرية الإقليمية",
        "institution": "الثانوية التأهيلية",
        "academic_year": "2024/2025"
    }
    
    start_date = datetime.datetime.now()
    periods_8 = ["1", "2", "3", "4", "5", "6", "7", "8"]
    periods_10 = ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10"]
    
    # إنشاء مجلد للاختبار
    test_dir = "test_thursday_reports"
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)
    
    # اختبار تقرير الخميس (8 حصص)
    print("اختبار تقرير الخميس (8 حصص)...")
    split_report_generator = SplitAttendanceReport()
    
    thursday_8_file = os.path.join(test_dir, "test_thursday_8_periods.pdf")
    success = split_report_generator.generate_second_half(
        thursday_8_file,
        students,
        class_name,
        institution_data,
        start_date,
        1,  # نوع التقرير
        periods_8
    )
    
    if success:
        print(f"✓ تم إنشاء تقرير الخميس (8 حصص) بنجاح: {thursday_8_file}")
    else:
        print("✗ فشل في إنشاء تقرير الخميس (8 حصص)")
    
    # اختبار تقرير الخميس (10 حصص)
    print("اختبار تقرير الخميس (10 حصص)...")
    thursday_10_generator = SplitAttendanceReportSecondHalf10()
    
    thursday_10_file = os.path.join(test_dir, "test_thursday_10_periods.pdf")
    success = thursday_10_generator.generate_pdf(
        thursday_10_file,
        students,
        class_name,
        institution_data,
        start_date,
        1,  # نوع التقرير
        periods_10,
        ["السبت", "الجمعة", "الخميس"]  # أيام النصف الثاني
    )
    
    if success:
        print(f"✓ تم إنشاء تقرير الخميس (10 حصص) بنجاح: {thursday_10_file}")
    else:
        print("✗ فشل في إنشاء تقرير الخميس (10 حصص)")
    
    print("\nتم الانتهاء من اختبار تقارير الخميس!")
    print(f"يمكنك العثور على ملفات الاختبار في مجلد: {test_dir}")

if __name__ == "__main__":
    test_thursday_reports()
