# -*- coding: utf-8 -*-
"""
وحدة مساعدة للتعامل مع قاعدة البيانات
تتضمن وظائف مختلفة للتعامل مع قاعدة البيانات SQLite
"""

import os
import sqlite3
import time
import shutil

# إضافة فئة مدير قاعدة البيانات
class DatabaseManager:
    """فئة مدير قاعدة البيانات لإدارة الاتصالات والاستعلامات"""

    _instance = None  # نمط Singleton

    def __init__(self, db_path=None):
        """تهيئة مدير قاعدة البيانات"""
        if db_path:
            self.db_path = db_path
        else:
            self.db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "data.db")

        self.connection = None
        self._connect()

    def _connect(self):
        """إنشاء اتصال بقاعدة البيانات"""
        try:
            if not os.path.exists(self.db_path):
                raise FileNotFoundError(f"قاعدة البيانات غير موجودة في المسار: {self.db_path}")

            self.connection = sqlite3.connect(self.db_path)
            self.connection.row_factory = sqlite3.Row  # للوصول إلى النتائج عن طريق اسم العمود
            return True
        except Exception as e:
            print(f"خطأ في الاتصال بقاعدة البيانات: {str(e)}")
            return False

    def execute_query(self, query, params=None, fetch_all=True):
        """تنفيذ استعلام في قاعدة البيانات"""
        try:
            if not self.connection:
                self._connect()

            cursor = self.connection.cursor()

            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)

            if query.strip().upper().startswith(("SELECT", "PRAGMA")):
                if fetch_all:
                    result = cursor.fetchall()
                else:
                    result = cursor.fetchone()
                return True, result, None
            else:
                self.connection.commit()
                return True, None, None
        except Exception as e:
            print(f"خطأ في تنفيذ الاستعلام: {str(e)}")
            return False, None, str(e)

    def close(self):
        """إغلاق الاتصال بقاعدة البيانات"""
        if self.connection:
            self.connection.close()
            self.connection = None

    def check_student_enrollment(self, student_id, academic_year=None):
        """
        التحقق مما إذا كان الطالب مسجلاً في السنة الدراسية
        Args:
            student_id: رمز التلميذ
            academic_year: السنة الدراسية (اختياري)
        Returns:
            (is_enrolled, section, message) حيث:
            - is_enrolled: قيمة منطقية تشير إلى ما إذا كان التلميذ مسجلاً
            - section: القسم الذي ينتمي إليه التلميذ (إذا كان مسجلاً)
            - message: رسالة توضيحية
        """
        try:
            if not self.connection:
                self._connect()

            cursor = self.connection.cursor()

            # التحقق من وجود التلميذ أولاً
            cursor.execute("SELECT رمز_التلميذ, الاسم_الكامل, القسم, الحالة FROM بيانات_التلاميذ WHERE رمز_التلميذ = ?", (student_id,))
            student_data = cursor.fetchone()

            if not student_data:
                return False, None, "التلميذ غير موجود في قاعدة البيانات"

            student_status = student_data[3] if len(student_data) > 3 else None
            student_section = student_data[2] if len(student_data) > 2 else None
            student_name = student_data[1] if len(student_data) > 1 else "غير معروف"

            # التحقق من حالة التلميذ
            if student_status != "نشط":
                return False, None, f"التلميذ {student_name} غير نشط حاليا (الحالة: {student_status})"

            # التحقق من السنة الدراسية إذا كانت محددة
            if academic_year:
                # يمكن هنا إضافة استعلام إضافي للتحقق من تسجيل التلميذ في السنة الدراسية المحددة
                # على سبيل المثال، جدول_التسجيل أو ما شابه
                pass

            return True, student_section, f"التلميذ {student_name} مسجل في القسم {student_section}"

        except Exception as e:
            return False, None, f"خطأ أثناء التحقق من تسجيل التلميذ: {str(e)}"

    def get_current_school_year(self):
        """
        الحصول على السنة الدراسية الحالية من جدول بيانات_المؤسسة
        Returns:
            (success, year, error) حيث:
            - success: قيمة منطقية تشير إلى نجاح العملية
            - year: السنة الدراسية الحالية
            - error: رسالة الخطأ إن وجدت
        """
        try:
            if not self.connection:
                self._connect()

            cursor = self.connection.cursor()
            cursor.execute("SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
            result = cursor.fetchone()

            if result and result[0]:
                return True, result[0], None
            else:
                return False, "2023/2024", "لم يتم العثور على السنة الدراسية في جدول بيانات_المؤسسة"

        except Exception as e:
            return False, "2023/2024", f"خطأ أثناء الحصول على السنة الدراسية: {str(e)}"

    def get_current_semester(self):
        """
        الحصول على الأسدس الحالي من جدول بيانات_المؤسسة
        Returns:
            (success, semester, error) حيث:
            - success: قيمة منطقية تشير إلى نجاح العملية
            - semester: الأسدس الحالي
            - error: رسالة الخطأ إن وجدت
        """
        try:
            if not self.connection:
                self._connect()

            cursor = self.connection.cursor()
            cursor.execute("SELECT الأسدس FROM بيانات_المؤسسة LIMIT 1")
            result = cursor.fetchone()

            if result and result[0]:
                return True, result[0], None
            else:
                return False, "الأول", "لم يتم العثور على الأسدس في جدول بيانات_المؤسسة"

        except Exception as e:
            return False, "الأول", f"خطأ أثناء الحصول على الأسدس: {str(e)}"

    def ensure_violations_table_exists(self):
        """التأكد من وجود جدول المخالفات وإنشائه إذا لم يكن موجوداً"""
        try:
            if not self.connection:
                self._connect()

            cursor = self.connection.cursor()

            # التحقق من وجود جدول المخالفات
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='المخالفات'")
            if not cursor.fetchone():
                # إنشاء جدول المخالفات إذا لم يكن موجوداً
                create_table_query = """
                CREATE TABLE المخالفات (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    التاريخ TEXT,
                    رمز_التلميذ TEXT,
                    اسم_التلميذ TEXT,
                    المستوى TEXT,
                    القسم TEXT,
                    المادة TEXT,
                    الأستاذ TEXT,
                    الملاحظات TEXT,
                    الإجراءات TEXT,
                    إجراءات_الحراسة TEXT,
                    السنة_الدراسية TEXT,
                    الأسدس TEXT
                )
                """
                cursor.execute(create_table_query)
                self.connection.commit()
                print("تم إنشاء جدول المخالفات بنجاح")
                return True
            return True
        except Exception as e:
            print(f"خطأ في التحقق من وجود جدول المخالفات: {str(e)}")
            return False

    def save_violation(self, violation_data):
        """
        حفظ مخالفة جديدة في قاعدة البيانات
        Args:
            violation_data: قاموس يحتوي على بيانات المخالفة
        Returns:
            (success, message) حيث:
            - success: قيمة منطقية تشير إلى نجاح العملية
            - message: رسالة توضيحية
        """
        try:
            if not self.connection:
                self._connect()

            # التأكد من وجود جدول المخالفات
            self.ensure_violations_table_exists()

            cursor = self.connection.cursor()

            # تحضير البيانات للإدخال
            query = """
                INSERT INTO المخالفات (
                    التاريخ, رمز_التلميذ, اسم_التلميذ, المستوى, القسم,
                    المادة, الأستاذ, الملاحظات, الإجراءات, إجراءات_الحراسة,
                    السنة_الدراسية, الأسدس
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """

            params = (
                violation_data.get('date'),
                violation_data.get('student_code'),
                violation_data.get('student_name'),
                violation_data.get('level'),
                violation_data.get('section'),
                violation_data.get('subject'),
                violation_data.get('teacher'),
                violation_data.get('notes'),
                violation_data.get('procedures'),
                violation_data.get('guard_actions'),
                violation_data.get('school_year'),
                violation_data.get('semester')
            )

            cursor.execute(query, params)

            # تحديث عدد المخالفات في السجل العام
            update_query = """
                UPDATE السجل_العام
                SET عدد_المخالفات = (
                    SELECT COUNT(*)
                    FROM المخالفات
                    WHERE رمز_التلميذ = ?
                    AND الأسدس = ?
                    AND السنة_الدراسية = ?
                )
                WHERE الرمز = ?
            """
            update_params = (
                violation_data.get('student_code'),
                violation_data.get('semester'),
                violation_data.get('school_year'),
                violation_data.get('student_code')
            )

            cursor.execute(update_query, update_params)
            self.connection.commit()

            print("تم حفظ المخالفة بنجاح")
            return True

        except Exception as e:
            print(f"خطأ في حفظ المخالفة: {str(e)}")
            return False

# الدالة المطلوبة التي تستخدمها sub11_window.py
def get_db_manager(db_path=None):
    """الحصول على نسخة من مدير قاعدة البيانات"""
    if DatabaseManager._instance is None:
        DatabaseManager._instance = DatabaseManager(db_path)
    elif db_path and DatabaseManager._instance.db_path != db_path:
        # إذا تم تغيير مسار قاعدة البيانات، قم بإنشاء نسخة جديدة
        DatabaseManager._instance.close()
        DatabaseManager._instance = DatabaseManager(db_path)
    return DatabaseManager._instance

def get_db_path():
    """الحصول على مسار قاعدة البيانات الرئيسية"""
    return os.path.join(os.path.dirname(os.path.abspath(__file__)), "data.db")

def create_connection(path=None):
    """إنشاء اتصال بقاعدة البيانات"""
    try:
        db_path = path if path else get_db_path()
        if not os.path.exists(db_path):
            return False, None, f"قاعدة البيانات غير موجودة في المسار: {db_path}"

        conn = sqlite3.connect(db_path)
        return True, conn, None
    except Exception as e:
        return False, None, f"خطأ في الاتصال بقاعدة البيانات: {str(e)}"

def compress_database(db_path=None):
    """
    ضغط قاعدة البيانات SQLite عن طريق استخدام VACUUM
    """
    try:
        if not db_path:
            db_path = get_db_path()

        if not os.path.exists(db_path):
            print(f"خطأ: قاعدة البيانات غير موجودة في المسار: {db_path}")
            return False

        # إنشاء نسخة احتياطية قبل الضغط
        backup_path = f"{db_path}.bak"
        try:
            shutil.copy2(db_path, backup_path)
            print(f"تم إنشاء نسخة احتياطية: {backup_path}")
        except Exception as e:
            print(f"تحذير: لم يتم إنشاء نسخة احتياطية: {str(e)}")

        # ضغط قاعدة البيانات
        conn = sqlite3.connect(db_path)
        conn.execute("VACUUM")
        conn.commit()
        conn.close()

        print(f"تم ضغط قاعدة البيانات بنجاح: {db_path}")
        return True
    except Exception as e:
        print(f"خطأ أثناء ضغط قاعدة البيانات: {str(e)}")
        return False

def execute_query(db_path, query, params=None, fetch_all=True):
    """
    تنفيذ استعلام في قاعدة البيانات
    """
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)

        if query.strip().upper().startswith(("SELECT", "PRAGMA")):
            if fetch_all:
                result = cursor.fetchall()
            else:
                result = cursor.fetchone()
            conn.close()
            return True, result, None
        else:
            conn.commit()
            conn.close()
            return True, None, None
    except Exception as e:
        if 'conn' in locals() and conn:
            conn.close()
        return False, None, str(e)

def get_violation_types():
    """الحصول على قائمة أنواع المخالفات"""
    db_path = get_db_path()
    success, result, error = execute_query(
        db_path,
        "SELECT كود_المخالفة, وصف_المخالفة FROM المخالفات ORDER BY كود_المخالفة"
    )

    if success:
        return result
    else:
        print(f"خطأ في الحصول على أنواع المخالفات: {error}")
        return []

def get_violation_actions():
    """الحصول على قائمة الإجراءات المتخذة"""
    db_path = get_db_path()
    success, result, error = execute_query(
        db_path,
        "SELECT كود_الإجراء, وصف_الإجراء FROM الإجراءات ORDER BY كود_الإجراء"
    )

    if success:
        return result
    else:
        print(f"خطأ في الحصول على الإجراءات المتخذة: {error}")
        return []

def save_violation(violation_data):
    """حفظ مخالفة جديدة في قاعدة البيانات"""
    db_path = get_db_path()
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # أدخل بيانات المخالفة
        cursor.execute("""
            INSERT INTO سجل_المخالفات (
                رمز_التلميذ, كود_المخالفة, تاريخ_المخالفة, الوقت,
                الموقع, وصف_المخالفة, كود_الإجراء, ملاحظات, تمت_المعالجة, مرات_التكرار
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            violation_data.get('student_id'),
            violation_data.get('violation_code'),
            violation_data.get('date'),
            violation_data.get('time'),
            violation_data.get('location'),
            violation_data.get('description'),
            violation_data.get('action_code'),
            violation_data.get('notes'),
            violation_data.get('processed', 0),
            violation_data.get('repetition', 1)
        ))

        conn.commit()
        record_id = cursor.lastrowid
        conn.close()

        return True, record_id, None
    except Exception as e:
        if 'conn' in locals() and conn:
            conn.close()
        return False, None, str(e)

def get_active_students():
    """الحصول على قائمة الطلاب النشطين"""
    db_path = get_db_path()
    success, result, error = execute_query(
        db_path,
        """
        SELECT رمز_التلميذ, الاسم_الكامل, القسم
        FROM بيانات_التلاميذ
        WHERE الحالة = 'نشط'
        ORDER BY القسم, الاسم_الكامل
        """
    )

    if success:
        return result
    else:
        print(f"خطأ في الحصول على قائمة الطلاب النشطين: {error}")
        return []

def check_student_enrollment(student_id, academic_year=None, db_path=None):
    """
    التحقق مما إذا كان الطالب مسجلاً في السنة الدراسية
    Args:
        student_id: رمز التلميذ
        academic_year: السنة الدراسية (اختياري)
        db_path: مسار قاعدة البيانات (اختياري)
    Returns:
        (is_enrolled, section, message) حيث:
        - is_enrolled: قيمة منطقية تشير إلى ما إذا كان التلميذ مسجلاً
        - section: القسم الذي ينتمي إليه التلميذ (إذا كان مسجلاً)
        - message: رسالة توضيحية
    """
    try:
        if not db_path:
            db_path = get_db_path()

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # التحقق من وجود التلميذ أولاً
        cursor.execute("SELECT رمز_التلميذ, الاسم_الكامل, القسم, الحالة FROM بيانات_التلاميذ WHERE رمز_التلميذ = ?", (student_id,))
        student_data = cursor.fetchone()

        if not student_data:
            return False, None, "التلميذ غير موجود في قاعدة البيانات"

        student_status = student_data[3] if len(student_data) > 3 else None
        student_section = student_data[2] if len(student_data) > 2 else None
        student_name = student_data[1] if len(student_data) > 1 else "غير معروف"

        # التحقق من حالة التلميذ
        if student_status != "نشط":
            return False, None, f"التلميذ {student_name} غير نشط حاليا (الحالة: {student_status})"

        # التحقق من السنة الدراسية إذا كانت محددة
        if academic_year:
            # يمكن هنا إضافة استعلام إضافي للتحقق من تسجيل التلميذ في السنة الدراسية المحددة
            # على سبيل المثال، جدول_التسجيل أو ما شابه
            pass

        return True, student_section, f"التلميذ {student_name} مسجل في القسم {student_section}"

    except Exception as e:
        return False, None, f"خطأ أثناء التحقق من تسجيل التلميذ: {str(e)}"
    finally:
        if 'conn' in locals() and conn:
            conn.close()

def get_student_data(student_id, db_path=None):
    """
    الحصول على جميع بيانات الطالب
    Args:
        student_id: رمز التلميذ
        db_path: مسار قاعدة البيانات (اختياري)
    Returns:
        (success, data, message) حيث:
        - success: قيمة منطقية تشير إلى نجاح العملية
        - data: قاموس يحتوي على بيانات التلميذ (إذا كان موجودًا)
        - message: رسالة توضيحية
    """
    try:
        if not db_path:
            db_path = get_db_path()

        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row  # للوصول إلى النتائج عن طريق اسم العمود
        cursor = conn.cursor()

        cursor.execute("SELECT * FROM بيانات_التلاميذ WHERE رمز_التلميذ = ?", (student_id,))
        student_data = cursor.fetchone()

        if not student_data:
            return False, None, "التلميذ غير موجود في قاعدة البيانات"

        # تحويل Row إلى قاموس
        student_dict = {key: student_data[key] for key in student_data.keys()}

        return True, student_dict, "تم الحصول على بيانات التلميذ بنجاح"

    except Exception as e:
        return False, None, f"خطأ أثناء الحصول على بيانات التلميذ: {str(e)}"
    finally:
        if 'conn' in locals() and conn:
            conn.close()
