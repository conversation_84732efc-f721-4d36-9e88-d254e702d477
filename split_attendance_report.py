import os
import datetime
from reportlab.lib import colors as reportlab_colors
from reportlab.lib.pagesizes import A4  # تغيير إلى A4 بدلاً من A3
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.lib.units import cm

import arabic_reshaper
from bidi.algorithm import get_display

class SplitAttendanceReport:
    def __init__(self):
        self.arabic_font = self._load_arabic_fonts()

    def _load_arabic_fonts(self):
        arabic_fonts = [
            ("Calibri", "c:/windows/fonts/calibri.ttf"),
            ("ARIALUNI", "c:/windows/fonts/ARIALUNI.TTF"),
            ("Arial", "c:/windows/fonts/arial.ttf"),
            ("TraditionalArabic", "c:/windows/fonts/trado.ttf"),
            ("SimplifiedArabic", "c:/windows/fonts/simpo.ttf"),
            ("ArabicTypesetting", "c:/windows/fonts/arabtype.ttf"),
            ("Tahoma", "c:/windows/fonts/tahoma.ttf")
        ]
        for font_name, font_path in arabic_fonts:
            if os.path.exists(font_path):
                pdfmetrics.registerFont(TTFont(font_name, font_path))
                return font_name
        return 'Helvetica'

    def arabic_text(self, text):
        reshaped_text = arabic_reshaper.reshape(text)
        bidi_text = get_display(reshaped_text)
        return bidi_text

    def _is_student_hidden_from_attendance(self, rt_value):
        """
        التحقق الذكي من إخفاء التلميذ من لائحة الغياب
        يتحقق من وجود علامة X في عمود رت
        """
        if not rt_value:
            return False

        rt_value = str(rt_value).strip()

        # التلميذ مخفي إذا كان عمود رت يحتوي على "X" (إما X فقط أو رقم + مسافة + X)
        return rt_value == "X" or rt_value.endswith(" X")

    def generate_pdf(self, file_path, students, class_name, institution_data, start_date, report_type, periods=None, days=None):
        # فلترة التلاميذ - إزالة التلاميذ الذين لديهم علامة X في عمود رت
        filtered_students = []
        hidden_count = 0

        print(f"=== تشخيص فلترة التلاميذ في {class_name} (split_attendance_report.py) ===")
        print(f"عدد التلاميذ قبل الفلترة: {len(students)}")

        for i, student in enumerate(students):
            # التحقق من وجود علامة X في عمود رت
            rt_value = student.get('rt', '') or student.get('id', '') or ''
            rt_value = str(rt_value).strip()

            # طباعة تشخيص للتلاميذ الأوائل والأواخر
            if i < 3 or i >= len(students) - 3:
                print(f"التلميذ {i+1}: {student.get('name', 'بدون اسم')}, rt='{rt_value}'")

            # التحقق الذكي: هل التلميذ مخفي من لائحة الغياب؟
            is_hidden = self._is_student_hidden_from_attendance(rt_value)

            if is_hidden:
                hidden_count += 1
                print(f"تم إخفاء التلميذ: {student.get('name', 'بدون اسم')}, rt='{rt_value}'")
            else:
                filtered_students.append(student)

        print(f"عدد التلاميذ المخفيين: {hidden_count}")
        print(f"عدد التلاميذ بعد الفلترة: {len(filtered_students)}")
        print("=" * 50)

        # استخدام القائمة المفلترة بدلاً من القائمة الأصلية
        students = filtered_students

        # التأكد من وجود قيم الحصص - فقط 8 قيم
        if periods is None or len(periods) != 8:
            periods = [str(i) for i in range(1, 9)]  # القيم الافتراضية من "1" إلى "8"

        # افتراضياً، استخدم جميع الأيام إذا لم يتم تحديد أيام محددة
        if days is None:
            days = ["الأربعاء", "الثلاثاء", "الاثنين"]

        # تغيير حجم الصفحة إلى A4
        doc = SimpleDocTemplate(
            file_path,
            pagesize=A4,  # تغيير إلى A4 بدلاً من A3
            rightMargin=0.2 * cm,
            leftMargin=0.2 * cm,
            topMargin=0.1 * cm,
            bottomMargin=0.1 * cm
        )

        elements = []
        styles = getSampleStyleSheet()

        # إعداد الترويسة
        header_style = ParagraphStyle(
            'RightHeader',
            parent=styles['Normal'],
            fontName=self.arabic_font,
            fontSize=12,
            alignment=2,
            textColor=reportlab_colors.black,
            fontWeight='bold'
        )

        # إنشاء جدول لوضع الشعار والبيانات في نفس الصف
        from reportlab.platypus import Table, TableStyle

        # إعداد البيانات للجدول
        data = [['', '', '']]  # صف واحد مع ثلاثة أعمدة

        # ===== بداية إعدادات موضع السنة الدراسية (يمكن تعديلها) =====
        # حجم خط السنة الدراسية
        year_font_size = 14  # يمكن تغييره حسب الحاجة (مثلاً 12, 14, 16, 18)

        # موضع السنة الدراسية (يمين، وسط، يسار)
        # 0 = يسار، 1 = وسط، 2 = يمين
        year_alignment = 0  # 0 = يسار (تم تغييره من 2 = يمين)

        # المسافة العمودية بين السنة الدراسية وأعلى الصفحة
        year_top_margin = 30  # يمكن زيادتها لتحريك السنة الدراسية للأسفل

        # توزيع عرض الأعمدة (العمود الأيمن، العمود الأوسط، العمود الأيسر)
        # القيم الافتراضية هي 1/3 لكل عمود
        right_column_width = 0.30  # نسبة من العرض الكلي
        center_column_width = 0.40  # نسبة من العرض الكلي
        left_column_width = 0.30  # نسبة من العرض الكلي

        # المسافة الجانبية للسنة الدراسية (للتحكم في المسافة من اليمين أو اليسار)
        year_right_indent = 0  # للمحاذاة اليمنى: زيادة القيمة تزيد المسافة من اليمين
        year_left_indent = 10  # للمحاذاة اليسرى: زيادة القيمة تزيد المسافة من اليسار
        # ===== نهاية إعدادات موضع السنة الدراسية =====

        # إنشاء نمط جديد للسنة الدراسية بخط Calibri أزرق غامق
        year_style_blue = ParagraphStyle(
            'YearStyleBlue',
            parent=styles['Normal'],
            fontName=self.arabic_font,
            fontSize=year_font_size,  # استخدام القيمة المحددة أعلاه
            alignment=year_alignment,  # استخدام القيمة المحددة أعلاه
            leading=20,
            rightIndent=year_right_indent,  # استخدام القيمة المحددة أعلاه
            leftIndent=year_left_indent,  # استخدام القيمة المحددة أعلاه
            spaceAfter=0,
            spaceBefore=0,  # تم تعديله لأننا نستخدم Spacer بدلاً منه
            textColor=reportlab_colors.navy,  # لون أزرق غامق
            fontWeight='bold'
        )

        # إنشاء العمود الأيمن لاسم المؤسسة وعنوان القسم
        right_column = []

        # إضافة مسافة فارغة قبل النصوص للتحكم في المسافة العمودية
        if year_top_margin > 0:
            empty_style = ParagraphStyle(
                'EmptyStyle',
                parent=styles['Normal'],
                spaceBefore=0,
                spaceAfter=0,
                fontSize=1
            )
            right_column.append(Paragraph("&nbsp;", empty_style))
            right_column.append(Spacer(1, year_top_margin))

        # إنشاء نمط لاسم المؤسسة وعنوان القسم
        institution_style = ParagraphStyle(
            'InstitutionStyle',
            parent=styles['Normal'],
            fontName=self.arabic_font,
            fontSize=12,
            alignment=2,  # محاذاة يمنى
            textColor=reportlab_colors.navy,
            fontWeight='bold',
            spaceBefore=0,
            spaceAfter=5
        )

        # إضافة اسم المؤسسة
        institution_name = institution_data.get('institution', 'المؤسسة التعليمية')
        institution_text = self.arabic_text(institution_name)
        right_column.append(Paragraph(institution_text, institution_style))

        # إضافة عنوان القسم
        class_title = self.arabic_text(f"القسم: {class_name}")
        right_column.append(Paragraph(class_title, institution_style))

        # إضافة الشعار في العمود الأوسط
        center_column = []
        if 'ImagePath1' in institution_data and institution_data['ImagePath1'] and os.path.exists(institution_data['ImagePath1']):
            from reportlab.platypus import Image
            print(f"تم العثور على مسار الشعار: {institution_data['ImagePath1']}")
            try:
                logo = Image(institution_data['ImagePath1'])
                logo.drawWidth = 200  # عرض الشعار 200 نقطة
                logo.drawHeight = 70  # ارتفاع الشعار 70 نقطة
                center_column.append(logo)
                print("تم إضافة الشعار بنجاح")
            except Exception as e:
                print(f"خطأ في تحميل الشعار: {str(e)}")
                center_column.append(Paragraph("", styles['Normal']))
        else:
            print("لم يتم العثور على مسار الشعار أو الملف غير موجود")
            if 'ImagePath1' in institution_data:
                print(f"مسار الشعار: {institution_data['ImagePath1']}")
            else:
                print("مفتاح ImagePath1 غير موجود في بيانات المؤسسة")
            center_column.append(Paragraph("", styles['Normal']))

        # إنشاء العمود الثالث (الأيسر) للسنة الدراسية
        left_column = []

        # إضافة مسافة فارغة قبل السنة الدراسية للتحكم في المسافة العمودية
        if year_top_margin > 0:
            # إضافة فقرة فارغة للمسافة العمودية
            empty_style = ParagraphStyle(
                'EmptyStyle',
                parent=styles['Normal'],
                spaceBefore=0,
                spaceAfter=0,
                leading=1
            )
            left_column.append(Paragraph("&nbsp;", empty_style))
            # إضافة مسافة محددة
            left_column.append(Spacer(1, year_top_margin))

        year_text = self.arabic_text(f"السنة الدراسية : {institution_data['academic_year']}")
        left_column.append(Paragraph(year_text, year_style_blue))

        # إنشاء الجدول
        data = [[right_column, center_column, left_column]]  # العمود الثالث للسنة الدراسية

        # إنشاء الجدول - استخدام عرض A4 بدلاً من doc.width
        available_width = A4[0] - 0.4 * cm  # حساب العرض المتاح في الصفحة A4
        # استخدام القيم المحددة أعلاه لعرض الأعمدة
        header_table = Table(data, colWidths=[
            available_width * right_column_width,
            available_width * center_column_width,
            available_width * left_column_width
        ])

        # تنسيق الجدول
        header_table.setStyle(TableStyle([
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('ALIGN', (0, 0), (0, 0), 'RIGHT'),
            ('ALIGN', (1, 0), (1, 0), 'CENTER'),
            ('ALIGN', (2, 0), (2, 0), 'LEFT'),
            ('BACKGROUND', (0, 0), (-1, -1), reportlab_colors.white),
            ('BOX', (0, 0), (-1, -1), 0, reportlab_colors.white),
        ]))

        # إضافة الجدول إلى العناصر
        elements.append(header_table)
        elements.append(Spacer(1, 0.1*cm))

        # إنشاء مربع نص للمؤسسة مع ضمان أن النص يظهر داخل المربع
        # استخدام عمود "المؤسسة" من جدول بيانات_المؤسسة
        institution_name = institution_data.get('institution', 'المؤسسة التعليمية')
        institution_text = self.arabic_text(f"المؤسسة : {institution_name}")

        # تعديل إنشاء مربع النص للمؤسسة بحيث يظهر النص داخله
        institution_box = Table(
            [[institution_text]],
            colWidths=[300],  # العرض 300
            rowHeights=[25]   # الارتفاع 20
        )

        # تنسيق مربع النص بشكل يجعل النص يظهر داخله
        institution_box.setStyle(TableStyle([
    ('FONT', (0, 0), (0, 0), self.arabic_font, 16, 1),  # استخدام خط وحجم مناسب
    ('TEXTCOLOR', (0, 0), (0, 0), reportlab_colors.black),
    ('ALIGN', (0, 0), (0, 0), 'CENTER'),     # محاذاة أفقية وسط
    ('VALIGN', (0, 0), (0, 0), 'TOP'),        # رفع النص إلى الأعلى
    ('GRID', (0, 0), (0, 0), 2, reportlab_colors.black, None, (3, 3)),  # رسم الإطار بنمط متقطع
    ('LEFTPADDING', (0, 0), (0, 0), 5),
    ('RIGHTPADDING', (0, 0), (0, 0), 5),
    ('TOPPADDING', (0, 0), (0, 0), 2),
    ('BOTTOMPADDING', (0, 0), (0, 0), 2),      # تقليل التباعد السفلي لضمان أن النص في الأعلى
    ('LEADING', (0, 0), (0, 0), 1),
]))

        # إضافة مساحة فارغة متساوية قبل وبعد العنوان للتوسيط في الصفحة
        elements.append(Spacer(1, 0.1*cm))
        # إضافة مربع النص للمؤسسة مباشرة إلى elements
        elements.append(institution_box)

        # إضافة اسم القسم مباشرة تحت المؤسسة
        class_text = self.arabic_text(f"ورقة الغياب لقسم : {class_name}")

        # إنشاء مربع للقسم بنفس طريقة مربع المؤسسة
        class_table = Table(
            [[class_text]],
            colWidths=[300],  # نفس عرض مربع المؤسسة
            rowHeights=[25]   # نفس ارتفاع مربع المؤسسة
        )

        # تنسيق مربع القسم - تم إزالة الإطار
        class_table.setStyle(TableStyle([
            ('FONT', (0, 0), (0, 0), self.arabic_font, 14, 1),  # خط Calibri 14 غليظ
            ('TEXTCOLOR', (0, 0), (0, 0), reportlab_colors.black),        # لون النص أسود
            ('ALIGN', (0, 0), (0, 0), 'CENTER'),                # محاذاة أفقية وسط
            ('VALIGN', (0, 0), (0, 0), 'MIDDLE'),               # محاذاة عمودية وسط
            ('LEFTPADDING', (0, 0), (0, 0), 5),                 # تباعد داخلي متناسق
            ('RIGHTPADDING', (0, 0), (0, 0), 5),
            ('TOPPADDING', (0, 0), (0, 0), 2),
            ('BOTTOMPADDING', (0, 0), (0, 0), 3),
            ('LEADING', (0, 0), (0, 0), 5),
]))
            # تم إزالة خاصية BOX التي كانت تضيف إطاراً حول مربع القسم


        # إضافة مربع القسم إلى العناصر
        elements.append(class_table)

        # إضافة مسافة قبل بدء الجدول الرئيسي
        elements.append(Spacer(1, 0.1 * cm))

        # إعداد الجدول
        # تجهيز التواريخ للأيام المحددة
        week_dates = {}
        full_days = ["السبت", "الجمعة", "الخميس", "الأربعاء", "الثلاثاء", "الاثنين"]

        for day in full_days:
            day_index = full_days.index(day)
            current_date = start_date + datetime.timedelta(days=5 - day_index)
            week_dates[day] = self.arabic_text(current_date.strftime("%Y-%m-%d"))

        # إعداد الأيام المختارة والتواريخ المقابلة
        selected_days_ar = [self.arabic_text(day) for day in days]
        selected_dates = [week_dates[day] for day in days]

        # إنشاء بيانات الجدول
        data = []

        # صف الأيام - نبدأ مباشرة بهذا الصف (حذف صف "تلاميذ يوجهون الى الإدارة")
        day_row = []
        for day in selected_days_ar:
            day_row.append(day)
            day_row.extend([""] * 7)  # خلايا فارغة للامتداد
        day_row.append(self.arabic_text("الاسم والنسب"))
        day_row.append(self.arabic_text("رت"))
        data.append(day_row)

        # صف التواريخ
        date_row = []
        for date in selected_dates:
            date_row.append(date)
            date_row.extend([""] * 7)  # خلايا فارغة للامتداد
        date_row.append("")
        date_row.append("")
        data.append(date_row)

        # صف الحصص
        period_row = []
        for _ in range(len(days)):
            period_row.extend(periods)
        period_row.append("")
        period_row.append("")
        data.append(period_row)

        # صفوف الطلاب
        for i, student in enumerate(students):
            student_row = []
            # طباعة تشخيص للتلاميذ الأوائل
            if i < 3:
                print(f"=== تشخيص التلميذ {i+1} في split_attendance_report.py ===")
                print(f"البيانات الكاملة: {student}")
                print(f"student.get('rt'): {student.get('rt', 'غير موجود')}")
                print(f"student.get('id'): {student.get('id', 'غير موجود')}")

            # الحصول على رقم الترتيب الفعلي من جدول اللوائح
            rt_value = student.get("rt", "") or student.get("id", "")

            # طباعة تشخيص للتلاميذ الأوائل
            if i < 3:
                print(f"rt_value النهائية: {rt_value}")
                print("=" * 50)

            # إزالة علامة X إذا كانت موجودة لعرض الرقم الأصلي فقط
            if str(rt_value).endswith(" X"):
                rt_display = str(rt_value)[:-2].strip()
            elif str(rt_value) == "X":
                rt_display = ""
            else:
                rt_display = str(rt_value)

            # إضافة خلايا لكل يوم من الأيام المختارة
            for _ in range(len(days)):
                student_row.append(rt_display)  # رقم الترتيب الفعلي من جدول اللوائح
                student_row.extend([""] * 7)  # 7 خلايا فارغة للحصص

            # إضافة معلومات الطالب
            student_row.append(self.arabic_text(student["name"]))
            student_row.append(rt_display)  # رقم الترتيب الفعلي في العمود الأخير
            data.append(student_row)

        # إضافة صفوف فارغة (حد أقصى 7 صفوف بشرط عدم تجاوز 50 صفاً إجمالياً)
        current_rows = len(data)  # عدد الصفوف الحالية (رؤوس + طلاب)
        max_total_rows = 50
        max_empty_rows = 7

        # حساب عدد الصفوف الفارغة المطلوبة
        empty_rows_needed = min(max_empty_rows, max_total_rows - current_rows - 1)  # -1 لصف توقيع الأساتذة

        if empty_rows_needed > 0:
            for _ in range(empty_rows_needed):
                empty_row = []
                # إضافة خلايا فارغة لكل يوم
                for _ in range(len(days)):
                    empty_row.append("")  # خلية فارغة لرقم الترتيب
                    empty_row.extend([""] * 7)  # 7 خلايا فارغة للحصص

                # إضافة خلايا فارغة لمعلومات الطالب
                empty_row.append("")  # الاسم والنسب
                empty_row.append("")  # رت
                data.append(empty_row)

        # إضافة صف "توقيع الأساتذة"
        footer_row = []
        for _ in range(len(days) * 8):
            footer_row.append("")  # خلايا فارغة للأيام
        footer_row.append(self.arabic_text("توقيع الأساتذة"))  # نص أسفل الاسم والنسب
        footer_row.append("")  # خلية فارغة أسفل رت
        data.append(footer_row)

        # إعداد عرض الأعمدة
        available_width = A4[0] - 0.4 * cm  # حساب العرض المتاح في الصفحة A4
        attendance_width = (available_width - 5.2 * cm) / (len(days) * 8)
        col_widths = [attendance_width] * (len(days) * 8)
        col_widths.append(3.8 * cm)  # تقليل عرض الاسم والنسب من 4 إلى 3.8 سم
        col_widths.append(1.2 * cm)  # رت

        # تحديد ارتفاع الصفوف - تعديل ارتفاع صفوف الطلاب وصف التوقيع
        row_heights = [20, 20, 20]  # صفوف الرؤوس بارتفاع 20 نقطة

        # حساب العدد الإجمالي للصفوف (طلاب + صفوف فارغة)
        total_student_rows = len(students) + (empty_rows_needed if empty_rows_needed > 0 else 0)

        # تحديد ارتفاع الصف حسب العدد الإجمالي
        if total_student_rows >= 45:
            student_row_height = 12
        elif total_student_rows >= 35:
            student_row_height = 13
        else:
            student_row_height = 14

        # إضافة ارتفاعات صفوف الطلاب
        for _ in range(len(students)):
            row_heights.append(student_row_height)

        # إضافة ارتفاعات الصفوف الفارغة
        if empty_rows_needed > 0:
            for _ in range(empty_rows_needed):
                row_heights.append(student_row_height)

        row_heights.append(50)  # تقليل ارتفاع الصف الأخير (توقيع الأساتذة) إلى 50 نقطة

        # إنشاء الجدول
        table = Table(data, colWidths=col_widths, rowHeights=row_heights)

        # تنسيق الجدول
        table_style = TableStyle([
            ('FONT', (0, 0), (-1, -1), self.arabic_font, 11),  # تصغير الخط من 12 إلى 11
            ('GRID', (0, 0), (-1, -1), 1, reportlab_colors.black),  # إطار لكل الخلايا - سماكة 1 نقطة
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),  # محاذاة مركزية
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),  # محاذاة عمودية وسط
            ('BACKGROUND', (0, 0), (-1, 2), reportlab_colors.lightgrey),  # خلفية رمادية فاتحة لصفوف الرؤوس
            ('FONTSIZE', (0, 0), (-1, 2), 13),  # زيادة حجم الخط للأيام والتواريخ
            ('SPAN', (-2, 0), (-2, 2)),  # امتداد خلية الاسم والنسب
            ('SPAN', (-1, 0), (-1, 2)),  # امتداد خلية رت
            ('SPAN', (-2, -1), (-2, -1)),  # امتداد "توقيع الأساتذة"
            ('FONT', (-2, 3), (-2, -2), self.arabic_font, 10),  # تكبير خط أسماء التلاميذ إلى 10 أسود غامق
        ])

        # إضافة امتداد للأيام والتواريخ
        for i in range(len(days)):
            col_start = i * 8
            col_end = col_start + 7
            table_style.add('SPAN', (col_start, 0), (col_end, 0))  # امتداد اليوم
            table_style.add('SPAN', (col_start, 1), (col_end, 1))  # امتداد التاريخ

        # إضافة تنسيق خاص لرقم الترتيب في بداية كل يوم (للطلاب والصفوف الفارغة)
        total_student_rows = len(students) + (empty_rows_needed if empty_rows_needed > 0 else 0)
        for row_index in range(3, 3 + total_student_rows):
            for day_index in range(len(days)):
                col_index = day_index * 8
                table_style.add('BACKGROUND', (col_index, row_index), (col_index, row_index), reportlab_colors.lightgrey)
                table_style.add('FONT', (col_index, row_index), (col_index, row_index), self.arabic_font, 7)  # تصغير الخط إلى 7

        # تطبيق التنسيقات
        table.setStyle(table_style)
        elements.append(table)

        # إضافة ملاحظة أسفل الجدول
        note_style = ParagraphStyle('Note', fontName=self.arabic_font, fontSize=11, alignment=1)  # تقليل حجم الخط من 12 إلى 11
        note_text = self.arabic_text("المرجو من السادة الأساتذة عدم قبول أي تلميذ(ة) غير مسجل(ة) في اللائحة ولا يتوفر على ورقة السماح بالدخول")
        elements.append(Spacer(1, 0.3 * cm))  # تقليل المساحة من 0.5 إلى 0.3 سم
        elements.append(Paragraph(note_text, note_style))

        # إضافة معالجة الأخطاء لعملية بناء PDF
        try:
            # إنشاء ملف PDF
            doc.build(elements)
            return True
        except Exception as e:
            print(f"خطأ أثناء إنشاء ملف PDF: {str(e)}")
            return False

    # تحسين التوثيق للدوال
    def generate_first_half(self, file_path, students, class_name, institution_data, start_date, report_type, periods=None):
        """
        توليد تقرير لأيام النصف الأول من الأسبوع (الاثنين إلى الأربعاء)

        المعلمات:
            file_path (str): مسار حفظ الملف
            students (list): قائمة الطلاب
            class_name (str): اسم القسم
            institution_data (dict): بيانات المؤسسة
            start_date (datetime): تاريخ بداية الأسبوع
            report_type (int): نوع التقرير
            periods (list): قائمة الحصص

        العودة:
            bool: True إذا نجحت العملية، False إذا فشلت
        """
        return self.generate_pdf(
            file_path=file_path,
            students=students,
            class_name=class_name,
            institution_data=institution_data,
            start_date=start_date,
            report_type=report_type,
            periods=periods,
            days=["الأربعاء", "الثلاثاء", "الاثنين"]
        )

    def generate_second_half(self, file_path, students, class_name, institution_data, start_date, report_type, periods=None):
        """
        توليد تقرير لأيام النصف الثاني من الأسبوع (الخميس إلى السبت)

        المعلمات:
            file_path (str): مسار حفظ الملف
            students (list): قائمة الطلاب
            class_name (str): اسم القسم
            institution_data (dict): بيانات المؤسسة
            start_date (datetime): تاريخ بداية الأسبوع
            report_type (int): نوع التقرير
            periods (list): قائمة الحصص

        العودة:
            bool: True إذا نجحت العملية، False إذا فشلت
        """
        return self.generate_pdf(
            file_path=file_path,
            students=students,
            class_name=class_name,
            institution_data=institution_data,
            start_date=start_date,
            report_type=report_type,
            periods=periods,
            days=["السبت", "الجمعة", "الخميس"]
        )

    def generate_first_half_multi_class(self, file_path, class_data_list, institution_data, start_date, report_type, periods=None):
        """
        توليد تقرير لأيام النصف الأول من الأسبوع (الاثنين إلى الأربعاء) لعدة أقسام في ملف واحد

        المعلمات:
            file_path (str): مسار حفظ الملف
            class_data_list (list): قائمة الأقسام والطلاب (كل عنصر هو قاموس يحتوي على 'class_name' و 'students')
            institution_data (dict): بيانات المؤسسة
            start_date (datetime): تاريخ بداية الأسبوع
            report_type (int): نوع التقرير
            periods (list): قائمة الحصص

        العودة:
            bool: True إذا نجحت العملية، False إذا فشلت
        """
        try:
            from reportlab.platypus import PageBreak

            # التأكد من وجود قيم الحصص - فقط 8 قيم
            if periods is None or len(periods) != 8:
                periods = [str(i) for i in range(1, 9)]  # القيم الافتراضية من "1" إلى "8"

            # تحديد أيام النصف الأول من الأسبوع
            days = ["الأربعاء", "الثلاثاء", "الاثنين"]

            # تغيير حجم الصفحة إلى A4
            doc = SimpleDocTemplate(
                file_path,
                pagesize=A4,
                rightMargin=0.2 * cm,
                leftMargin=0.2 * cm,
                topMargin=0.1 * cm,
                bottomMargin=0.1 * cm
            )

            elements = []

            # إنشاء تقرير لكل قسم
            for i, class_data in enumerate(class_data_list):
                class_name = class_data['class_name']
                students = class_data['students']

                # الحصول على عناصر التقرير للقسم الحالي
                class_elements = self._get_report_elements(
                    students=students,
                    class_name=class_name,
                    institution_data=institution_data,
                    start_date=start_date,
                    days=days,
                    periods=periods,
                    report_type=report_type
                )

                # إضافة عناصر التقرير الحالي
                elements.extend(class_elements)

                # إضافة فاصل صفحة إلا إذا كان هذا القسم الأخير
                if i < len(class_data_list) - 1:
                    elements.append(PageBreak())

            # إنشاء ملف PDF
            doc.build(elements)
            return True

        except Exception as e:
            print(f"خطأ أثناء إنشاء ملف PDF متعدد الأقسام: {str(e)}")
            return False

    def generate_second_half_multi_class(self, file_path, class_data_list, institution_data, start_date, report_type, periods=None):
        """
        توليد تقرير لأيام النصف الثاني من الأسبوع (الخميس إلى السبت) لعدة أقسام في ملف واحد

        المعلمات:
            file_path (str): مسار حفظ الملف
            class_data_list (list): قائمة الأقسام والطلاب (كل عنصر هو قاموس يحتوي على 'class_name' و 'students')
            institution_data (dict): بيانات المؤسسة
            start_date (datetime): تاريخ بداية الأسبوع
            report_type (int): نوع التقرير
            periods (list): قائمة الحصص

        العودة:
            bool: True إذا نجحت العملية، False إذا فشلت
        """
        try:
            from reportlab.platypus import PageBreak

            # التأكد من وجود قيم الحصص - فقط 8 قيم
            if periods is None or len(periods) != 8:
                periods = [str(i) for i in range(1, 9)]  # القيم الافتراضية من "1" إلى "8"

            # تحديد أيام النصف الثاني من الأسبوع
            days = ["السبت", "الجمعة", "الخميس"]

            # تغيير حجم الصفحة إلى A4
            doc = SimpleDocTemplate(
                file_path,
                pagesize=A4,
                rightMargin=0.2 * cm,
                leftMargin=0.2 * cm,
                topMargin=0.1 * cm,
                bottomMargin=0.1 * cm
            )

            elements = []

            # إنشاء تقرير لكل قسم
            for i, class_data in enumerate(class_data_list):
                class_name = class_data['class_name']
                students = class_data['students']

                # الحصول على عناصر التقرير للقسم الحالي
                class_elements = self._get_report_elements(
                    students=students,
                    class_name=class_name,
                    institution_data=institution_data,
                    start_date=start_date,
                    days=days,
                    periods=periods,
                    report_type=report_type
                )

                # إضافة عناصر التقرير الحالي
                elements.extend(class_elements)

                # إضافة فاصل صفحة إلا إذا كان هذا القسم الأخير
                if i < len(class_data_list) - 1:
                    elements.append(PageBreak())

            # إنشاء ملف PDF
            doc.build(elements)
            return True

        except Exception as e:
            print(f"خطأ أثناء إنشاء ملف PDF متعدد الأقسام: {str(e)}")
            return False

    def _get_report_elements(self, students, class_name, institution_data, start_date, days, periods, report_type=1):
        """
        إنشاء عناصر التقرير لقسم واحد

        المعلمات:
            students (list): قائمة الطلاب
            class_name (str): اسم القسم
            institution_data (dict): بيانات المؤسسة
            start_date (datetime): تاريخ بداية الأسبوع
            days (list): قائمة الأيام المطلوبة للتقرير
            periods (list): قائمة الحصص
            report_type (int): نوع التقرير (0: أسبوعي، 1: نصف أسبوعي)

        العودة:
            list: قائمة عناصر التقرير للقسم
        """
        # فلترة التلاميذ - إزالة التلاميذ الذين لديهم علامة X في عمود رت
        filtered_students = []
        hidden_count = 0

        print(f"=== تشخيص فلترة التلاميذ في {class_name} (_get_report_elements) ===")
        print(f"عدد التلاميذ قبل الفلترة: {len(students)}")

        for i, student in enumerate(students):
            # التحقق من وجود علامة X في عمود رت
            rt_value = student.get('rt', '') or student.get('id', '') or ''
            rt_value = str(rt_value).strip()

            # طباعة تشخيص للتلاميذ الأوائل والأواخر
            if i < 3 or i >= len(students) - 3:
                print(f"التلميذ {i+1}: {student.get('name', 'بدون اسم')}, rt='{rt_value}'")

            # التحقق الذكي: هل التلميذ مخفي من لائحة الغياب؟
            is_hidden = self._is_student_hidden_from_attendance(rt_value)

            if is_hidden:
                hidden_count += 1
                print(f"تم إخفاء التلميذ: {student.get('name', 'بدون اسم')}, rt='{rt_value}'")
            else:
                filtered_students.append(student)

        print(f"عدد التلاميذ المخفيين: {hidden_count}")
        print(f"عدد التلاميذ بعد الفلترة: {len(filtered_students)}")
        print("=" * 50)

        # استخدام القائمة المفلترة بدلاً من القائمة الأصلية
        students = filtered_students

        elements = []
        styles = getSampleStyleSheet()

        # لا نحتاج إلى تعريف header_style هنا لأننا نعرفه في الشروط حسب نوع التقرير

        # إنشاء جدول لوضع الشعار والسنة الدراسية في نفس الصف
        from reportlab.platypus import Table, TableStyle, Image

        # ===== بداية إعدادات موضع السنة الدراسية (يمكن تعديلها) =====
        # حجم خط السنة الدراسية
        year_font_size = 14  # يمكن تغييره حسب الحاجة (مثلاً 12, 14, 16, 18)

        # موضع السنة الدراسية (يمين، وسط، يسار)
        # 0 = يسار، 1 = وسط، 2 = يمين
        year_alignment = 0  # 0 = يسار (تم تغييره من 2 = يمين)

        # المسافة العمودية بين السنة الدراسية وأعلى الصفحة
        year_top_margin = 30  # يمكن زيادتها لتحريك السنة الدراسية للأسفل

        # توزيع عرض الأعمدة (العمود الأيمن، العمود الأوسط، العمود الأيسر)
        # القيم الافتراضية هي 1/3 لكل عمود
        right_column_width = 0.30  # نسبة من العرض الكلي
        center_column_width = 0.40  # نسبة من العرض الكلي
        left_column_width = 0.30  # نسبة من العرض الكلي

        # المسافة الجانبية للسنة الدراسية (للتحكم في المسافة من اليمين أو اليسار)
        year_right_indent = 0  # للمحاذاة اليمنى: زيادة القيمة تزيد المسافة من اليمين
        year_left_indent = 10  # للمحاذاة اليسرى: زيادة القيمة تزيد المسافة من اليسار
        # ===== نهاية إعدادات موضع السنة الدراسية =====

        # إنشاء نمط جديد للسنة الدراسية بخط Calibri أزرق غامق
        year_style_blue = ParagraphStyle(
            'YearStyleBlue',
            parent=styles['Normal'],
            fontName=self.arabic_font,
            fontSize=year_font_size,  # استخدام القيمة المحددة أعلاه
            alignment=year_alignment,  # استخدام القيمة المحددة أعلاه
            leading=20,
            rightIndent=year_right_indent,  # استخدام القيمة المحددة أعلاه
            leftIndent=year_left_indent,  # استخدام القيمة المحددة أعلاه
            spaceAfter=0,
            spaceBefore=0,  # تم تعديله لأننا نستخدم Spacer بدلاً منه
            textColor=reportlab_colors.navy,  # لون أزرق غامق
            fontWeight='bold'
        )

        # إنشاء العمود الأيمن لاسم المؤسسة وعنوان القسم
        right_column = []

        # إضافة مسافة فارغة قبل النصوص للتحكم في المسافة العمودية
        if year_top_margin > 0:
            empty_style = ParagraphStyle(
                'EmptyStyle',
                parent=styles['Normal'],
                spaceBefore=0,
                spaceAfter=0,
                fontSize=1
            )
            right_column.append(Paragraph("&nbsp;", empty_style))
            right_column.append(Spacer(1, year_top_margin))

        # إنشاء نمط لاسم المؤسسة وعنوان القسم
        institution_style = ParagraphStyle(
            'InstitutionStyle',
            parent=styles['Normal'],
            fontName=self.arabic_font,
            fontSize=12,
            alignment=2,  # محاذاة يمنى
            textColor=reportlab_colors.navy,
            fontWeight='bold',
            spaceBefore=0,
            spaceAfter=5
        )

        # إضافة اسم المؤسسة
        institution_name = institution_data.get('institution', 'المؤسسة التعليمية')
        institution_text = self.arabic_text(institution_name)
        right_column.append(Paragraph(institution_text, institution_style))

        # إضافة عنوان القسم
        class_title = self.arabic_text(f"القسم: {class_name}")
        right_column.append(Paragraph(class_title, institution_style))

        # إنشاء العمود الأوسط للشعار
        center_column = []
        if 'ImagePath1' in institution_data and institution_data['ImagePath1'] and os.path.exists(institution_data['ImagePath1']):
            try:
                logo = Image(institution_data['ImagePath1'])
                logo.drawWidth = 200  # عرض الشعار 200 نقطة
                logo.drawHeight = 70  # ارتفاع الشعار 70 نقطة
                center_column.append(logo)
                print("تم إضافة الشعار بنجاح")
            except Exception as e:
                print(f"خطأ في تحميل الشعار: {str(e)}")
                center_column.append(Paragraph("", styles['Normal']))
        else:
            print("لم يتم العثور على مسار الشعار أو الملف غير موجود")
            center_column.append(Paragraph("", styles['Normal']))

        # إنشاء العمود الأيسر للسنة الدراسية
        left_column = []

        # إضافة مسافة فارغة قبل السنة الدراسية للتحكم في المسافة العمودية
        if year_top_margin > 0:
            # إضافة فقرة فارغة للمسافة العمودية
            empty_style = ParagraphStyle(
                'EmptyStyle',
                parent=styles['Normal'],
                spaceBefore=0,
                spaceAfter=0,
                leading=1
            )
            left_column.append(Paragraph("&nbsp;", empty_style))
            # إضافة مسافة محددة
            left_column.append(Spacer(1, year_top_margin))

        year_text = self.arabic_text(f"السنة الدراسية : {institution_data['academic_year']}")
        left_column.append(Paragraph(year_text, year_style_blue))

        # إنشاء الجدول
        data = [[right_column, center_column, left_column]]

        # إنشاء الجدول - استخدام عرض A4
        available_width = A4[0] - 0.4 * cm
        # استخدام القيم المحددة أعلاه لعرض الأعمدة
        header_table = Table(data, colWidths=[
            available_width * right_column_width,
            available_width * center_column_width,
            available_width * left_column_width
        ])

        # تنسيق الجدول
        header_table.setStyle(TableStyle([
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('ALIGN', (0, 0), (0, 0), 'RIGHT'),
            ('ALIGN', (1, 0), (1, 0), 'CENTER'),
            ('ALIGN', (2, 0), (2, 0), 'LEFT'),
            ('BACKGROUND', (0, 0), (-1, -1), reportlab_colors.white),
            ('BOX', (0, 0), (-1, -1), 0, reportlab_colors.white),
        ]))

        # إضافة الجدول إلى العناصر
        elements.append(header_table)

        elements.append(Spacer(1, 0.1*cm))

        # إنشاء مربع نص للمؤسسة مع ضمان أن النص يظهر داخل المربع
        # استخدام عمود "المؤسسة" من جدول بيانات_المؤسسة
        institution_name = institution_data.get('institution', 'المؤسسة التعليمية')
        institution_text = self.arabic_text(f"المؤسسة : {institution_name}")

        # تعديل إنشاء مربع النص للمؤسسة بحيث يظهر النص داخله
        institution_box = Table(
            [[institution_text]],
            colWidths=[300],  # العرض 300
            rowHeights=[25]   # الارتفاع 25
        )

        # تنسيق مربع النص بشكل يجعل النص يظهر داخله
        institution_box.setStyle(TableStyle([
            ('FONT', (0, 0), (0, 0), self.arabic_font, 16, 1),  # استخدام خط وحجم مناسب
            ('TEXTCOLOR', (0, 0), (0, 0), reportlab_colors.black),
            ('ALIGN', (0, 0), (0, 0), 'CENTER'),     # محاذاة أفقية وسط
            ('VALIGN', (0, 0), (0, 0), 'TOP'),        # رفع النص إلى الأعلى
            ('GRID', (0, 0), (0, 0), 2, reportlab_colors.black, None, (3, 3)),  # رسم الإطار بنمط متقطع
            ('LEFTPADDING', (0, 0), (0, 0), 5),
            ('RIGHTPADDING', (0, 0), (0, 0), 5),
            ('TOPPADDING', (0, 0), (0, 0), 2),
            ('BOTTOMPADDING', (0, 0), (0, 0), 2),      # تقليل التباعد السفلي
            ('LEADING', (0, 0), (0, 0), 1),
        ]))

        # إضافة مساحة فارغة متساوية قبل وبعد العنوان للتوسيط في الصفحة
        elements.append(Spacer(1, 0.1*cm))
        # إضافة مربع النص للمؤسسة
        elements.append(institution_box)

        # إضافة اسم القسم مباشرة تحت المؤسسة
        class_text = self.arabic_text(f"ورقة الغياب لقسم : {class_name}")

        # إنشاء مربع للقسم بنفس طريقة مربع المؤسسة
        class_table = Table(
            [[class_text]],
            colWidths=[300],  # نفس عرض مربع المؤسسة
            rowHeights=[25]   # نفس ارتفاع مربع المؤسسة
        )

        # تنسيق مربع القسم - تم إزالة الإطار
        class_table.setStyle(TableStyle([
            ('FONT', (0, 0), (0, 0), self.arabic_font, 14, 1),  # خط Calibri 14 غليظ
            ('TEXTCOLOR', (0, 0), (0, 0), reportlab_colors.black),        # لون النص أسود
            ('ALIGN', (0, 0), (0, 0), 'CENTER'),                # محاذاة أفقية وسط
            ('VALIGN', (0, 0), (0, 0), 'MIDDLE'),               # محاذاة عمودية وسط
            ('LEFTPADDING', (0, 0), (0, 0), 5),                 # تباعد داخلي متناسق
            ('RIGHTPADDING', (0, 0), (0, 0), 5),
            ('TOPPADDING', (0, 0), (0, 0), 2),
            ('BOTTOMPADDING', (0, 0), (0, 0), 3),
            ('LEADING', (0, 0), (0, 0), 5),
        ]))

        # إضافة مربع القسم إلى العناصر
        elements.append(class_table)

        # إضافة مسافة قبل بدء الجدول الرئيسي
        elements.append(Spacer(1, 0.1 * cm))

        # إعداد الجدول
        # تجهيز التواريخ للأيام المحددة
        week_dates = {}
        full_days = ["السبت", "الجمعة", "الخميس", "الأربعاء", "الثلاثاء", "الاثنين"]

        for day in full_days:
            day_index = full_days.index(day)
            current_date = start_date + datetime.timedelta(days=5 - day_index)
            week_dates[day] = self.arabic_text(current_date.strftime("%Y-%m-%d"))

        # إعداد الأيام المختارة والتواريخ المقابلة
        selected_days_ar = [self.arabic_text(day) for day in days]
        selected_dates = [week_dates[day] for day in days]

        # إنشاء بيانات الجدول
        data = []

        # صف الأيام
        day_row = []
        for day in selected_days_ar:
            day_row.append(day)
            day_row.extend([""] * 7)  # خلايا فارغة للامتداد
        day_row.append(self.arabic_text("الاسم والنسب"))
        day_row.append(self.arabic_text("رت"))
        data.append(day_row)

        # صف التواريخ
        date_row = []
        for date in selected_dates:
            date_row.append(date)
            date_row.extend([""] * 7)  # خلايا فارغة للامتداد
        date_row.append("")
        date_row.append("")
        data.append(date_row)

        # صف الحصص
        period_row = []
        for _ in range(len(days)):
            period_row.extend(periods)
        period_row.append("")
        period_row.append("")
        data.append(period_row)

        # صفوف الطلاب
        for student in students:
            student_row = []
            # الحصول على رقم الترتيب الفعلي من جدول اللوائح
            rt_value = student.get("rt", "") or student.get("id", "")
            # إزالة علامة X إذا كانت موجودة لعرض الرقم الأصلي فقط
            if str(rt_value).endswith(" X"):
                rt_display = str(rt_value)[:-2].strip()
            elif str(rt_value) == "X":
                rt_display = ""
            else:
                rt_display = str(rt_value)

            # إضافة خلايا لكل يوم من الأيام المختارة
            for _ in range(len(days)):
                student_row.append(rt_display)  # رقم الترتيب الفعلي من جدول اللوائح
                student_row.extend([""] * 7)  # 7 خلايا فارغة للحصص

            # إضافة معلومات الطالب
            student_row.append(self.arabic_text(student["name"]))
            student_row.append(rt_display)  # رقم الترتيب الفعلي في العمود الأخير
            data.append(student_row)

        # إضافة صفوف فارغة (حد أقصى 7 صفوف بشرط عدم تجاوز 50 صفاً إجمالياً)
        current_rows = len(data)  # عدد الصفوف الحالية (رؤوس + طلاب)
        max_total_rows = 50
        max_empty_rows = 7

        # حساب عدد الصفوف الفارغة المطلوبة
        empty_rows_needed = min(max_empty_rows, max_total_rows - current_rows - 1)  # -1 لصف توقيع الأساتذة

        if empty_rows_needed > 0:
            for _ in range(empty_rows_needed):
                empty_row = []
                # إضافة خلايا فارغة لكل يوم
                for _ in range(len(days)):
                    empty_row.append("")  # خلية فارغة لرقم الترتيب
                    empty_row.extend([""] * 7)  # 7 خلايا فارغة للحصص

                # إضافة خلايا فارغة لمعلومات الطالب
                empty_row.append("")  # الاسم والنسب
                empty_row.append("")  # رت
                data.append(empty_row)

        # إضافة صف "توقيع الأساتذة"
        footer_row = []
        for _ in range(len(days) * 8):
            footer_row.append("")  # خلايا فارغة للأيام
        footer_row.append(self.arabic_text("توقيع الأساتذة"))  # نص أسفل الاسم والنسب
        footer_row.append("")  # خلية فارغة أسفل رت
        data.append(footer_row)

        # إعداد عرض الأعمدة
        available_width = A4[0] - 0.4 * cm  # حساب العرض المتاح في الصفحة A4
        attendance_width = (available_width - 5.2 * cm) / (len(days) * 8)
        col_widths = [attendance_width] * (len(days) * 8)
        col_widths.append(3.8 * cm)  # عرض الاسم والنسب
        col_widths.append(1.2 * cm)  # رت

        # تحديد ارتفاع الصفوف
        row_heights = [20, 20, 20]  # صفوف الرؤوس بارتفاع 20 نقطة

        # حساب العدد الإجمالي للصفوف (طلاب + صفوف فارغة)
        total_student_rows = len(students) + (empty_rows_needed if empty_rows_needed > 0 else 0)

        # تحديد ارتفاع الصف حسب العدد الإجمالي
        if total_student_rows >= 45:
            student_row_height = 12
        elif total_student_rows >= 35:
            student_row_height = 13
        else:
            student_row_height = 14

        # إضافة ارتفاعات صفوف الطلاب
        for _ in range(len(students)):
            row_heights.append(student_row_height)

        # إضافة ارتفاعات الصفوف الفارغة
        if empty_rows_needed > 0:
            for _ in range(empty_rows_needed):
                row_heights.append(student_row_height)

        row_heights.append(50)  # ارتفاع الصف الأخير (توقيع الأساتذة)

        # إنشاء الجدول
        table = Table(data, colWidths=col_widths, rowHeights=row_heights)

        # تنسيق الجدول
        table_style = TableStyle([
            ('FONT', (0, 0), (-1, -1), self.arabic_font, 11),
            ('GRID', (0, 0), (-1, -1), 1, reportlab_colors.black),  # إطار لكل الخلايا - سماكة 1 نقطة
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),  # محاذاة مركزية
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),  # محاذاة عمودية وسط
            ('BACKGROUND', (0, 0), (-1, 2), reportlab_colors.lightgrey),  # خلفية رمادية فاتحة لصفوف الرؤوس
            ('FONTSIZE', (0, 0), (-1, 2), 13),  # زيادة حجم الخط للأيام والتواريخ
            ('SPAN', (-2, 0), (-2, 2)),  # امتداد خلية الاسم والنسب
            ('SPAN', (-1, 0), (-1, 2)),  # امتداد خلية رت
            ('SPAN', (-2, -1), (-2, -1)),  # امتداد "توقيع الأساتذة"
            ('FONT', (-2, 3), (-2, -2), self.arabic_font, 10),  # تكبير خط أسماء التلاميذ إلى 10 أسود غامق
        ])

        # إضافة امتداد للأيام والتواريخ
        for i in range(len(days)):
            col_start = i * 8
            col_end = col_start + 7
            table_style.add('SPAN', (col_start, 0), (col_end, 0))  # امتداد اليوم
            table_style.add('SPAN', (col_start, 1), (col_end, 1))  # امتداد التاريخ

        # إضافة تنسيق خاص لرقم الترتيب في بداية كل يوم (للطلاب والصفوف الفارغة)
        total_student_rows = len(students) + (empty_rows_needed if empty_rows_needed > 0 else 0)
        for row_index in range(3, 3 + total_student_rows):
            for day_index in range(len(days)):
                col_index = day_index * 8
                table_style.add('BACKGROUND', (col_index, row_index), (col_index, row_index), reportlab_colors.lightgrey)
                table_style.add('FONT', (col_index, row_index), (col_index, row_index), self.arabic_font, 7)

        # تطبيق التنسيقات
        table.setStyle(table_style)
        elements.append(table)

        # إضافة ملاحظة أسفل الجدول
        note_style = ParagraphStyle('Note', fontName=self.arabic_font, fontSize=11, alignment=1)
        note_text = self.arabic_text("المرجو من السادة الأساتذة عدم قبول أي تلميذ(ة) غير مسجل(ة) في اللائحة ولا يتوفر على ورقة السماح بالدخول")
        elements.append(Spacer(1, 0.3 * cm))
        elements.append(Paragraph(note_text, note_style))

        return elements

# كود الاختبار (يتم تنفيذه فقط عند تشغيل الملف مباشرة)
if __name__ == "__main__":
    # بيانات وهمية للتجربة
    students = [
        {"id": 1, "name": "العاقل أمينة"},
        {"id": 2, "name": "الحجي أنوار"},
        {"id": 3, "name": "الهوان إسماعيل"},
    ]

    class_name = "TCS-1"
    institution_data = {
        "academy": "الأكاديمية الجهوية الرباط",
        "directorate": "المديرية الإقليمية سلا",
        "institution": "الثانوية التأهيلية عبد المؤمن الموحدي",
        "academic_year": "2023/2024"
    }

    start_date = datetime.datetime(2025, 4, 7)

    report = SplitAttendanceReport()

    # التقرير الأول: الاثنين إلى الأربعاء
    report.generate_first_half(
        file_path="تقرير_الغياب_الاثنين_الأربعاء.pdf",
        students=students,
        class_name=class_name,
        institution_data=institution_data,
        start_date=start_date,
        report_type=1
    )

    # التقرير الثاني: الخميس إلى السبت
    report.generate_second_half(
        file_path="تقرير_الغياب_الخميس_السبت.pdf",
        students=students,
        class_name=class_name,
        institution_data=institution_data,
        start_date=start_date,
        report_type=1
    )