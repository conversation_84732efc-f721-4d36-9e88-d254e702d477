# -*- mode: python ; coding: utf-8 -*-

block_cipher = None
datas=[
    # ملفات التطبيق الأساسية في المجلد الرئيسي
    # نظام قاعدة البيانات الجديد: database_config.py + مجلد date2025/
    # هذا النظام يحل مشكلة التضارب بين النسخة المجمعة والنسخة العادية
    ('01.ico', '.'),
    # نافذة حوار إنشاء الاختصار
    ('shortcut_dialog.py', '.'),
    # ملفات النوافذ الرئيسية
    ('main_window.py', '.'),
    ('app.py', '.'),
    ('sub01_window.py', '.'),
    ('sub1_window_html.py', '.'),
    ('sub2_window_html.py', '.'),
    ('sub3_window_html.py', '.'),
    ('sub4_window.py', '.'),
    ('sub5_window_html.py', '.'),
    ('sub6_window_html.py', '.'),
    ('sub7_window_html.py', '.'),
    ('sub8_window.py', '.'),
    ('sub9_window.py', '.'),
    ('sub9_window__html.py', '.'),
    ('sub10_window.py', '.'),
    ('sub10_window__html.py', '.'),
    ('sub11_window_html.py', '.'),
    ('sub12_window_html.py', '.'),
    ('sub13_window_html.py', '.'),
    ('sub14_window_html.py', '.'),
    ('sub15_window_html.py', '.'),
    ('sub16_window_html.py', '.'),
    ('sub17_window_html.py', '.'),
    ('sub18_window_html.py', '.'),
    ('sub19_window_html.py', '.'),
    ('sub20_window.py', '.'),
    ('sub21_window.py', '.'),
    ('sub37_window.py', '.'),
    ('sub100_window.py', '.'),
    ('sub210_window.py', '.'),
    # ملفات الطباعة
    ('print_test.py', '.'),
    ('print0.py', '.'),
    ('print1.py', '.'),
    ('print2.py', '.'),
    ('print3.py', '.'),
    ('print5.py', '.'),
    ('print6.py', '.'),
    ('print7.py', '.'),
    ('print8.py', '.'),
    ('print9.py', '.'),
    ('print10.py', '.'),
    ('print100.py', '.'),
    # ملفات إضافية
    ('database_utils.py', '.'),
    ('database_config.py', '.'),  # ملف إعدادات قاعدة البيانات الجديد
    ('absence_reports.py', '.'),
    ('attendance_report.py', '.'),
    ('default_settings_window.py', '.'),
    ('second_semester_helper.py', '.'),
    ('custom_messages.py', '.'),
    ('arabic_pdf_report.py', '.'),
    ('set_default_printer.py', '.'),
    ('split_attendance_report.py', '.'),
    ('check_table.py', '.'),
    ('help_guide.py', '.'),
    ('import_engine_corrected.py', '.'),
    ('institution_engine.py', '.'),
    ('performance_fix.py', '.'),
    ('print_settings.json', '.'),
    ('print_settings_violation.json', '.'),
    # المجلدات
    ('fonts/', 'fonts/'),
    ('temp/', 'temp/'),
    ('date2025/', 'date2025/'),  # مجلد قاعدة البيانات الجديد
    ('__pycache__/', '__pycache__/'),
]

a = Analysis(
    ['app.py'],
    pathex=[r'c:\Users\<USER>\Desktop\csv\taheri22'],
    binaries=[],
    datas=datas,
    # إضافة خيارات لتحسين التوافق
    hiddenimports=[
        # النوافذ الرئيسية
        'main_window',
        'app',
        'sub01_window',
        'sub1_window_html',
        'sub2_window_html',
        'sub3_window_html',
        'sub4_window',
        'sub5_window_html',
        'sub6_window_html',
        'sub7_window_html',
        'sub8_window',
        'sub9_window',
        'sub9_window__html',
        'sub10_window',
        'sub10_window__html',
        'sub11_window_html',
        'sub12_window_html',
        'sub13_window_html',
        'sub14_window_html',
        'sub15_window_html',
        'sub16_window_html',
        'sub17_window_html',
        'sub18_window_html',
        'sub19_window_html',
        'sub20_window',
        'sub21_window',
        'sub37_window',
        'sub100_window',
        'sub210_window',
        # وحدات الطباعة
        'print_test',
        'print0',
        'print1',
        'print2',
        'print3',
        'print5',
        'print6',
        'print7',
        'print8',
        'print9',
        'print10',
        'print100',
        # وحدات إضافية
        'database_utils',
        'database_config',  # وحدة إعدادات قاعدة البيانات الجديدة
        'absence_reports',
        'attendance_report',
        'default_settings_window',
        'custom_messages',
        'arabic_pdf_report',
        'second_semester_helper',
        'set_default_printer',
        'split_attendance_report',
        'check_table',
        'help_guide',
        'import_engine_corrected',
        'institution_engine',
        'performance_fix',
        # مكتبات خارجية
        'PIL',
        'PIL.Image',
        'PIL.ImageDraw',
        'PIL.ImageFont',
        'PIL.ImageWin',
        'arabic_reshaper',
        'bidi.algorithm',
        'win32print',
        'win32ui',
        'subprocess',  # لإنشاء اختصارات سطح المكتب
        'logging',
        'sqlite3',
        'PyQt5.QtWidgets',
        'PyQt5.QtGui',
        'PyQt5.QtCore',
        'PyQt5.QtPrintSupport',
        'reportlab',
        'reportlab.lib',
        'reportlab.pdfgen',
        'reportlab.pdfbase',
        'reportlab.platypus',
        'reportlab.lib.pagesizes',
        'reportlab.lib.units',
        'reportlab.lib.colors',
        'reportlab.lib.styles',
        'reportlab.pdfbase.ttfonts',
        'reportlab.pdfbase.pdfmetrics',
        # أضف أي وحدات أخرى مطلوبة هنا
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

# وضع "one-folder" (مجلد واحد)
exe = EXE(
    pyz,
    a.scripts,
    [],  # فارغة لوضع المجلد الواحد - لا تضع a.binaries و a.zipfiles و a.datas هنا
    exclude_binaries=True,  # مهم! يجب أن تكون True للمجلد الواحد
    name='Taheri77',
    debug=False,  # تغيير إلى False لإيقاف وضع التصحيح
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,  # تغيير إلى False لإخفاء نافذة موجه الأوامر
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch='x86',  # تحديد بنية 32 بت للتوافق مع جميع الأنظمة
    codesign_identity=None,
    entitlements_file=None,
    icon='01.ico',
)

# جمع جميع الملفات في مجلد واحد
coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='Taheri77'
)
