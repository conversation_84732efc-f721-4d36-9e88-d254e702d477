#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import sqlite3
import datetime
import os
import re
import traceback
from database_config import get_database_path, get_database_connection
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QW<PERSON>t, QFrame, QComboBox, QTableView,
    QGraphicsDropShadowEffect, QPushButton, QMessageBox, QLabel, QVBoxLayout,
    QHBoxLayout, QGroupBox, QLineEdit, QFormLayout, QToolButton, QDialog, QTextEdit, QTextBrowser
)
from PyQt5.QtGui import QFont, QColor, QIcon, QBrush, QTextOption
from PyQt5.QtCore import Qt, QAbstractTableModel, QModelIndex, QVariant, QSettings, QSize
from PyQt5.QtSql import QSqlDatabase, QSqlQuery
from PyQt5.QtWidgets import QHeaderView, QStyledItemDelegate

# استيراد نافذة الإعدادات الافتراضية من الملف الجديد
from default_settings_window import DefaultSettingsDialog

# --- تعديل الاستيراد لدوال تقرير الأسدس ---
try:
    # محاولة استيراد دوال تقرير الأسدس من absence_reports.py
    from absence_reports import print_semester_report, print_semester_report_multiple
    # محاولة استيراد دالة الأسدس الثاني إذا كانت متوفرة
    try:
        from absence_reports import print_second_semester_report
        SECOND_SEMESTER_FUNCTION_AVAILABLE = True
        print("تم استيراد دالة تقرير الأسدس الثاني من absence_reports.py بنجاح.")
    except ImportError:
        # إذا لم تكن دالة الأسدس الثاني متوفرة، نستخدم دالة بديلة
        def print_second_semester_report(parent, section):
            print("دالة طباعة تقرير الأسدس الثاني غير متوفرة بشكل مباشر.")
            print("محاولة استخدام دالة الأسدس العامة لتقرير الأسدس الثاني...")
            # استدعاء الدالة العامة بطريقة تحدد أنها للأسدس الثاني (ربما من خلال بارامتر في الواجهة)
            # هذه مجرد محاولة، وقد لا تنجح اعتمادًا على تنفيذ الدالة الأصلية
            return print_semester_report(parent, section)
        SECOND_SEMESTER_FUNCTION_AVAILABLE = True

    SEMESTER_FUNCTIONS_AVAILABLE = True
    print("تم استيراد دوال تقرير الأسدس من absence_reports.py بنجاح.") # رسالة تأكيد
except ImportError as e:
    # طباعة رسالة الخطأ الفعلية للمساعدة في التشخيص
    print(f"فشل استيراد دوال تقرير الأسدس من absence_reports.py: {e}")
    # إذا لم تنجح محاولة الاستيراد، نعرف دوال فارغة
    def print_semester_report(parent, section):
        print("وظيفة طباعة تقرير الأسدس غير متوفرة بسبب فشل الاستيراد.")
        return False

    def print_second_semester_report(parent, section):
        print("وظيفة طباعة تقرير الأسدس الثاني غير متوفرة بسبب فشل الاستيراد.")
        return False

    def print_semester_report_multiple(parent, sections_list):
        print("وظيفة طباعة تقارير الأسدس المتعددة غير متوفرة بسبب فشل الاستيراد.")
        return False
    SEMESTER_FUNCTIONS_AVAILABLE = False
    SECOND_SEMESTER_FUNCTION_AVAILABLE = False

# استيراد دالة طباعة التقرير الشهري (تبقى كما هي)
try:
    from absence_reports import print_absence_report
    ABSENCE_REPORT_FUNCTION_AVAILABLE = True
    print("تم استيراد دالة تقرير الغياب الشهري من absence_reports.py بنجاح.")
except ImportError as e:
    # طباعة رسالة الخطأ الفعلية للمساعدة في التشخيص
    print(f"فشل استيراد دالة تقرير الغياب الشهري من absence_reports.py: {e}")
    # تعريف دالة بديلة
    def print_absence_report(parent, section, month, model=None):
        print(f"وظيفة طباعة تقرير الغياب الشهري غير متوفرة بسبب فشل الاستيراد.")
        parent.show_message("تنبيه",
            f"لا يمكن إنشاء تقرير الغياب الشهري لقسم '{section}' وشهر '{month}'.\n"
            f"لم يتم العثور على دالة طباعة التقارير المطلوبة.",
            parent.QMessageBox.Warning)
        return False
    ABSENCE_REPORT_FUNCTION_AVAILABLE = False

# مندوب مخصص لعرض الخلايا في الجدول
class StudentCodeDelegate(QStyledItemDelegate):
    def __init__(self, parent=None):
        super(StudentCodeDelegate, self).__init__(parent)

    def paint(self, painter, option, index):
        # التحقق مما إذا كانت الخلية في عمود رمز التلميذ (العمود 3)
        if index.column() == 3:
            # الحصول على قيمة الخلية
            value = index.data(Qt.DisplayRole)
            if value:
                # تعديل خيارات الرسم لتظهر كارتباط تشعبي
                option.palette.setColor(option.palette.Text, QColor('#0000FF'))  # لون أزرق للنص

                # رسم خلفية الخلية
                option.widget.style().drawPrimitive(option.widget.style().PE_PanelItemViewItem, option, painter, option.widget)

                # رسم النص بتنسيق الارتباط التشعبي
                painter.setPen(option.palette.color(option.palette.Text))
                painter.setFont(option.font)
                painter.drawText(option.rect, Qt.AlignCenter, str(value))

                # رسم خط تحت النص
                rect = option.rect
                line_y = rect.bottom() - 3
                painter.drawLine(rect.left() + 2, line_y, rect.right() - 2, line_y)
                return

        # استخدام الرسم الافتراضي للخلايا الأخرى
        super(StudentCodeDelegate, self).paint(painter, option, index)

# نموذج مخصص يدعم التحرير
class EditableTableModel(QAbstractTableModel):
    def __init__(self, data=[], headers=[], parent=None):
        super(EditableTableModel, self).__init__(parent)
        self._data = data
        self._headers = headers
        # إضافة عتبة الغياب الأسبوعي والشهري
        self.threshold = 0
        self.monthly_threshold = 0

    def rowCount(self, parent=QModelIndex()):
        return len(self._data)

    def columnCount(self, parent=QModelIndex()):
        # التأكد من أن هذه السطر يستخدم نفس نوع ومقدار المسافة البادئة مثل السطر أعلاه
        return len(self._headers) if self._data else 0

    def data(self, index, role=Qt.DisplayRole):
        if not index.isValid():
            return QVariant()

        row = index.row()
        col = index.column()

        # التحقق من حدود البيانات (إذا لم يكن موجودًا بالفعل)
        if row >= len(self._data) or col >= len(self._data[row]):
             print(f"Data requested for out-of-bounds index [{row},{col}], role={role}") # تشخيص
             return QVariant()

        value = self._data[row][col]

        if role == Qt.DisplayRole:
            return str(value)
        elif role == Qt.TextAlignmentRole:
            return Qt.AlignCenter
        elif role == Qt.BackgroundRole:
            # التحقق من أعمدة الأسابيع (4 إلى 8)
            if 4 <= col <= 8:
                try:
                    cell_value = str(value).strip()
                    arabic_to_english = {'٠': '0', '١': '1', '٢': '2', '٣': '3', '٤': '4',
                                        '٥': '5', '٦': '6', '٧': '7', '٨': '8', '٩': '9'}
                    normalized_value = ''.join(arabic_to_english.get(c, c) for c in cell_value)

                    # استخدام self.threshold بدلاً من القيمة الثابتة 5
                    if normalized_value.isdigit() and self.threshold > 0:
                        num_value = int(normalized_value)
                        # تلوين الخلية باللون الأصفر عندما تكون القيمة أكبر من أو تساوي عتبة الغياب الأسبوعي
                        if num_value >= self.threshold:
                            return QBrush(QColor('yellow')) # أصفر (استخدام QBrush)
                except (ValueError, TypeError):
                    pass # تجاهل القيم غير الرقمية

            # تلوين المجموع الشهري (العمود 9)
            if col == 9:
                try:
                    cell_value = str(value).strip()
                    # توحيد الأرقام قبل التحقق
                    arabic_to_english = {'٠': '0', '١': '1', '٢': '2', '٣': '3', '٤': '4',
                                        '٥': '5', '٦': '6', '٧': '7', '٨': '8', '٩': '9'}
                    normalized_value = ''.join(arabic_to_english.get(c, c) for c in cell_value)

                    if normalized_value.isdigit() and self.monthly_threshold > 0:
                        num_value = int(normalized_value)
                        if num_value >= self.monthly_threshold:
                            return QBrush(QColor('#FF6347')) # أحمر فاتح (طماطم) (استخدام QBrush)
                except (ValueError, TypeError):
                    pass

        return QVariant() # إرجاع قيمة فارغة للأدوار الأخرى أو إذا لم تتحقق الشروط

    def setData(self, index, value, role=Qt.EditRole):
        if not index.isValid() or role != Qt.EditRole:
            return False

        row = index.row()
        col = index.column()

        # تنظيف وتوحيد القيمة قبل التخزين
        if isinstance(value, str):
            value = value.strip()
            arabic_to_english = {'٠': '0', '١': '1', '٢': '2', '٣': '3', '٤': '4',
                                '٥': '5', '٦': '6', '٧': '7', '٨': '8', '٩': '9'}
            value = ''.join(arabic_to_english.get(c, c) for c in value)

        # التحقق إذا كانت القيمة قد تغيرت بالفعل
        if str(self._data[row][col]) == str(value):
             return False # لا تقم بأي شيء إذا لم تتغير القيمة

        self._data[row][col] = value
        print(f"تم تحديث البيانات الداخلية: [{row},{col}] = {value}")

        # إعادة حساب المجموع الشهري وتحديثه إذا تم تغيير أحد الأسابيع
        if 4 <= col <= 8:
            weekly_values = []
            for c in range(4, 9):
                try:
                    val = str(self._data[row][c]).strip()
                    # تحويل أي أرقام عربية متبقية إلى إنجليزية
                    arabic_to_english = {'٠': '0', '١': '1', '٢': '2', '٣': '3', '٤': '4',
                                       '٥': '5', '٦': '6', '٧': '7', '٨': '8', '٩': '9'}
                    normalized_val = ''.join(arabic_to_english.get(ch, ch) for ch in val)

                    if normalized_val and normalized_val.isdigit():
                        weekly_values.append(int(normalized_val))
                    else:
                        weekly_values.append(0)  # تعامل مع القيم الفارغة أو غير الرقمية كصفر
                except (ValueError, TypeError):
                    weekly_values.append(0)

            # حساب المجموع الشهري
            monthly_total = sum(weekly_values)
            self._data[row][9] = str(monthly_total)
            print(f"تم تحديث المجموع الشهري للصف {row}: {monthly_total}")

            # إعلام الواجهة بتغيير الخلية الحالية وخلية المجموع
            self.dataChanged.emit(index, index) # تحديث الخلية المعدلة
            monthly_index = self.index(row, 9)
            self.dataChanged.emit(monthly_index, monthly_index)  # تحديث خلية المجموع

            # تطبيق التلوين حسب العتبة للخلية المحدثة والمجموع
            if self.threshold > 0 or self.monthly_threshold > 0:
                self.dataChanged.emit(
                    self.index(row, 4),  # بداية من أول خلية في الأسبوع الأول للصف
                    self.index(row, 9)   # نهاية بخلية المجموع الشهري للصف
                )
        else:
            # إذا تم تغيير عمود آخر (مثل الملاحظات أو الغياب المبرر)، فقط أبلغ عن تغيير تلك الخلية
            self.dataChanged.emit(index, index, [Qt.DisplayRole])

        return True

    def headerData(self, section, orientation, role=Qt.DisplayRole):
        if orientation == Qt.Horizontal and role == Qt.DisplayRole:
            return self._headers[section]
        return QVariant()

    def flags(self, index):
        if not index.isValid():
            return Qt.NoItemFlags

        col = index.column()
        # الفهارس في self._data: id=0(مخفي), رت=1, الاسم=2, رمز=3, أسابيع=4-8, مجموع=9, مبرر=10, ملاحظات=11
        # الأعمدة القابلة للتحرير: الأسابيع (4-8), الغياب المبرر (10), الملاحظات (11)
        # تعطيل التحرير لعمود المجموع الشهري (9)
        if (4 <= col <= 8) or col == 10 or col == 11: # العمود 9 لم يعد قابلاً للتحرير
            return Qt.ItemIsEnabled | Qt.ItemIsSelectable | Qt.ItemIsEditable
        # بقية الأعمدة قابلة للتحديد والعرض فقط
        return Qt.ItemIsEnabled | Qt.ItemIsSelectable

def calc_week_dates(start_date_str):
    """حساب تواريخ بداية الأسابيع (حتى خمسة أسابيع) بناءً على تاريخ بداية الشهر"""
    try:
        if isinstance(start_date_str, str) and start_date_str:
            day, month, year = map(int, start_date_str.split('-'))
            start_date = datetime.date(year, month, day)
            week_starts = []
            current_date = start_date
            # البحث عن أول اثنين (بداية الأسبوع)
            while (current_date.weekday() != 0):
                current_date += datetime.timedelta(days=1)
            current_month = start_date.month
            for i in range(5):
                week_start = current_date + datetime.timedelta(days=i*7)
                # إذا كان الأسبوع الخامس خارج الشهر، نعيد سلسلة فارغة
                if i == 4 and week_start.month != current_month:
                    week_starts.append("")
                else:
                    date_str = f"{week_start.day:02d}-{week_start.month:02d}-{week_start.year}"
                    week_starts.append(date_str)
            return week_starts
        return ["" for _ in range(5)]
    except Exception as e:
        print(f"خطأ في حساب تواريخ الأسابيع: {e}")
        return ["" for _ in range(5)]

class Hirasa300Window(QMainWindow):
    def __init__(self, db=None, academic_year=None, parent=None):
        super().__init__(parent)
        self.settings = QSettings('Taheri200', 'Sub9Window')
        self.db = db
        self.academic_year = academic_year
        self.setWindowTitle("مسلك الغياب")
        self.setMinimumWidth(1000)
        # إعداد الخطوط المستخدمة
        self.font_calibri_13_bold = QFont("Calibri", 13)
        self.font_calibri_13_bold.setBold(True)
        self.font_calibri_13_normal = QFont('Calibri', 13)
        self.font_calibri_12_normal = QFont('Calibri', 12)
        # إنشاء الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        # تخطيط بسيط
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 2, 5, 5)  # زيادة الهامش العلوي إلى 20 بكسل
        main_layout.setSpacing(10)  # زيادة المسافة بين العناصر
        # إضافة شريط أزرار بسيط في الأعلى
        button_bar = QHBoxLayout()
        button_bar.setContentsMargins(0, 5, 0, 5)  # إضافة هوامش علوية وسفلية للأزرار
        button_bar.setSpacing(4)  # تقليل المسافة بين الأزرار من 5 إلى 4
        # تم إزالة تعريف الخط غير المستخدم

        # زر الحفظ مع أيقونة مخصصة
        self.btn_save = QPushButton("حفظ")
        self.btn_save.setObjectName("btn_save")  # تعيين معرف للزر لتطبيق النمط الخاص
        # استخدام خط مناسب للزر
        font_save_button = QFont('Calibri', 12, QFont.Bold)  # حجم خط مناسب مع غامق
        self.btn_save.setFont(font_save_button)
        self.btn_save.setFixedHeight(35)  # نفس ارتفاع زر تقارير الغياب
        self.btn_save.setFixedWidth(160)  # نفس عرض زر تقارير الغياب
        self.btn_save.setCursor(Qt.PointingHandCursor)  # نفس المؤشر

        # استخدام أيقونة مخصصة للحفظ
        save_icon_path = "save_icon.svg"
        if os.path.exists(save_icon_path):
            self.btn_save.setIcon(QIcon(save_icon_path))
            # تعيين حجم الأيقونة
            self.btn_save.setIconSize(QSize(24, 24))
        else:
            # استخدام الأيقونة الافتراضية إذا لم يتم العثور على الملف
            self.btn_save.setIcon(QIcon.fromTheme("document-save"))

        button_bar.addWidget(self.btn_save)

        # إنشاء زر تقارير الغياب مع قائمة منسدلة
        from PyQt5.QtWidgets import QMenu
        self.btn_reports = QPushButton("📊 تقارير الغياب")
        self.btn_reports.setObjectName("reportButton")  # استخدام نفس النمط من apply_styles
        self.btn_reports.setFont(QFont('Calibri', 12, QFont.Bold))
        self.btn_reports.setFixedHeight(35)
        self.btn_reports.setFixedWidth(160)
        self.btn_reports.setCursor(Qt.PointingHandCursor)
        
        # استخدام أيقونة مخصصة للتقارير
        reports_icon_path = "reports_icon.svg"
        if os.path.exists(reports_icon_path):
            self.btn_reports.setIcon(QIcon(reports_icon_path))
            self.btn_reports.setIconSize(QSize(24, 24))
        else:
            # استخدام الأيقونة الافتراضية إذا لم يتم العثور على الملف
            self.btn_reports.setIcon(QIcon.fromTheme("document-print"))
        
        # إنشاء القائمة المنسدلة
        reports_menu = QMenu(self)
        reports_menu.setLayoutDirection(Qt.RightToLeft)
        reports_menu.setStyleSheet("""
            QMenu {
                background-color: white;
                border: 2px solid #6f42c1;
                border-radius: 8px;
                padding: 5px;
                font-family: 'Calibri';
                font-size: 12pt;
                font-weight: bold;
            }
            QMenu::item {
                background-color: transparent;
                padding: 8px 20px;
                border-radius: 5px;
                color: #2c3e50;
            }
            QMenu::item:selected {
                background-color: #6f42c1;
                color: white;
            }
            QMenu::item:pressed {
                background-color: #5a32a3;
            }
        """)
        
        # إضافة عناصر القائمة
        monthly_action = reports_menu.addAction("📈 تقرير الغياب الشهري")
        monthly_action.triggered.connect(self.printAbsenceReport)
        
        first_semester_action = reports_menu.addAction("📋 تقرير الأسدس الأول")
        first_semester_action.triggered.connect(self.printFirstSemesterReport)
        
        second_semester_action = reports_menu.addAction("📊 تقرير الأسدس الثاني")
        second_semester_action.triggered.connect(self.printSecondSemesterReport)
        
        # ربط القائمة بالزر
        self.btn_reports.setMenu(reports_menu)
        button_bar.addWidget(self.btn_reports)

        # زر الاعدادات الافتراضية
        self.btn_default_settings = QPushButton("الاعدادات الافتراضية")
        self.btn_default_settings.setObjectName("btn_default_settings")  # تعيين معرف للزر
        self.btn_default_settings.setFont(QFont('Calibri', 12, QFont.Bold))  # خط غامق مثل بقية الأزرار
        self.btn_default_settings.setFixedHeight(35)  # نفس ارتفاع بقية الأزرار
        self.btn_default_settings.setFixedWidth(160)  # نفس عرض بقية الأزرار
        self.btn_default_settings.setCursor(Qt.PointingHandCursor)  # نفس المؤشر
        
        # استخدام أيقونة مخصصة للإعدادات
        settings_icon_path = "settings_icon.svg"
        if os.path.exists(settings_icon_path):
            self.btn_default_settings.setIcon(QIcon(settings_icon_path))
            self.btn_default_settings.setIconSize(QSize(24, 24))
        else:
            # استخدام الأيقونة الافتراضية إذا لم يتم العثور على الملف
            self.btn_default_settings.setIcon(QIcon.fromTheme("preferences-system"))
        button_bar.addWidget(self.btn_default_settings)

        # زر التعليمات
        self.btn_help = QPushButton("تعليمات")
        self.btn_help.setObjectName("btn_help")  # تعيين معرف للزر
        self.btn_help.setFont(QFont('Calibri', 12, QFont.Bold))  # خط غامق مثل بقية الأزرار
        self.btn_help.setFixedHeight(35)  # نفس ارتفاع بقية الأزرار
        self.btn_help.setFixedWidth(160)  # نفس عرض بقية الأزرار
        self.btn_help.setCursor(Qt.PointingHandCursor)  # نفس المؤشر
        
        # استخدام أيقونة مخصصة للتعليمات
        help_icon_path = "help_icon.svg"
        if os.path.exists(help_icon_path):
            self.btn_help.setIcon(QIcon(help_icon_path))
            self.btn_help.setIconSize(QSize(24, 24))
        else:
            # استخدام الأيقونة الافتراضية إذا لم يتم العثور على الملف
            self.btn_help.setIcon(QIcon.fromTheme("help-contents"))
        button_bar.addWidget(self.btn_help)
        # إضافة فاصل مرن
        button_bar.addStretch(1)

        # إنشاء تخطيطات عمودية لكل مربع تحرير وسرد مع عنوانه
        # إضافة مربع نص لعرض السنة الدراسية
        school_year_layout = QVBoxLayout()
        school_year_layout.setSpacing(5)  # المسافة بين العنوان والمربع
        school_year_label = QLabel("السنة الدراسية:")
        school_year_label.setFont(self.font_calibri_13_bold)  # تغيير الخط إلى غامق
        school_year_label.setAlignment(Qt.AlignCenter)  # محاذاة العنوان في الوسط
        self.school_year_text = QLineEdit()
        self.school_year_text.setFont(self.font_calibri_13_bold)  # تغيير الخط إلى غامق
        self.school_year_text.setFixedSize(150, 30)  # تعيين عرض مربع النص إلى 150 بكسل
        self.school_year_text.setReadOnly(True)  # جعل مربع النص للقراءة فقط
        self.school_year_text.setAlignment(Qt.AlignCenter)  # محاذاة النص في الوسط
        self.school_year_text.setStyleSheet("background-color: #f0f0f0; padding: 0px; margin: 0px;")  # لون خلفية رمادي فاتح بدون حشو
        school_year_layout.addWidget(school_year_label)
        school_year_layout.addWidget(self.school_year_text)
        button_bar.addLayout(school_year_layout)

        # تخطيط المستوى
        level_layout = QVBoxLayout()
        level_layout.setSpacing(5)  # المسافة بين العنوان والمربع
        level_label = QLabel("المستوى:")
        level_label.setFont(self.font_calibri_13_bold)  # تغيير الخط إلى غامق
        level_label.setAlignment(Qt.AlignCenter)  # محاذاة العنوان في الوسط
        self.combo_mustawa = QComboBox()
        self.combo_mustawa.setFont(self.font_calibri_13_bold)  # تغيير الخط إلى غامق
        self.combo_mustawa.setFixedSize(285, 30)  # تصغير عرض المستوى بنسبة 5% (300 * 0.95 = 285)
        self.combo_mustawa.setObjectName("combo_mustawa")  # تعيين معرف للتنسيق
        level_layout.addWidget(level_label)
        level_layout.addWidget(self.combo_mustawa)
        button_bar.addLayout(level_layout)

        # تخطيط القسم
        section_layout = QVBoxLayout()
        section_layout.setSpacing(5)  # المسافة بين العنوان والمربع
        section_label = QLabel("القسم:")
        section_label.setFont(self.font_calibri_13_bold)  # تغيير الخط إلى غامق
        section_label.setAlignment(Qt.AlignCenter)  # محاذاة العنوان في الوسط
        self.combo_qism = QComboBox()
        self.combo_qism.setFont(self.font_calibri_13_bold)  # تغيير الخط إلى غامق
        self.combo_qism.setFixedSize(124, 30)  # تصغير عرض القسم بنسبة 5% (130 * 0.95 = 124)
        self.combo_qism.setObjectName("combo_qism")  # تعيين معرف للتنسيق
        section_layout.addWidget(section_label)
        section_layout.addWidget(self.combo_qism)
        button_bar.addLayout(section_layout)

        # تخطيط الشهر
        month_layout = QVBoxLayout()
        month_layout.setSpacing(5)  # المسافة بين العنوان والمربع
        month_label = QLabel("الشهر:")
        month_label.setFont(self.font_calibri_13_bold)  # تغيير الخط إلى غامق
        month_label.setAlignment(Qt.AlignCenter)  # محاذاة العنوان في الوسط
        self.combo_shahr = QComboBox()
        self.combo_shahr.setFont(self.font_calibri_13_bold)  # تغيير الخط إلى غامق
        self.combo_shahr.setFixedSize(95, 30)  # تصغير عرض الشهر بنسبة 5% (100 * 0.95 = 95)
        self.combo_shahr.setObjectName("combo_shahr")  # تعيين معرف للتنسيق
        month_layout.addWidget(month_label)
        month_layout.addWidget(self.combo_shahr)
        button_bar.addLayout(month_layout)
        # إضافة شريط الأزرار للتخطيط الرئيسي
        main_layout.addLayout(button_bar)
        # إنشاء الجدول
        self.table_view = QTableView()
        self.table_view.setFont(self.font_calibri_13_bold)  # تغيير الخط إلى غامق
        header = self.table_view.horizontalHeader()
        header.setFont(self.font_calibri_13_bold)  # تغيير خط رؤوس الأعمدة إلى غامق
        header.setMinimumHeight(35)
        # إضافة الجدول للتخطيط الرئيسي
        main_layout.addWidget(self.table_view)
        # تطبيق الأنماط
        self.apply_styles()
        # إعداد قاعدة البيانات والنموذج
        self.setupDatabase()
        # تعبئة مربعات التحرير والسرد من قاعدة البيانات باستخدام sqlite3
        self.populateFilters()
        # ربط الإشارات
        self.combo_qism.currentIndexChanged.connect(self.updateFilterShahr)  # تحديث مربع الشهر عند تغيير القسم
        self.combo_qism.currentIndexChanged.connect(self.updateModel)
        self.combo_shahr.currentIndexChanged.connect(self.updateModel)
        self.combo_mustawa.currentIndexChanged.connect(self.updateFilterQism)
        self.btn_save.clicked.connect(self.saveData)
        # تم حذف ربط إشارات الأزرار القديمة (btn_print, btn_print_second_semester, btn_print_first_semester)
        # لأنها تم استبدالها بالقائمة المنسدلة في btn_absence_reports
        self.btn_default_settings.clicked.connect(self.openDefaultSettings)
        self.btn_help.clicked.connect(self.showHelpDialog)

        # إضافة ربط إشارة النقر على خلايا الجدول
        self.table_view.clicked.connect(self.on_table_cell_clicked)
        # فتح النافذة بملء الشاشة
        self.showMaximized()

    def updateFilterShahr(self):
        """تفعيل مربع الشهر عندما يتم تحديد القسم"""
        section = self.combo_qism.currentText()
        if section:
            # تفعيل مربع الشهر إذا تم تحديد القسم
            self.combo_shahr.setEnabled(True)
            self.combo_shahr.setPlaceholderText("اختر الشهر")
        else:
            # تعطيل مربع الشهر إذا لم يتم تحديد القسم
            self.combo_shahr.setEnabled(False)
            self.combo_shahr.setPlaceholderText("حدد المستوى والقسم أولاً")

    def updateFilterQism(self):
        # تحديث قائمة الأقسام بناءً على اختيار المستوى
        conn = sqlite3.connect(get_database_path())
        cur = conn.cursor()

        # الحصول على السنة الدراسية ورقم الحراسة من جدول بيانات_المؤسسة
        cur.execute("SELECT السنة_الدراسية, رقم_الحراسة FROM بيانات_المؤسسة LIMIT 1")
        res = cur.fetchone()

        year = res[0] if res and res[0] else ""
        guard_number = res[1] if res and len(res) > 1 else None

        print(f"السنة الدراسية: {year}, رقم الحراسة: {guard_number}")

        # تحديث مربع نص السنة الدراسية
        self.school_year_text.setText(year)

        # حفظ القيمة الحالية لـ combo_qism قبل التغيير
        current_qism = self.combo_qism.currentText()

        level = self.combo_mustawa.currentText()
        if level:
            # التحقق من وجود عمود الأقسام_المسندة في جدول البنية_التربوية
            has_assigned_column = False
            try:
                cur.execute("PRAGMA table_info(البنية_التربوية)")
                columns = [info[1] for info in cur.fetchall()]
                has_assigned_column = "الأقسام_المسندة" in columns
                print(f"هل يوجد عمود الأقسام_المسندة في البنية_التربوية؟ {has_assigned_column}")
            except Exception as e:
                print(f"خطأ عند التحقق من وجود عمود الأقسام_المسندة: {e}")

            # صياغة استعلام SQL المناسب بناءً على وجود عمود الأقسام_المسندة
            if has_assigned_column and guard_number:
                try:
                    query = """
                        SELECT DISTINCT القسم
                        FROM البنية_التربوية b
                        JOIN بيانات_المؤسسة inst ON b.السنة_الدراسية = inst.السنة_الدراسية
                        WHERE b.المستوى = ?
                          AND b.الأقسام_المسندة = ?
                    """
                    print(f"استعلام مع عمود الأقسام_المسندة: {query}")
                    cur.execute(query, (level, guard_number))
                except Exception as e:
                    print(f"خطأ في تنفيذ استعلام مع الأقسام_المسندة: {e}")
                    query = """
                        SELECT DISTINCT القسم
                        FROM البنية_التربوية b
                        JOIN بيانات_المؤسسة inst ON b.السنة_الدراسية = inst.السنة_الدراسية
                        WHERE b.المستوى = ?
                    """
                    cur.execute(query, (level,))
            else:
                query = """
                    SELECT DISTINCT القسم
                    FROM البنية_التربوية b
                    JOIN بيانات_المؤسسة inst ON b.السنة_الدراسية = inst.السنة_الدراسية
                    WHERE b.المستوى = ?
                """
                print(f"استعلام بدون عمود الأقسام_المسندة: {query}")
                cur.execute(query, (level,))
        else:
            # إذا لم يتم اختيار مستوى، نجلب الأقسام للسنة الدراسية فقط
            if has_assigned_column and guard_number:
                try:
                    cur.execute("PRAGMA table_info(البنية_التربوية)")
                    columns = [info[1] for info in cur.fetchall()]
                    if "الأقسام_المسندة" in columns:
                        cur.execute("""
                            SELECT DISTINCT القسم
                            FROM البنية_التربوية b
                            JOIN بيانات_المؤسسة inst ON b.السنة_الدراسية = inst.السنة_الدراسية
                            WHERE b.الأقسام_المسندة = ?
                        """, (guard_number,))
                    else:
                        cur.execute("""
                            SELECT DISTINCT القسم
                            FROM البنية_التربوية b
                            JOIN بيانات_المؤسسة inst ON b.السنة_الدراسية = inst.السنة_الدراسية
                        """)
                except Exception as e:
                    print(f"خطأ عند جلب الأقسام: {e}")
                    cur.execute("""
                        SELECT DISTINCT القسم
                        FROM البنية_التربوية b
                        JOIN بيانات_المؤسسة inst ON b.السنة_الدراسية = inst.السنة_الدراسية
                    """)
            else:
                cur.execute("""
                    SELECT DISTINCT القسم
                    FROM البنية_التربوية b
                    JOIN بيانات_المؤسسة inst ON b.السنة_الدراسية = inst.السنة_الدراسية
                """)

        sections = [row[0] for row in cur.fetchall() if row[0] is not None]
        print(f"تم جلب {len(sections)} قسم: {sections}")

        # دالة مساعدة لاستخراج الرقم من اسم القسم للترتيب
        def extract_section_number(section_name):
            # تقسيم اسم القسم إلى جزأين: الجزء الأول (مثل 1APIC) والجزء الثاني (مثل 1)
            parts = section_name.split('-')
            if len(parts) > 1:
                try:
                    # محاولة استخراج الرقم من الجزء الثاني
                    return int(parts[1])
                except (ValueError, IndexError):
                    # إذا لم يكن هناك رقم، نعيد قيمة كبيرة لوضعه في النهاية
                    return 9999

            # إذا لم يكن هناك شرطة، نحاول استخراج الرقم من نهاية النص
            import re
            match = re.search(r'(\d+)$', section_name)
            if match:
                return int(match.group(1))
            return 9999

        # دالة مساعدة للترتيب المزدوج: أولاً حسب الجزء الأول من القسم، ثم حسب الرقم
        def sort_key(section_name):
            # تقسيم اسم القسم إلى جزأين: الجزء الأول (مثل 1APIC) والجزء الثاني (مثل 1)
            parts = section_name.split('-')
            prefix = parts[0] if len(parts) > 0 else section_name

            # استخراج الرقم من بداية الجزء الأول (مثل 1 من 1APIC)
            import re
            prefix_num = 0
            match = re.match(r'^(\d+)', prefix)
            if match:
                prefix_num = int(match.group(1))

            # إرجاع مفتاح الترتيب المزدوج: (رقم المستوى، رقم القسم)
            return (prefix_num, extract_section_number(section_name))

        # ترتيب الأقسام باستخدام دالة الترتيب المخصصة
        sorted_sections = sorted(sections, key=sort_key)
        print(f"الأقسام بعد الترتيب: {sorted_sections}")

        # تذكر: احفظ إشارة currentIndexChanged قبل تغيير مربع الاختيار
        self.combo_qism.blockSignals(True)

        self.combo_qism.clear()
        # إضافة عنصر فارغ في البداية
        self.combo_qism.addItem("")
        self.combo_qism.addItems(sorted_sections)

        # تحديد العنصر الفارغ كعنصر افتراضي، أو المحاولة للاحتفاظ بالقيمة السابقة إذا كانت موجودة في القائمة الجديدة
        index = self.combo_qism.findText(current_qism)
        if (index >= 0):
            self.combo_qism.setCurrentIndex(index)
        else:
            self.combo_qism.setCurrentIndex(0)

        # تفعيل مربع القسم إذا تم تحديد مستوى
        if level:
            self.combo_qism.setEnabled(True)
            self.combo_qism.setPlaceholderText("اختر القسم")
        else:
            self.combo_qism.setEnabled(False)
            self.combo_qism.setPlaceholderText("حدد المستوى أولاً")

        # تعطيل مربع الشهر حتى يتم تحديد القسم
        self.combo_shahr.setEnabled(False)
        self.combo_shahr.setPlaceholderText("حدد المستوى والقسم أولاً")

        # استعادة إشارة currentIndexChanged
        self.combo_qism.blockSignals(False)

        conn.close()

    def setupDatabase(self):
        # إعداد الاتصال بقاعدة بيانات SQLite
        self.db = QSqlDatabase.addDatabase("QSQLITE")
        self.db.setDatabaseName(get_database_path())
        if not self.db.open():
            print("Database error:", self.db.lastError().text())
        # إنشاء نموذج فارغ باستخدام EditableTableModel مع عناوين مبدئية
        # سنقوم بتحديث العناوين لاحقًا بناءً على تواريخ الأسابيع
        headers = ["رت", "الاسم والنسب", "رمز التلميذ",
                   "الأسبوع الأول", "الأسبوع الثاني", "الأسبوع الثالث",
                   "الأسبوع الرابع", "الأسبوع الخامس", "ملاحظات"]
        self.model = EditableTableModel([], headers)
        self.table_view.setModel(self.model)
        # ضبط محاذاة الجدول من اليمين إلى اليسار
        self.table_view.setLayoutDirection(Qt.RightToLeft)
        # زيادة ارتفاع العناوين للسماح بعرض سطرين
        header = self.table_view.horizontalHeader()
        header.setMinimumHeight(45)  # زيادة ارتفاع العناوين
        # محاذاة النص في العناوين إلى المركز
        header.setDefaultAlignment(Qt.AlignCenter)
        # التأكد من عدم وجود أي تنسيق مطبق هنا
        self.table_view.setStyleSheet("")
        # التأكد من عدم وجود مندوب مخصص قد يتداخل (يمكن إلغاء التعليق إذا لزم الأمر لاحقًا)
        # self.table_view.setItemDelegate(None) # تم التعليق عليه مؤقتًا بناءً على الاقتراح السابق

    def populateFilters(self):
        # تعبئة مربعات التحرير والسرد من قاعدة البيانات باستخدام sqlite3
        conn = sqlite3.connect(get_database_path())
        cur = conn.cursor()

        # الحصول على السنة الدراسية ورقم الحراسة من جدول بيانات_المؤسسة
        cur.execute("SELECT السنة_الدراسية, رقم_الحراسة FROM بيانات_المؤسسة LIMIT 1")
        res = cur.fetchone()
        year = res[0] if res and res[0] else ""
        guard_number = res[1] if res and len(res) > 1 else None

        # طباعة الاستعلام واظهار السنة الدراسية
        print(f"السنة الدراسية الحالية: {year}, رقم الحراسة: {guard_number}")

        # تحديث مربع نص السنة الدراسية
        self.school_year_text.setText(year)

        # التحقق من وجود عمود الأقسام_المسندة في جدول البنية_التربوية
        has_assigned_column = False
        try:
            cur.execute("PRAGMA table_info(البنية_التربوية)")
            columns = [info[1] for info in cur.fetchall()]
            has_assigned_column = "الأقسام_المسندة" in columns
            print(f"هل يوجد عمود الأقسام_المسندة في البنية_التربوية؟ {has_assigned_column}")
        except Exception as e:
            print(f"خطأ عند التحقق من وجود عمود الأقسام_المسندة: {e}")

        # تحقق من وجود عمود السنة_الدراسية في جدول البنية_التربوية
        try:
            # بناء استعلام SQL حسب وجود عمود الأقسام_المسندة
            if has_assigned_column and guard_number:
                query = """
                    SELECT DISTINCT المستوى
                    FROM البنية_التربوية b
                    JOIN بيانات_المؤسسة inst ON b.السنة_الدراسية = inst.السنة_الدراسية
                    WHERE b.الأقسام_المسندة = ?
                    ORDER BY b.ترتيب_المستويات
                """
                print(f"استعلام جلب المستويات مع الأقسام_المسندة: {query}")
                cur.execute(query, (guard_number,))
            else:
                query = """
                    SELECT DISTINCT المستوى
                    FROM البنية_التربوية b
                    JOIN بيانات_المؤسسة inst ON b.السنة_الدراسية = inst.السنة_الدراسية
                    ORDER BY b.ترتيب_المستويات
                """
                print(f"استعلام جلب المستويات بدون الأقسام_المسندة: {query}")
                cur.execute(query)

            levels = [row[0] for row in cur.fetchall() if row[0] is not None]
            print(f"المستويات التي تم جلبها: {levels}")

            # في حالة عدم وجود مستويات، نحاول التحقق من وجود بيانات في الجدول
            if not levels:
                # التحقق من وجود بيانات مع الأقسام المسندة
                if has_assigned_column and guard_number:
                    cur.execute("SELECT COUNT(*) FROM البنية_التربوية WHERE السنة_الدراسية = ? AND الأقسام_المسندة = ?",
                               (year, guard_number))
                    count = cur.fetchone()[0]
                    print(f"عدد السجلات في البنية_التربوية للسنة {year} والأقسام_المسندة {guard_number}: {count}")

                    # إذا لم يكن هناك نتائج مع الأقسام_المسندة، نحاول البحث بدونها
                    if count == 0:
                        cur.execute("SELECT COUNT(*) FROM البنية_التربوية WHERE السنة_الدراسية = ?", (year,))
                        count_without_assigned = cur.fetchone()[0]
                        print(f"عدد السجلات بدون تصفية الأقسام_المسندة: {count_without_assigned}")

                        if count_without_assigned > 0:
                            print("إعادة محاولة جلب المستويات بدون تصفية الأقسام_المسندة")
                            cur.execute("""
                                SELECT DISTINCT المستوى
                                FROM البنية_التربوية
                                WHERE السنة_الدراسية = ?
                                ORDER BY ترتيب_المستويات
                            """, (year,))
                            levels = [row[0] for row in cur.fetchall() if row[0] is not None]
                            print(f"المستويات التي تم جلبها بدون تصفية الأقسام_المسندة: {levels}")
                else:
                    cur.execute("SELECT COUNT(*) FROM البنية_التربوية WHERE السنة_الدراسية = ?", (year,))
                    count = cur.fetchone()[0]
                    print(f"عدد السجلات في جدول البنية_التربوية للسنة {year}: {count}")

                if count == 0:
                    # التحقق من وجود بيانات في جدول البنية_التربوية عموماً
                    cur.execute("SELECT COUNT(*) FROM البنية_التربوية")
                    total_count = cur.fetchone()[0]
                    print(f"إجمالي عدد السجلات في جدول البنية_التربوية: {total_count}")

                    if total_count > 0:
                        # إذا كان هناك بيانات ولكن ليس للسنة المطلوبة، جلب قائمة السنوات المتوفرة
                        cur.execute("SELECT DISTINCT السنة_الدراسية FROM البنية_التربوية")
                        available_years = [row[0] for row in cur.fetchall() if row[0] is not None]
                        print(f"السنوات الدراسية المتوفرة في جدول البنية_التربوية: {available_years}")
        except Exception as e:
            print(f"خطأ أثناء جلب المستويات: {e}")
            levels = []

        # تحديث مربع التحرير والسرد للمستوى
        self.combo_mustawa.clear()
        # إضافة عنصر فارغ في البداية
        self.combo_mustawa.addItem("")
        if levels:
            self.combo_mustawa.addItems(levels)
        else:
            # عرض رسالة تشخيصية إذا لم يتم العثور على مستويات
            print("تحذير: لم يتم العثور على مستويات للسنة الدراسية المحددة")

            # محاولة جلب معلومات إضافية حول جدول البنية_التربوية
            try:
                cur.execute("PRAGMA table_info(البنية_التربوية)")
                columns = [info[1] for info in cur.fetchall()]
                print(f"أعمدة جدول البنية_التربوية: {columns}")
            except Exception as e:
                print(f"خطأ أثناء جلب معلومات الجدول: {e}")

        # تحديد العنصر الفارغ كعنصر افتراضي وإظهار رسالة للمستخدم
        self.combo_mustawa.setCurrentIndex(0)
        # إضافة نص توجيهي في مربع المستوى
        self.combo_mustawa.setPlaceholderText("الرجاء تحديد المستوى أولاً")

        # تصفية الأقسام
        # التحقق من وجود عمود الأقسام_المسندة في جدول البنية_التربوية
        has_assigned_column = False
        try:
            cur.execute("PRAGMA table_info(البنية_التربوية)")
            columns = [info[1] for info in cur.fetchall()]
            has_assigned_column = "الأقسام_المسندة" in columns
            print(f"هل يوجد عمود الأقسام_المسندة في البنية_التربوية (للأقسام)؟ {has_assigned_column}")
        except Exception as e:
            print(f"خطأ عند التحقق من وجود عمود الأقسام_المسندة للأقسام: {e}")

        # تطبيق التصفية المناسبة للأقسام
        try:
            # التعامل مع الأقسام المسندة بشكل صحيح
            sections = []  # تعريف المتغير قبل استخدامه

            if has_assigned_column and guard_number:
                print(f"تصفية الأقسام حسب الأقسام_المسندة '{guard_number}' والسنة الدراسية الحالية")

                # التحقق من وجود أقسام مسندة بالقيمة المحددة
                cur.execute("""
                    SELECT COUNT(*)
                    FROM البنية_التربوية b
                    JOIN بيانات_المؤسسة inst ON b.السنة_الدراسية = inst.السنة_الدراسية
                    WHERE b.الأقسام_المسندة = ?
                """, (guard_number,))

                count = cur.fetchone()[0]
                print(f"عدد الأقسام المسندة للحراسة '{guard_number}': {count}")

                if count > 0:
                    # استخدام الأقسام المسندة للتصفية
                    cur.execute("""
                        SELECT DISTINCT القسم
                        FROM البنية_التربوية b
                        JOIN بيانات_المؤسسة inst ON b.السنة_الدراسية = inst.السنة_الدراسية
                        WHERE b.الأقسام_المسندة = ?
                    """, (guard_number,))
                    sections = [row[0] for row in cur.fetchall() if row[0] is not None]

                # إذا لم يتم العثور على أقسام، نحاول البحث بطريقة مختلفة
                if not sections:
                    # محاولة البحث عن الأقسام المسندة بطريقة مختلفة (قد تكون القيمة مخزنة بتنسيق مختلف)
                    print("محاولة البحث عن الأقسام المسندة بطريقة مختلفة...")

                    # البحث عن أي قيمة تحتوي على رقم الحراسة
                    search_pattern = f"%{guard_number}%"
                    cur.execute("""
                        SELECT DISTINCT القسم
                        FROM البنية_التربوية b
                        JOIN بيانات_المؤسسة inst ON b.السنة_الدراسية = inst.السنة_الدراسية
                        WHERE b.الأقسام_المسندة LIKE ?
                    """, (search_pattern,))

                    sections = [row[0] for row in cur.fetchall() if row[0] is not None]

                    # إذا لم يتم العثور على أقسام باستخدام البحث النصي، نبحث بدون تصفية الأقسام المسندة
                    if not sections:
                        print("لم يتم العثور على أقسام باستخدام البحث النصي. محاولة البحث بدون تصفية الأقسام المسندة.")
                        cur.execute("""
                            SELECT DISTINCT القسم
                            FROM البنية_التربوية b
                            JOIN بيانات_المؤسسة inst ON b.السنة_الدراسية = inst.السنة_الدراسية
                        """)
                        sections = [row[0] for row in cur.fetchall() if row[0] is not None]
            else:
                print(f"تصفية الأقسام حسب السنة الدراسية الحالية فقط")
                cur.execute("""
                    SELECT DISTINCT القسم
                    FROM البنية_التربوية b
                    JOIN بيانات_المؤسسة inst ON b.السنة_الدراسية = inst.السنة_الدراسية
                """)
                sections = [row[0] for row in cur.fetchall() if row[0] is not None]

            print(f"الأقسام التي تم جلبها: {sections}")

            # دالة مساعدة لاستخراج الرقم من اسم القسم للترتيب
            def extract_section_number(section_name):
                # تقسيم اسم القسم إلى جزأين: الجزء الأول (مثل 1APIC) والجزء الثاني (مثل 1)
                parts = section_name.split('-')
                if len(parts) > 1:
                    try:
                        # محاولة استخراج الرقم من الجزء الثاني
                        return int(parts[1])
                    except (ValueError, IndexError):
                        # إذا لم يكن هناك رقم، نعيد قيمة كبيرة لوضعه في النهاية
                        return 9999

                # إذا لم يكن هناك شرطة، نحاول استخراج الرقم من نهاية النص
                import re
                match = re.search(r'(\d+)$', section_name)
                if match:
                    return int(match.group(1))
                return 9999

            # دالة مساعدة للترتيب المزدوج: أولاً حسب الجزء الأول من القسم، ثم حسب الرقم
            def sort_key(section_name):
                # تقسيم اسم القسم إلى جزأين: الجزء الأول (مثل 1APIC) والجزء الثاني (مثل 1)
                parts = section_name.split('-')
                prefix = parts[0] if len(parts) > 0 else section_name

                # استخراج الرقم من بداية الجزء الأول (مثل 1 من 1APIC)
                import re
                prefix_num = 0
                match = re.match(r'^(\d+)', prefix)
                if match:
                    prefix_num = int(match.group(1))

                # إرجاع مفتاح الترتيب المزدوج: (رقم المستوى، رقم القسم)
                return (prefix_num, extract_section_number(section_name))

            # ترتيب الأقسام باستخدام دالة الترتيب المخصصة
            sections = sorted(sections, key=sort_key)
            print(f"الأقسام بعد الترتيب: {sections}")

        except Exception as e:
            print(f"خطأ أثناء جلب الأقسام: {e}")
            sections = []

        # تحديث مربع التحرير والسرد للقسم
        self.combo_qism.clear()
        # إضافة عنصر فارغ في البداية
        self.combo_qism.addItem("")
        if sections:
            self.combo_qism.addItems(sections)
        else:
            print("تحذير: لم يتم العثور على أقسام مطابقة للمعايير")

        # تحديد العنصر الفارغ كعنصر افتراضي وإضافة نص توجيهي
        self.combo_qism.setCurrentIndex(0)
        # إضافة نص توجيهي في مربع القسم
        self.combo_qism.setPlaceholderText("حدد المستوى أولاً")
        # تعطيل مربع القسم حتى يتم تحديد المستوى
        self.combo_qism.setEnabled(False)

        # جلب الشهور
        try:
            cur.execute("""
                SELECT DISTINCT j.الشهر
                FROM جدولة_مسك_الغياب j
                JOIN بيانات_المؤسسة inst ON j.السنة_الدراسية = inst.السنة_الدراسية
            """)
            months = [str(row[0]) for row in cur.fetchall() if row[0] is not None]
            print(f"الشهور التي تم جلبها: {months}")
        except Exception as e:
            print(f"خطأ أثناء جلب الشهور: {e}")
            months = []

        # تحديث مربع التحرير والسرد للشهر
        self.combo_shahr.clear()
        if months:
            self.combo_shahr.addItems(months)
        else:
            print("تحذير: لم يتم العثور على شهور لجدولة مسك الغياب")

        # إضافة نص توجيهي في مربع الشهر
        self.combo_shahr.setPlaceholderText("حدد المستوى والقسم أولاً")
        # تعطيل مربع الشهر حتى يتم تحديد المستوى والقسم
        self.combo_shahr.setEnabled(False)

        conn.close()

    def updateModel(self):
        """تحديث نموذج البيانات مع تحسين الأداء"""
        # استخدام تأخير قصير لتحسين استجابة واجهة المستخدم
        from PyQt5.QtCore import QTimer
        QTimer.singleShot(10, self._delayed_update_model)

    def _delayed_update_model(self):
        """تنفيذ تحديث النموذج بعد تأخير قصير لتحسين الأداء"""
        try:
            print("بدء تحديث النموذج updateModel...") # طباعة للتشخيص

            # إظهار مؤشر الانتظار
            from PyQt5.QtCore import Qt
            from PyQt5.QtGui import QCursor
            self.setCursor(QCursor(Qt.WaitCursor))

            conn = sqlite3.connect(get_database_path())
            cur = conn.cursor()

            # التحقق من وجود عمود id في جدول مسك_الغياب_الأسبوعي
            cur.execute("PRAGMA table_info(مسك_الغياب_الأسبوعي)")
            columns = [info[1] for info in cur.fetchall()]
            print(f"أعمدة جدول مسك_الغياب_الأسبوعي: {columns}")

            # تحديد عمود المعرّف الصحيح
            id_column = "id"
            if "id" not in columns:
                if "المعرف" in columns:
                    id_column = "المعرف"
                elif "معرف" in columns:
                    id_column = "معرف"
                else:
                    # إذا لم يتم العثور على عمود معرّف، استخدم الرمز كمعرّف بديل
                    id_column = "رمز_التلميذ"
                print(f"استخدام {id_column} كعمود معرّف بديل عن id")

            # تحسين الاستعلام باستخدام مؤشرات وتقليل عدد الانضمامات
            base_query = f"""
                SELECT
                    m.{id_column} as "id",
                    l.رت as "رت",
                    sg.الاسم_والنسب as "الاسم والنسب",
                    m.رمز_التلميذ as "رمز التلميذ",
                    m.بداية_الشهر as "بداية الشهر",
                    m."1" as "الأسبوع الأول",
                    m."2" as "الأسبوع الثاني",
                    m."3" as "الأسبوع الثالث",
                    m."4" as "الأسبوع الرابع",
                    m."5" as "الأسبوع الخامس",
                    CAST(COALESCE(CAST(m."1" AS INTEGER), 0) +
                        COALESCE(CAST(m."2" AS INTEGER), 0) +
                        COALESCE(CAST(m."3" AS INTEGER), 0) +
                        COALESCE(CAST(m."4" AS INTEGER), 0) +
                        COALESCE(CAST(m."5" AS INTEGER), 0)
                    AS TEXT) as "المجموع الشهري",
                    m.الغياب_المبرر as "الغياب المبرر",
                    m.ملاحظات as "ملاحظات"
                FROM مسك_الغياب_الأسبوعي m
                LEFT JOIN اللوائح l ON m.رمز_التلميذ = l.الرمز
                LEFT JOIN السجل_العام sg ON m.رمز_التلميذ = sg.الرمز
                JOIN بيانات_المؤسسة inst ON l.السنة_الدراسية = inst.السنة_الدراسية
            """
            filters = []
            params = []
            month = self.combo_shahr.currentText()
            section = self.combo_qism.currentText()
            if month:
                filters.append("m.الشهر = ?")
                params.append(month)
            if section:
                filters.append("l.القسم = ?")
                params.append(section)
            if filters:
                base_query += " AND " + " AND ".join(filters)
            base_query += " ORDER BY l.رت"  # ترتيب حسب عمود "رت"

            # إضافة مؤشرات لتحسين الأداء
            cur.execute("CREATE INDEX IF NOT EXISTS idx_masak_ramz ON مسك_الغياب_الأسبوعي(رمز_التلميذ)")
            cur.execute("CREATE INDEX IF NOT EXISTS idx_lawayih_ramz ON اللوائح(الرمز)")
            cur.execute("CREATE INDEX IF NOT EXISTS idx_sijil_ramz ON السجل_العام(الرمز)")

            # تنفيذ الاستعلام
            cur.execute(base_query, params)
            rows = [list(row) for row in cur.fetchall()]
            print(f"تم جلب {len(rows)} صفًا من قاعدة البيانات.") # طباعة للتشخيص

            if not rows:
                print("لا توجد بيانات للعرض، سيتم إنشاء نموذج فارغ.") # طباعة للتشخيص
                headers = ["رت", "الاسم والنسب", "رمز التلميذ",
                           "الأسبوع الأول", "الأسبوع الثاني", "الأسبوع الثالث",
                           "الأسبوع الرابع", "الأسبوع الخامس", "ملاحظات"]
                self.model = EditableTableModel([], headers)
                print("تم تعيين نموذج فارغ للجدول.") # طباعة للتشخيص
                self.table_view.setModel(self.model)
                # تعيين المندوب المخصص لعرض رمز التلميذ كارتباط تشعبي
                self.table_view.setItemDelegate(StudentCodeDelegate(self.table_view))
                print("تم تعيين StudentCodeDelegate للجدول الفارغ.") # طباعة للتشخيص
                # تعيين عرض الأعمدة الثابت (نبدأ من العمود 1 لأن العمود 0 مخفي)
                fixed_widths = [60, 170, 120, 120, 120, 120, 120, 120, 120]
                for col, width in enumerate(fixed_widths):
                    self.table_view.setColumnWidth(col, width)
                self.table_view.setLayoutDirection(Qt.RightToLeft)
                conn.close()
                print("اكتمل تحديث النموذج (فارغ).") # طباعة للتشخيص

                # إعادة المؤشر إلى الوضع الطبيعي
                self.setCursor(QCursor(Qt.ArrowCursor))
                return

            # استخدام عمود "بداية الشهر" (الفهرس 4) لحساب تواريخ الأسابيع
            start_date_str = rows[0][4]
            if not start_date_str:
                start_date_str = "01-01-2023"
            week_dates = calc_week_dates(start_date_str)
            # الآن نحتفظ بعمود id (الفهرس 0) ولكن نخفي عمود "بداية الشهر" (الفهرس 4)
            for row in rows:
                del row[4]  # حذف عمود "بداية الشهر" فقط
            # بناء العناوين الجديدة مع تواريخ الأسابيع
            headers = [
                "id",  # عمود مخفي
                "رت",
                "الاسم والنسب",
                "رمز التلميذ"
            ]
            # إضافة تواريخ الأسابيع كعناوين مع أسماء الأسابيع التقليدية
            for i, week_date in enumerate(week_dates):
                if (week_date):  # إذا كان هناك تاريخ
                    headers.append(f"الأسبوع {i+1}\n{week_date}")
                else:
                    headers.append(f"الأسبوع {i+1}")
            # إضافة بقية العناوين
            headers.extend([
                "المجموع الشهري",
                "الغياب المبرر",
                "ملاحظات"
            ])

            # تعيين النموذج بعد تأخير قصير لتحسين الأداء
            from PyQt5.QtCore import QTimer
            QTimer.singleShot(10, lambda: self._set_model_with_data(rows, headers, conn))

        except Exception as e:
            self.show_message("خطأ", f"حدث خطأ أثناء تحديث النموذج:\n{e}", QMessageBox.Critical)
            import traceback
            traceback.print_exc()

            # إعادة المؤشر إلى الوضع الطبيعي
            from PyQt5.QtCore import Qt
            from PyQt5.QtGui import QCursor
            self.setCursor(QCursor(Qt.ArrowCursor))

    def _set_model_with_data(self, rows, headers, conn):
        """تعيين النموذج بالبيانات بعد تأخير قصير"""
        try:
            # استيراد Qt في بداية الدالة
            from PyQt5.QtCore import Qt
            from PyQt5.QtGui import QCursor

            # إنشاء النموذج وتعيينه للجدول
            self.model = EditableTableModel(rows, headers)
            self.table_view.setModel(self.model)
            print("تم تعيين النموذج للجدول بنجاح.") # طباعة للتشخيص

            # --- استدعاء update_thresholds بعد تعيين النموذج ---
            self.update_thresholds()
            print(f"تم تحديث العتبات من DB: أسبوعي={self.model.threshold}, شهري={self.model.monthly_threshold}")

            # تعيين المندوب المخصص لعرض رمز التلميذ كارتباط تشعبي
            self.table_view.setItemDelegate(StudentCodeDelegate(self.table_view))
            print("تم تعيين StudentCodeDelegate للجدول.") # طباعة للتشخيص

            # إخفاء عمود id
            self.table_view.setColumnHidden(0, True)

            # تعيين عرض الأعمدة الثابت (نبدأ من العمود 1 لأن العمود 0 مخفي)
            fixed_widths = [60, 170, 120, 120, 120, 120, 120, 120, 120, 120, 120, 200]
            for i, width in enumerate(fixed_widths):
                if i+1 < self.table_view.model().columnCount():
                    self.table_view.setColumnWidth(i+1, width)
            self.table_view.setLayoutDirection(Qt.RightToLeft)

            # إغلاق الاتصال بقاعدة البيانات
            if conn:
                conn.close()

            print("اكتمل تحديث النموذج (مع بيانات).") # طباعة للتشخيص

            # إعادة المؤشر إلى الوضع الطبيعي
            self.setCursor(QCursor(Qt.ArrowCursor))

        except Exception as e:
            self.show_message("خطأ", f"حدث خطأ أثناء تعيين النموذج بالبيانات:\n{e}", QMessageBox.Critical)
            import traceback
            traceback.print_exc()

            # إعادة المؤشر إلى الوضع الطبيعي
            try:
                from PyQt5.QtCore import Qt
                from PyQt5.QtGui import QCursor
                self.setCursor(QCursor(Qt.ArrowCursor))
            except Exception:
                pass  # تجاهل أي خطأ في إعادة المؤشر

    def insertData(self):
        """
        دالة لإدخال البيانات في جدول مسك_الغياب_الأسبوعي.
        يتم جلب السنة الدراسية من جدول بيانات_المؤسسة،
        واختيار 10 سجلات من جدول جدولة_مسك_الغياب (مرتبة حسب الشهر)
        لكل سجل من جدول اللوائح (بعد تصفية بالسنة) باستخدام Cross Join،
        مع إدخال قيم فارغة للأعمدة "1" إلى "5" وعمود الملاحظات.
        """
        try:
            conn = sqlite3.connect(get_database_path())
            cur = conn.cursor()
            cur.execute("SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
            res = cur.fetchone()
            if not res:
                self.show_message("خطأ", "لا توجد بيانات في جدول بيانات_المؤسسة", QMessageBox.Warning)
                return
            year = res[0]
            insert_query = """
            WITH limited_schedule AS (
                SELECT *
                FROM (
                    SELECT j.*, ROW_NUMBER() OVER (ORDER BY j.الشهر) as rn
                    FROM جدولة_مسك_الغياب j
                    WHERE j.السنة_الدراسية = ?
                ) sub
                WHERE rn <= 10
            )
            INSERT OR IGNORE INTO مسك_الغياب_الأسبوعي
                (السنة_الدراسية, الأسدس, الشهر, "1", "2", "3", "4", "5", بداية_الشهر, رمز_التلميذ, ملاحظات)
            SELECT
                ls.السنة_الدراسية,
                ls.الأسدس,
                ls.الشهر,
                '', '', '', '', '',
                ls.بداية_الشهر,
                l.الرمز,
                ''
            FROM limited_schedule ls
            CROSS JOIN (SELECT * FROM اللوائح WHERE السنة_الدراسية = ?) l
            """
            cur.execute(insert_query, (year, year))
            conn.commit()
            self.show_message("نجاح", "تم إدخال البيانات في جدول مسك_الغياب_الأسبوعي بنجاح!")
        except Exception as e:
            self.show_message("خطأ", f"حدث خطأ أثناء الإدخال:\n{e}", QMessageBox.Critical)
        finally:
            conn.close()
            self.updateModel()

    def ensure_monthly_total_column(self):
        """إضافة عمود مجموع_شهري وعمود الغياب_المبرر إذا لم يكونا موجودين"""
        try:
            conn = sqlite3.connect(get_database_path())
            cur = conn.cursor()
            # إضافة عمود مجموع_شهري إذا لم يكن موجوداً
            try:
                cur.execute("""
                    ALTER TABLE مسك_الغياب_الأسبوعي
                    ADD COLUMN مجموع_شهري TEXT
                """)
            except sqlite3.OperationalError as e:
                if "duplicate column name" not in str(e).lower():
                    print(f"خطأ في إضافة عمود مجموع_شهري: {e}")
            # إضافة عمود الغياب_المبرر إذا لم يكن موجوداً
            try:
                cur.execute("""
                    ALTER TABLE مسك_الغياب_الأسبوعي
                    ADD COLUMN الغياب_المبرر TEXT DEFAULT '0'
                """)
                print("تم إضافة عمود الغياب_المبرر بنجاح")
            except sqlite3.OperationalError as e:
                if "duplicate column name" not in str(e).lower():
                    print(f"خطأ في إضافة عمود الغياب_المبرر: {e}")
            conn.commit()
        except Exception as e:
            print(f"خطأ في تعديل الجدول: {e}")
        finally:
            conn.close()

    def ensure_unjustified_absence_column(self):
        """إضافة عمود غياب_غير_مبرر إذا لم يكن موجوداً"""
        try:
            conn = sqlite3.connect(get_database_path())
            cur = conn.cursor()
            # إضافة عمود غياب_غير_مبرر إذا لم يكن موجوداً
            try:
                cur.execute("""
                    ALTER TABLE مسك_الغياب_الأسبوعي
                    ADD COLUMN غياب_غير_مبرر TEXT DEFAULT '0'
                """)
                print("تم إضافة عمود غياب_غير_مبرر بنجاح")
            except sqlite3.OperationalError as e:
                if "duplicate column name" not in str(e).lower():
                    print(f"خطأ في إضافة عمود غياب_غير_مبرر: {e}")
            conn.commit()
        except Exception as e:
            print(f"خطأ في تعديل الجدول: {e}")
        finally:
            conn.close()

    def saveData(self):
        """دالة لحفظ التغييرات في قاعدة البيانات وتحديث المجموع الشهري والغياب المبرر وغير المبرر"""
        print("بدء تنفيذ دالة saveData...")
        try:
            # التأكد من وجود عمود مجموع_شهري والغياب_المبرر
            print("التأكد من وجود عمود مجموع_شهري والغياب_المبرر...")
            self.ensure_monthly_total_column()
            # التأكد من وجود عمود غياب_غير_مبرر
            print("التأكد من وجود عمود غياب_غير_مبرر...")
            self.ensure_unjustified_absence_column()

            conn = sqlite3.connect(get_database_path())
            cur = conn.cursor()
            conn.execute("BEGIN TRANSACTION")
            print("بدء معاملة قاعدة البيانات...")

            # تشخيص: التحقق من وجود بيانات في النموذج
            print(f"عدد الصفوف في النموذج: {self.model.rowCount()}")

            updates = []
            for row in range(self.model.rowCount()):
                # استخراج معرف id من العمود المخفي (الفهرس 0)
                record_id = self.model.data(self.model.index(row, 0))
                if not record_id:
                    print(f"تخطي الصف {row} لأن معرف السجل غير موجود")
                    continue

                # تشخيص: عرض معرف السجل
                print(f"معالجة السجل ذو المعرف: {record_id}")

                # استخراج قيم الأسابيع
                week1 = str(self.model.data(self.model.index(row, 4)) or '0')
                week2 = str(self.model.data(self.model.index(row, 5)) or '0')
                week3 = str(self.model.data(self.model.index(row, 6)) or '0')
                week4 = str(self.model.data(self.model.index(row, 7)) or '0')
                week5 = str(self.model.data(self.model.index(row, 8)) or '0')
                justified = str(self.model.data(self.model.index(row, 10)) or '0')  # الغياب المبرر
                notes = str(self.model.data(self.model.index(row, 11)) or '')  # الملاحظات (تحديث الفهرس)

                # تشخيص: عرض قيم الأسابيع
                print(f"  الأسبوع 1: '{week1}', الأسبوع 2: '{week2}', الأسبوع 3: '{week3}', الأسبوع 4: '{week4}', الأسبوع 5: '{week5}'")
                print(f"  الغياب المبرر: '{justified}', الملاحظات: '{notes}'")

                # حساب المجموع الشهري - تحويل كل قيمة إلى نص أولاً ثم تنظيفها وتحويلها لرقم
                weekly_values = [week1, week2, week3, week4, week5]
                monthly_total = sum(int(str(x).strip() or '0') for x in weekly_values)
                print(f"  المجموع الشهري المحسوب: {monthly_total}")

                # حساب الغياب غير المبرر (مجموع_شهري ناقص الغياب_المبرر)
                justified_value = int(justified.strip() or '0')
                unjustified_absence = max(0, monthly_total - justified_value)
                print(f"  الغياب غير المبرر المحسوب: {unjustified_absence}")

                # تجميع البيانات للتحديث
                updates.append((
                    week1.strip(), week2.strip(), week3.strip(), week4.strip(), week5.strip(),
                    str(monthly_total), justified.strip(), str(unjustified_absence), notes.strip(), record_id
                ))

                # تشخيص: عرض البيانات المجمعة للتحديث
                if row < 3:  # عرض أول 3 صفوف فقط للتشخيص
                    print(f"  البيانات المجمعة للتحديث: {updates[-1]}")

            if not updates:
                print("تنبيه: لا توجد بيانات للحفظ")
                self.show_message("تنبيه", "لا توجد بيانات للحفظ", QMessageBox.Warning)
                return

            # تشخيص: عرض عدد التحديثات
            print(f"عدد التحديثات المطلوبة: {len(updates)}")

            # تحديث البيانات
            update_query = """
            UPDATE مسك_الغياب_الأسبوعي
            SET "1" = ?,
                "2" = ?,
                "3" = ?,
                "4" = ?,
                "5" = ?,
                مجموع_شهري = ?,
                الغياب_المبرر = ?,
                غياب_غير_مبرر = ?,
                ملاحظات = ?
            WHERE id = ?
            """
            print(f"استعلام التحديث: {update_query}")

            # تشخيص: التحقق من وجود عمود id في جدول مسك_الغياب_الأسبوعي
            cur.execute("PRAGMA table_info(مسك_الغياب_الأسبوعي)")
            columns = [info[1] for info in cur.fetchall()]
            print(f"أعمدة جدول مسك_الغياب_الأسبوعي: {columns}")

            # تنفيذ التحديثات
            print("تنفيذ التحديثات...")
            cur.executemany(update_query, updates)

            # تشخيص: التحقق من عدد الصفوف المتأثرة
            print(f"عدد الصفوف المتأثرة: {cur.rowcount}")

            conn.commit()
            print("تم الالتزام بالتغييرات في قاعدة البيانات")

            self.show_message("نجاح", f"تم حفظ {len(updates)} سجل بنجاح!")
            print(f"تم عرض رسالة نجاح للمستخدم")

            # تحديث عرض الجدول فوراً
            print("تحديث عرض الجدول...")
            self.updateModel()
            print("تم تحديث عرض الجدول بنجاح")

        except Exception as e:
            if conn:
                conn.rollback()
                print("تم التراجع عن التغييرات بسبب حدوث خطأ")
            self.show_message("خطأ", f"حدث خطأ أثناء حفظ البيانات:\n{str(e)}", QMessageBox.Critical)
            print(f"خطأ أثناء حفظ البيانات: {e}")
            import traceback
            traceback.print_exc()
        finally:
            if conn:
                conn.close()
                print("تم إغلاق الاتصال بقاعدة البيانات")

    def update_threshold(self):
        """تحديث عتبة الغياب وإعادة رسم الجدول"""
        try:
            threshold_value = int(self.threshold_box.text() or 0)
            if hasattr(self, 'model'):
                self.model.threshold = threshold_value
                # إصدار إشارة تحديث لكل الخلايا من الأسبوع 1 إلى المجموع الشهري
                self.model.dataChanged.emit(
                    self.model.index(0, 4),  # بداية من أول خلية في الأسبوع الأول
                    self.model.index(self.model.rowCount() - 1, 9)  # نهاية بآخر خلية في المجموع الشهري
                )
        except ValueError:
            if hasattr(self, 'model'):
                self.model.threshold = 0
                self.model.dataChanged.emit(
                    self.model.index(0, 4),
                    self.model.index(self.model.rowCount() - 1, 9)
                )

    def update_monthly_threshold(self):
        """تحديث عتبة الغياب الشهري وإعادة رسم الجدول"""
        try:
            threshold_value = int(self.monthly_threshold_box.text() or 0)
            if hasattr(self, 'model'):
                self.model.monthly_threshold = threshold_value
                # إصدار إشارة تحديث لكل الخلايا من الأسبوع 1 إلى المجموع الشهري
                self.model.dataChanged.emit(
                    self.model.index(0, 4),
                    self.model.index(self.model.rowCount() - 1, 9)
                )
        except ValueError:
            if hasattr(self, 'model'):
                self.model.monthly_threshold = 0
                self.model.dataChanged.emit(
                    self.model.index(0, 4),
                    self.model.index(self.model.rowCount() - 1, 9)
                )

    def save_threshold(self, value):
        """حفظ قيمة عتبة الغياب الأسبوعي"""
        self.settings.setValue('weekly_threshold', value)
        try:
            threshold_value = int(value or 0)
            if hasattr(self, 'model'):
                self.model.threshold = threshold_value
                self.model.layoutChanged.emit()
        except ValueError:
            if hasattr(self, 'model'):
                self.model.threshold = 0
                self.model.layoutChanged.emit()

    def save_monthly_threshold(self, value):
        """حفظ قيمة عتبة الغياب الشهري"""
        self.settings.setValue('monthly_threshold', value)
        try:
            threshold_value = int(value or 0)
            if hasattr(self, 'model'):
                self.model.monthly_threshold = threshold_value
                self.model.layoutChanged.emit()
        except ValueError:
            if hasattr(self, 'model'):
                self.model.monthly_threshold = 0
                self.model.layoutChanged.emit()

    def closeEvent(self, event):
        """حفظ الإعدادات عند إغلاق النافذة"""
        self.settings.sync()
        super().closeEvent(event)

    def show_message(self, title, text, icon=QMessageBox.Information):
        """عرض رسالة للمستخدم بالنمط المميز"""
        try:
            # استيراد الكلاسات المطلوبة
            from PyQt5.QtWidgets import QStyle, QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton
            from PyQt5.QtGui import QIcon, QPixmap, QFont
            from PyQt5.QtCore import Qt

            # تحديد نوع الرسالة ولونها
            if icon == QMessageBox.Critical:
                bg_color = "#fff0f0"
                border_color = "#e74c3c"
                title_color = "#c0392b"
                icon_style = QStyle.SP_MessageBoxCritical
                icon_text = "✕"
                icon_color = "#e74c3c"
            elif icon == QMessageBox.Warning:
                bg_color = "#fffbe6"
                border_color = "#f39c12"
                title_color = "#d35400"
                icon_style = QStyle.SP_MessageBoxWarning
                icon_text = "⚠"
                icon_color = "#f39c12"
            else:  # Information
                bg_color = "#f0f8ff"
                border_color = "#3498db"
                title_color = "#2980b9"
                icon_style = QStyle.SP_MessageBoxInformation
                icon_text = "i"
                icon_color = "#3498db"

            # إنشاء نافذة حوار جديدة
            msg_dialog = QDialog(self)
            msg_dialog.setWindowTitle(title)
            msg_dialog.setFixedSize(500, 300)
            msg_dialog.setLayoutDirection(Qt.RightToLeft)

            # إضافة أيقونة البرنامج
            try:
                app_icon = QIcon("01.ico")
                msg_dialog.setWindowIcon(app_icon)
            except Exception as e:
                print(f"خطأ في تحميل أيقونة البرنامج: {e}")

            # تنسيق النافذة
            msg_dialog.setStyleSheet(f"""
                QDialog {{
                    background-color: {bg_color};
                    border: 2px solid {border_color};
                    border-radius: 10px;
                }}
                QLabel {{
                    color: #333333;
                    font-weight: bold;
                }}
                QLabel#icon_label {{
                    padding: 10px;
                }}
                QLabel#message_label {{
                    background-color: white;
                    border: 1px solid {border_color};
                    border-radius: 5px;
                    padding: 15px;
                    font-size: 14pt;
                }}
                QPushButton {{
                    background-color: {border_color};
                    color: white;
                    border-radius: 5px;
                    padding: 8px 15px;
                    font-weight: bold;
                    min-height: 35px;
                    min-width: 120px;
                }}
                QPushButton:hover {{
                    background-color: {title_color};
                    border: 2px solid {border_color};
                }}
            """)

            # إنشاء تخطيط النافذة
            layout = QVBoxLayout(msg_dialog)
            layout.setContentsMargins(20, 20, 20, 20)
            layout.setSpacing(15)

            # إضافة أيقونة وعنوان
            header_layout = QHBoxLayout()

            # محاولة إضافة أيقونة الرسالة
            icon_label = QLabel()
            icon_label.setObjectName("icon_label")
            icon_label.setAlignment(Qt.AlignCenter)
            try:
                # محاولة استخدام أيقونة الرسالة القياسية
                msg_icon = self.style().standardPixmap(icon_style)
                msg_icon = QPixmap(msg_icon).scaled(48, 48, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                icon_label.setPixmap(msg_icon)
            except Exception:
                # استخدام نص بديل في حالة الفشل
                icon_label.setText(icon_text)
                icon_label.setFont(QFont("Arial", 24, QFont.Bold))
                icon_label.setStyleSheet(f"color: {icon_color};")

            header_layout.addWidget(icon_label)

            # إضافة عنوان النافذة
            title_label = QLabel(title)
            title_label.setFont(QFont("Calibri", 16, QFont.Bold))
            title_label.setStyleSheet(f"color: {title_color};")
            title_label.setAlignment(Qt.AlignCenter)
            header_layout.addWidget(title_label, 1)

            layout.addLayout(header_layout)

            # إضافة رسالة المعلومات
            message_label = QLabel(text)
            message_label.setObjectName("message_label")
            message_label.setFont(QFont("Calibri", 13))
            message_label.setAlignment(Qt.AlignCenter)
            message_label.setWordWrap(True)
            layout.addWidget(message_label)

            # إضافة زر الموافقة
            button_layout = QHBoxLayout()
            ok_button = QPushButton("موافق")
            ok_button.setFont(QFont("Calibri", 12, QFont.Bold))
            ok_button.setCursor(Qt.PointingHandCursor)
            ok_button.clicked.connect(msg_dialog.accept)
            button_layout.addWidget(ok_button)

            layout.addLayout(button_layout)

            # عرض النافذة
            return msg_dialog.exec_()

        except Exception as e:
            print(f"خطأ في عرض الرسالة المخصصة: {e}")
            import traceback
            traceback.print_exc()
            # استخدام الطريقة التقليدية في حالة الفشل
            msg = QMessageBox(self)
            msg.setWindowTitle(title)
            msg.setText(text)
            msg.setIcon(icon)
            return msg.exec_()

    def show_info_message(self, title, message):
        """عرض رسالة معلومات بالنمط المميز"""
        try:
            # استيراد الكلاسات المطلوبة
            from PyQt5.QtWidgets import QStyle, QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton
            from PyQt5.QtGui import QIcon, QPixmap, QFont
            from PyQt5.QtCore import Qt

            # إنشاء نافذة حوار جديدة
            info_dialog = QDialog(self)
            info_dialog.setWindowTitle(title)
            info_dialog.setFixedSize(500, 300)
            info_dialog.setLayoutDirection(Qt.RightToLeft)

            # إضافة أيقونة البرنامج
            try:
                app_icon = QIcon("01.ico")
                info_dialog.setWindowIcon(app_icon)
            except Exception as e:
                print(f"خطأ في تحميل أيقونة البرنامج: {e}")

            # تنسيق النافذة
            info_dialog.setStyleSheet("""
                QDialog {
                    background-color: #f0f8ff;
                    border: 2px solid #3498db;
                    border-radius: 10px;
                }
                QLabel {
                    color: #333333;
                    font-weight: bold;
                }
                QLabel#icon_label {
                    padding: 10px;
                }
                QLabel#message_label {
                    background-color: #e8f4fc;
                    border: 1px solid #3498db;
                    border-radius: 5px;
                    padding: 15px;
                    font-size: 14pt;
                }
                QPushButton {
                    border-radius: 5px;
                    padding: 8px 15px;
                    font-weight: bold;
                    min-height: 35px;
                    min-width: 120px;
                }
                QPushButton#preview_btn {
                    background-color: #4CAF50;
                    color: white;
                }
                QPushButton#preview_btn:hover {
                    background-color: #45a049;
                    border: 2px solid #4CAF50;
                }
                QPushButton#close_btn {
                    background-color: #3498db;
                    color: white;
                }
                QPushButton#close_btn:hover {
                    background-color: #2980b9;
                    border: 2px solid #3498db;
                }
            """)

            # إنشاء تخطيط النافذة
            layout = QVBoxLayout(info_dialog)
            layout.setContentsMargins(20, 20, 20, 20)
            layout.setSpacing(15)

            # إضافة أيقونة وعنوان
            header_layout = QHBoxLayout()

            # محاولة إضافة أيقونة المعلومات
            icon_label = QLabel()
            icon_label.setObjectName("icon_label")
            icon_label.setAlignment(Qt.AlignCenter)
            try:
                # محاولة استخدام أيقونة المعلومات القياسية
                info_icon = self.style().standardPixmap(QStyle.SP_MessageBoxInformation)
                info_icon = QPixmap(info_icon).scaled(48, 48, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                icon_label.setPixmap(info_icon)
            except Exception:
                # استخدام نص بديل في حالة الفشل
                icon_label.setText("i")
                icon_label.setFont(QFont("Arial", 24, QFont.Bold))
                icon_label.setStyleSheet("color: #3498db;")

            header_layout.addWidget(icon_label)

            # إضافة عنوان النافذة
            title_label = QLabel(title)
            title_label.setFont(QFont("Calibri", 16, QFont.Bold))
            title_label.setStyleSheet("color: #2980b9;")
            title_label.setAlignment(Qt.AlignCenter)
            header_layout.addWidget(title_label, 1)

            layout.addLayout(header_layout)

            # إضافة رسالة المعلومات
            message_label = QLabel(message)
            message_label.setObjectName("message_label")
            message_label.setFont(QFont("Calibri", 13))
            message_label.setAlignment(Qt.AlignCenter)
            message_label.setWordWrap(True)
            layout.addWidget(message_label)

            # استخراج مسار الملف من الرسالة
            import re
            file_path = ""
            match = re.search(r"اسم الملف: (.+)$", message, re.MULTILINE)
            if match:
                file_name = match.group(1)
                folder_match = re.search(r"في مجلد: (.+)$", message, re.MULTILINE)
                if folder_match:
                    folder_name = folder_match.group(1)
                    file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), folder_name, file_name)

            # إضافة أزرار التحكم
            button_layout = QHBoxLayout()

            # إضافة زر المعاينة إذا كان هناك ملف
            if file_path:
                preview_button = QPushButton("معاينة")
                preview_button.setObjectName("preview_btn")
                preview_button.setFont(QFont("Calibri", 12, QFont.Bold))
                preview_button.setCursor(Qt.PointingHandCursor)
                preview_button.clicked.connect(lambda: self._open_file(file_path))
                button_layout.addWidget(preview_button)

            # إضافة زر الإغلاق
            close_button = QPushButton("إغلاق")
            close_button.setObjectName("close_btn")
            close_button.setFont(QFont("Calibri", 12, QFont.Bold))
            close_button.setCursor(Qt.PointingHandCursor)
            close_button.clicked.connect(info_dialog.accept)
            button_layout.addWidget(close_button)

            layout.addLayout(button_layout)

            # عرض النافذة
            info_dialog.exec_()
            return True

        except Exception as e:
            print(f"خطأ في عرض رسالة المعلومات المخصصة: {e}")
            import traceback
            traceback.print_exc()
            # استخدام الطريقة التقليدية في حالة الفشل
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle(title)
            msg_box.setText(message)
            msg_box.setIcon(QMessageBox.Information)

            # إضافة زر معاينة وزر إغلاق
            preview_button = QPushButton("معاينة")
            preview_button.setCursor(Qt.PointingHandCursor)
            preview_button.setStyleSheet("background-color: #4CAF50; color: white;")

            close_button = QPushButton("إغلاق")
            close_button.setCursor(Qt.PointingHandCursor)
            close_button.setStyleSheet("background-color: #2196F3; color: white;")

            msg_box.addButton(close_button, QMessageBox.AcceptRole)
            msg_box.addButton(preview_button, QMessageBox.ActionRole)

            # استخراج مسار الملف من الرسالة
            import re
            file_path = ""
            match = re.search(r"اسم الملف: (.+)$", message, re.MULTILINE)
            if match:
                file_name = match.group(1)
                folder_match = re.search(r"في مجلد: (.+)$", message, re.MULTILINE)
                if folder_match:
                    folder_name = folder_match.group(1)
                    file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), folder_name, file_name)

            # تنفيذ الإجراء عند النقر على الزر
            result = msg_box.exec_()
            clicked_button = msg_box.clickedButton()

            if clicked_button == preview_button and file_path and os.path.exists(file_path):
                try:
                    os.startfile(file_path)
                except Exception as e:
                    self.show_message("خطأ", f"تعذر فتح الملف: {e}", QMessageBox.Critical)

    def _open_file(self, file_path):
        """فتح ملف باستخدام البرنامج الافتراضي"""
        if file_path and os.path.exists(file_path):
            try:
                os.startfile(file_path)
            except Exception as e:
                self.show_message("خطأ", f"تعذر فتح الملف: {e}", QMessageBox.Critical)

    def init_ui(self):
        """إنشاء واجهة المستخدم بتصميم عصري"""
        # إنشاء إطار رئيسي مع تأثير الظل
        content_frame = QFrame(self)
        # تعديل الأبعاد لتكون 90% من حجم النافذة
        content_frame.setGeometry(int(self.width() * 0.05), int(self.height() * 0.05),
                                int(self.width() * 0.9), int(self.height() * 0.9))
        content_frame.setFrameShape(QFrame.NoFrame)
        content_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 15px;
            }
        """)
        # إضافة تأثير الظل
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(15)
        shadow.setXOffset(5)
        shadow.setYOffset(5)
        shadow.setColor(QColor(0, 0, 0, 150))
        content_frame.setGraphicsEffect(shadow)
        # تعديل أبعاد الجداول لتملأ المساحة المتاحة
        table_x = int(content_frame.width() * 0.05)
        table_width = int(content_frame.width() * 0.9)
        # تعديل عرض وموقع الجداول
        self.general_record_table.setGeometry(table_x, 80, table_width, 300)
        self.lists_table.setGeometry(table_x, 400, table_width, 250)

    def apply_styles(self):
        """تطبيق تصميم احترافي حديث مشابه لـ sub252_window.py"""
        # تطبيق نمط احترافي للنافذة الرئيسية
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f8f9fc,
                    stop: 1 #e9ecef
                );
            }
            
            QGroupBox {
                background-color: white;
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                margin-top: 15px;
                font-family: 'Calibri';
                font-size: 14pt;
                font-weight: bold;
                padding: 10px;
            }
            QGroupBox::title {
                color: #2c3e50;
                subcontrol-position: top center;
                padding: 8px 15px;
                background-color: white;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: -10px;
            }
            
            /* تنسيق مربعات التحرير والسرد (QComboBox) */
            QComboBox {
                background-color: white;
                color: #2c3e50;
                border: 2px solid #ced4da;
                border-radius: 5px;
                padding: 8px 12px;
                font-family: 'Calibri';
                font-size: 13pt;
                font-weight: bold;
                min-height: 20px;
                selection-background-color: #007bff;
                selection-color: white;
            }

            QComboBox:hover {
                border-color: #007bff;
                background-color: #f8f9fa;
            }

            QComboBox:focus {
                border: 2px solid #007bff;
                background-color: #e7f3ff;
            }

            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 25px;
                border-left: 2px solid #ced4da;
                border-top-right-radius: 5px;
                border-bottom-right-radius: 5px;
                background-color: #f8f9fa;
            }

            QComboBox::down-arrow {
                image: none;
                border-left: 6px solid transparent;
                border-right: 6px solid transparent;
                border-top: 6px solid #495057;
                margin-right: 8px;
            }
            
            QComboBox::down-arrow:hover {
                border-top-color: #007bff;
            }
            
            /* تنسيق الأزرار */
            QPushButton {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #007bff,
                    stop: 1 #0056b3
                );
                color: white;
                border: 2px solid #0056b3;
                border-radius: 8px;
                padding: 10px 20px;
                min-height: 35px;
                font-family: 'Calibri';
                font-size: 13pt;
                font-weight: bold;
            }

            QPushButton:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #0056b3,
                    stop: 1 #004085
                );
                border-color: #004085;
                transform: translateY(-1px);
            }

            QPushButton:pressed {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #004085,
                    stop: 1 #002752
                );
                border-color: #002752;
                transform: translateY(1px);
            }
            
            /* أزرار خاصة بألوان مختلفة */
            QPushButton#saveButton {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #28a745,
                    stop: 1 #1e7e34
                );
                border-color: #1e7e34;
            }
            
            QPushButton#saveButton:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #1e7e34,
                    stop: 1 #155724
                );
                border-color: #155724;
            }
            
            QPushButton#reportButton {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #6f42c1,
                    stop: 1 #5a32a3
                );
                border-color: #5a32a3;
            }
            
            QPushButton#reportButton:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #5a32a3,
                    stop: 1 #4c2a85
                );
                border-color: #4c2a85;
            }
            
            /* تنسيق مربعات النص */
            QLineEdit {
                background-color: white;
                border: 2px solid #ced4da;
                border-radius: 5px;
                padding: 8px 12px;
                font-family: 'Calibri';
                font-size: 13pt;
                color: #2c3e50;
            }
            
            QLineEdit:focus {
                border-color: #007bff;
                background-color: #e7f3ff;
            }
            
            /* تنسيق التسميات */
            QLabel {
                color: #2c3e50;
                font-family: 'Calibri';
                font-size: 13pt;
                font-weight: bold;
            }
        """)
        
        # تطبيق تنسيق خاص للجدول
        self.table_view.setStyleSheet("""
            QTableView {
                background-color: white;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                gridline-color: #dee2e6;
                selection-background-color: #e3f2fd;
                selection-color: #1976d2;
                font-family: 'Calibri';
                font-size: 12pt;
                alternate-background-color: #f8f9fa;
            }
            
            QTableView::item {
                padding: 8px;
                border-bottom: 1px solid #dee2e6;
            }
            
            QTableView::item:selected {
                background-color: #e3f2fd;
                color: #1976d2;
            }
            
            QTableView::item:hover {
                background-color: #f0f8ff;
            }
        """)
        
        # تطبيق تنسيق احترافي لرأس الجدول
        self.table_view.horizontalHeader().setStyleSheet("""
            QHeaderView::section {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #e3f2fd,
                    stop: 1 #bbdefb
                );
                color: #0d47a1;
                font-weight: bold;
                font-size: 14px;
                font-family: 'Calibri';
                padding: 10px;
                border: 1px solid #90caf9;
                text-align: center;
                min-height: 35px;
            }
            QHeaderView::section:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #bbdefb,
                    stop: 1 #90caf9
                );
                color: #1565c0;
            }
        """)
        
        print("تم تطبيق التصميم الاحترافي الجديد مشابه لـ sub252_window.py")

    def printAbsenceReport(self):
        """طباعة تقرير الغياب للقسم والشهر المحددين"""
        print("بدء تنفيذ دالة printAbsenceReport...")
        section = self.combo_qism.currentText()
        month = self.combo_shahr.currentText()
        print(f"القسم المحدد: {section}, الشهر المحدد: {month}")

        # التحقق من تحديد المستوى والقسم
        if not self.combo_mustawa.currentText():
            print("تنبيه: لم يتم تحديد المستوى")
            self.show_message("تنبيه", "الرجاء تحديد المستوى أولاً.", QMessageBox.Warning)
            return

        if not section:
            print("تنبيه: لم يتم تحديد القسم")
            self.show_message("تنبيه", "الرجاء تحديد القسم.", QMessageBox.Warning)
            return

        if not month:
            print("تنبيه: لم يتم تحديد الشهر")
            self.show_message("تنبيه", "الرجاء تحديد الشهر.", QMessageBox.Warning)
            return

        try:
            # الحصول على مسار سطح المكتب
            import os
            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")

            # إنشاء مسار المجلد الرئيسي على سطح المكتب
            main_folder = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
            if not os.path.exists(main_folder):
                os.makedirs(main_folder)
                print(f"تم إنشاء المجلد الرئيسي: {main_folder}")

            # إنشاء مجلد تقارير الغياب داخل المجلد الرئيسي
            reports_folder = os.path.join(main_folder, "تقارير الغياب")
            if not os.path.exists(reports_folder):
                os.makedirs(reports_folder)
                print(f"تم إنشاء مجلد تقارير الغياب: {reports_folder}")

            # عرض رسالة نجاح محسنة قبل فتح التقرير
            self.show_custom_success_message(
                "جاري إنشاء التقرير",
                f"جاري إنشاء تقرير الغياب الشهري للقسم:\n"
                f"{section} - الشهر: {month}\n\n"
                f"سيتم حفظ التقرير في المسار:\n"
                f"سطح المكتب/تقارير برنامج المعين في الحراسة العامة/تقارير الغياب"
            )

            # استيراد نافذة تقرير الغياب من ملف second_semester_helper.py
            print("محاولة استيراد YearlyAbsenceSummaryWindow من second_semester_helper...")
            try:
                from second_semester_helper import YearlyAbsenceSummaryWindow
                print("تم استيراد YearlyAbsenceSummaryWindow بنجاح")
            except ImportError as e:
                # بدلاً من رفع استثناء، نعرض رسالة للمستخدم ونعود من الدالة
                print(f"خطأ استيراد: {e}")
                QMessageBox.warning(self, "وحدة مفقودة",
                                   "لم يتم العثور على وحدة 'second_semester_helper'. "
                                   "يرجى التأكد من وجودها في نفس المجلد أو تحديث PYTHONPATH.")
                print("تحذير: لم يتم العثور على وحدة 'second_semester_helper'.")

                # تشخيص: التحقق من وجود ملف second_semester_helper.py
                if os.path.exists("second_semester_helper.py"):
                    print("ملف second_semester_helper.py موجود في المجلد الحالي")
                else:
                    print("ملف second_semester_helper.py غير موجود في المجلد الحالي")

                return  # الخروج من الدالة بدلاً من إيقاف البرنامج

            # إنشاء نافذة جديدة لتقرير الغياب
            print("إنشاء نافذة جديدة لتقرير الغياب...")
            absence_window = YearlyAbsenceSummaryWindow()

            # تعيين القسم والشهر المحددين
            print(f"تعيين القسم '{section}' والشهر '{month}' في نافذة التقرير")
            absence_window.section_combo.setCurrentText(section)
            absence_window.month_combo.setCurrentText(month)

            # تجاوز رسائل التأكيد العادية
            try:
                # حفظ الدالة الأصلية
                original_info = QMessageBox.information

                # تعريف دالة فارغة لتجاوز عرض الرسائل
                def empty_info(*args, **kwargs):
                    return QMessageBox.Ok

                # استبدال الدالة الأصلية بالدالة الفارغة
                QMessageBox.information = empty_info

                # توليد التقرير الشهري
                print("توليد التقرير الشهري...")
                absence_window.generate_monthly_report()

                # إعادة الدالة الأصلية
                QMessageBox.information = original_info
            except Exception as e:
                print(f"خطأ أثناء محاولة تجاوز الرسائل: {e}")
                # في حالة حدوث خطأ، نستخدم الطريقة العادية
                absence_window.generate_monthly_report()

            # الحصول على التاريخ الحالي بتنسيق مناسب للملف
            current_date = datetime.datetime.now().strftime("%Y%m%d")

            # تحديد اسم الملف
            file_name = f"تقرير_الغياب_الشهري_{section}_{month}_{current_date}.pdf"
            report_path = os.path.join(reports_folder, file_name)

            # حفظ التقرير في المجلد المحدد
            if hasattr(absence_window, 'save_report_to_file'):
                absence_window.save_report_to_file(report_path)
                print(f"تم حفظ التقرير في: {report_path}")

                # فتح التقرير بعد حفظه
                try:
                    os.startfile(report_path)
                    print(f"تم فتح التقرير: {report_path}")
                except Exception as open_error:
                    print(f"خطأ في فتح التقرير: {open_error}")

            print(f"تم فتح تقرير الغياب الشهري للقسم {section} والشهر {month}")
        except Exception as e:
            import traceback
            traceback.print_exc()
            self.show_message("خطأ", f"لم يتم فتح تقرير الغياب الشهري:\n{e}", QMessageBox.Critical)
            print(f"خطأ عند فتح تقرير الغياب الشهري: {e}")

            # في حالة حدوث خطأ، نستخدم الدالة القديمة كاحتياط
            try:
                print("محاولة استخدام الدالة القديمة print_absence_report كاحتياط...")

                # تشخيص: التحقق من وجود دالة print_absence_report
                if 'print_absence_report' in globals():
                    print("دالة print_absence_report موجودة في globals()")
                else:
                    print("دالة print_absence_report غير موجودة في globals()")

                # تشخيص: التحقق من وجود النموذج
                if hasattr(self, 'model'):
                    print(f"النموذج موجود: {self.model}")
                else:
                    print("النموذج غير موجود")

                # تشخيص: طباعة معلومات عن الاستدعاء
                print(f"استدعاء print_absence_report مع المعلمات: parent={self}, section={section}, month={month}, model={self.model}")

                # استدعاء الدالة
                result = print_absence_report(self, section, month, self.model)
                print(f"نتيجة استدعاء print_absence_report: {result}")
            except Exception as e2:
                print(f"فشلت الدالة القديمة أيضًا: {e2}")
                import traceback
                traceback.print_exc()

    def update_current_month(self, new_month):
        """تحديث الشهر الحالي في قاعدة البيانات"""
        try:
            conn = sqlite3.connect(get_database_path())
            cur = conn.cursor()
            cur.execute("UPDATE اعدادات_البرنامج SET الشهر_الحالي = ?", (new_month,))
            conn.commit()
            conn.close()
        except Exception as e:
            print(f"خطأ في تحديث الشهر الحالي: {e}")

    def get_thresholds(self):
        """استرجاع عتبات الغياب من قاعدة البيانات"""
        try:
            conn = sqlite3.connect(get_database_path())
            cur = conn.cursor()
            # التحقق من وجود جدول اعدادات_البرنامج وإنشاؤه إذا لم يكن موجودًا
            cur.execute("""
                CREATE TABLE IF NOT EXISTS اعدادات_البرنامج (
                    المعرف INTEGER PRIMARY KEY,
                    الشهر_الحالي TEXT UNIQUE,
                    عتبة_الغياب_الأسبوعي INTEGER,
                    عتبة_الغياب_الشهري INTEGER,
                    نقطة_المواظبة INTEGER,
                    نقطة_السلوك INTEGER,
                    معامل_المواظبة INTEGER,
                    معامل_السلوك INTEGER,
                    المخالفة_الاولى INTEGER,
                    المخالفة_الثانية INTEGER,
                    المخالفة_الثالثة INTEGER,
                    تاريخ_التعديل DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            # التحقق من وجود سجل وإضافة سجل افتراضي إذا لم يكن موجودًا
            cur.execute("SELECT COUNT(*) FROM اعدادات_البرنامج")
            count = cur.fetchone()[0]
            if count == 0:
                # إنشاء سجل افتراضية
                cur.execute("""
                    INSERT INTO اعدادات_البرنامج (
                        الشهر_الحالي, عتبة_الغياب_الأسبوعي, عتبة_الغياب_الشهري,
                        نقطة_المواظبة, نقطة_السلوك, معامل_المواظبة,
                        معامل_السلوك, المخالفة_الاولى, المخالفة_الثانية, المخالفة_الثالثة
                    ) VALUES ('شتنبر', 3, 8, 20, 20, 2, 1, 2, 3)
                """)
                conn.commit()
            # الآن يمكننا استرجاع القيم بأمان
            cur.execute("SELECT عتبة_الغياب_الأسبوعي, عتبة_الغياب_الشهري FROM اعدادات_البرنامج LIMIT 1")
            result = cur.fetchone()
            conn.close()
            if result:
                return result[0] or 0, result[1] or 0
            return 0, 0
        except Exception as e:
            print(f"خطأ في استرجاع عتبات الغياب: {e}")
            return 0, 0

    def update_thresholds(self):
        """تحديث عتبات الغياب في النموذج"""
        weekly, monthly = self.get_thresholds()
        if hasattr(self, 'model') and self.model: # التأكد من وجود النموذج
            changed = False
            if self.model.threshold != weekly:
                self.model.threshold = weekly
                changed = True
            if self.model.monthly_threshold != monthly:
                self.model.monthly_threshold = monthly
                changed = True

            if changed:
                # استخدام layoutChanged لتحديث شامل للتلوين
                print(f"Thresholds updated in model. Emitting layoutChanged.") # تشخيص
                self.model.layoutChanged.emit()

    def openDefaultSettings(self):
        """فتح نافذة الإعدادات الافتراضية"""
        try:
            from default_settings_window import DefaultSettingsDialog
            dialog = DefaultSettingsDialog(self)
            if dialog.exec_():
                # تحديث عتبات الغياب بعد إغلاق النافذة
                self.update_thresholds()
        except Exception as e:
            self.show_message("خطأ", f"حدث خطأ أثناء فتح نافذة الإعدادات:\n{str(e)}", QMessageBox.Critical)

    def showHelpDialog(self):
        """عرض نافذة التعليمات بالنمط المميز"""
        try:
            # استيراد الكلاسات المطلوبة
            from PyQt5.QtWidgets import QStyle, QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QTextBrowser
            from PyQt5.QtGui import QIcon, QPixmap, QFont
            from PyQt5.QtCore import Qt

            # إنشاء نافذة حوار جديدة
            help_dialog = QDialog(self)
            help_dialog.setWindowTitle("تعليمات استخدام نافذة مسك الغياب")
            help_dialog.setMinimumSize(900, 700)
            help_dialog.setLayoutDirection(Qt.RightToLeft)

            # إضافة أيقونة البرنامج
            try:
                app_icon = QIcon("01.ico")
                help_dialog.setWindowIcon(app_icon)
            except Exception as e:
                print(f"خطأ في تحميل أيقونة البرنامج: {e}")

            # تنسيق النافذة بالنمط المميز
            help_dialog.setStyleSheet("""
                QDialog {
                    background-color: #f0f8ff;
                    border: 2px solid #0066cc;
                    border-radius: 10px;
                }
                QLabel {
                    color: #333333;
                    font-weight: bold;
                }
                QTextBrowser {
                    border: 1px solid #3498db;
                    border-radius: 5px;
                    padding: 10px;
                    background-color: white;
                    font-family: Calibri;
                    font-size: 14pt;
                    line-height: 1.5;
                    text-align: right;
                }
                QPushButton {
                    border-radius: 5px;
                    padding: 8px 15px;
                    font-weight: bold;
                    min-height: 35px;
                }
                QPushButton#close_btn {
                    background-color: #3498db;
                    color: white;
                }
                QPushButton#close_btn:hover {
                    background-color: #2980b9;
                    border: 2px solid #3498db;
                }
            """)

            # إنشاء تخطيط النافذة
            layout = QVBoxLayout(help_dialog)
            layout.setContentsMargins(20, 20, 20, 20)
            layout.setSpacing(15)

            # إضافة محتوى التعليمات
            text_browser = QTextBrowser()
            text_browser.setOpenExternalLinks(True)

            # محتوى التعليمات
            help_text = """
                <h2 style="color: #0066cc; text-align: center; font-family: Calibri;">دليل استخدام نافذة مسك الغياب</h2>

                <h3 style="color: #2980b9; font-family: Calibri;">نظرة عامة</h3>
                <p style="font-family: Calibri; font-size: 13pt; color: #333333;">هذه النافذة تساعدك على تسجيل ومتابعة غياب التلاميذ بطريقة منظمة وسهلة. إليك شرح مبسط لكيفية استخدام هذه النافذة:</p>

                <h3 style="color: #2980b9; font-family: Calibri;">1. اختيار البيانات الأساسية:</h3>
                <ul style="font-family: Calibri; font-size: 13pt; color: #333333;">
                    <li><b>المستوى:</b> اختر المستوى الدراسي من القائمة المنسدلة.</li>
                    <li><b>القسم:</b> بعد اختيار المستوى، ستظهر قائمة الأقسام المتاحة. اختر القسم المطلوب.</li>
                    <li><b>الشهر:</b> اختر الشهر الذي تريد تسجيل الغياب فيه.</li>
                </ul>

                <h3 style="color: #2980b9; font-family: Calibri;">2. تسجيل الغياب:</h3>
                <ul style="font-family: Calibri; font-size: 13pt; color: #333333;">
                    <li>بعد اختيار المستوى والقسم والشهر، سيظهر جدول بأسماء التلاميذ.</li>
                    <li><b>هام:</b> الغياب يُدخل بالساعة وليس باليوم. أدخل عدد ساعات الغياب لكل تلميذ في كل أسبوع.</li>
                    <li>في الأعمدة المخصصة للأسابيع (1-5)، أدخل عدد ساعات الغياب لكل تلميذ في كل أسبوع.</li>
                    <li>سيتم حساب المجموع الشهري تلقائيًا.</li>
                    <li>يمكنك أيضًا تسجيل الغياب المبرر وإضافة ملاحظات لكل تلميذ.</li>
                </ul>

                <h3 style="color: #2980b9; font-family: Calibri;">3. حفظ البيانات:</h3>
                <ul style="font-family: Calibri; font-size: 13pt; color: #333333;">
                    <li>بعد الانتهاء من تسجيل الغياب، انقر على زر "حفظ" لحفظ البيانات.</li>
                    <li>تأكد من حفظ البيانات قبل الانتقال إلى قسم أو شهر آخر.</li>
                </ul>

                <h3 style="color: #2980b9; font-family: Calibri;">4. إنشاء التقارير:</h3>
                <ul style="font-family: Calibri; font-size: 13pt; color: #333333;">
                    <li><b>تقرير الغياب الشهري:</b> لإنشاء تقرير بالغياب الشهري للقسم المحدد.</li>
                    <li><b>تقرير الأسدس الأول:</b> لإنشاء تقرير بالغياب للأسدس الأول.</li>
                    <li><b>تقرير الأسدس الثاني:</b> لإنشاء تقرير بالغياب للأسدس الثاني.</li>
                </ul>

                <h3 style="color: #2980b9; font-family: Calibri;">5. عرض بيانات غياب تلميذ معين:</h3>
                <ul style="font-family: Calibri; font-size: 13pt; color: #333333;">
                    <li>يمكنك عرض تقرير مفصل لغياب تلميذ معين على مدار السنة الدراسية بالنقر على رمز التلميذ (رقم مسار التلميذ) في العمود الأول من الجدول.</li>
                    <li>سيتم فتح نافذة جديدة تعرض تقريرًا سنويًا لغياب التلميذ المحدد، مع تفاصيل الغياب لكل شهر وكل أسبوع.</li>
                    <li>يمكنك من خلال هذا التقرير متابعة تطور غياب التلميذ على مدار العام الدراسي بشكل شامل.</li>
                </ul>

                <h3 style="color: #2980b9; font-family: Calibri;">6. حساب نقطة السلوك ونقطة المواظبة:</h3>
                <ul style="font-family: Calibri; font-size: 13pt; color: #333333;">
                    <li><b>نقطة المواظبة:</b> تُحسب كالتالي:
                        <ul>
                            <li>القيمة الأساسية لنقطة المواظبة (10 نقاط) ناقص (عدد ساعات الغياب غير المبرر مضروبة في معامل المواظبة).</li>
                            <li>مثال: إذا كانت نقطة المواظبة الأساسية = 10، ومعامل المواظبة = 2، وعدد ساعات الغياب غير المبرر = 3، فإن نقطة المواظبة = 10 - (3 × 2) = 4 نقاط.</li>
                        </ul>
                    </li>
                    <li><b>نقطة السلوك:</b> تُحسب بناءً على عدد المخالفات:
                        <ul>
                            <li>إذا لم توجد مخالفات: نقطة السلوك الأساسية (10 نقاط).</li>
                            <li>إذا كانت هناك مخالفة واحدة: نقطة السلوك الأساسية ناقص قيمة المخالفة الأولى.</li>
                            <li>إذا كانت هناك مخالفتان: نقطة السلوك الأساسية ناقص (قيمة المخالفة الأولى + قيمة المخالفة الثانية).</li>
                            <li>إذا كانت هناك ثلاث مخالفات أو أكثر: نقطة السلوك الأساسية ناقص (قيمة المخالفة الأولى + قيمة المخالفة الثانية + قيمة المخالفة الثالثة).</li>
                        </ul>
                    </li>
                </ul>

                <h3 style="color: #2980b9; font-family: Calibri;">7. الإعدادات الافتراضية:</h3>
                <ul style="font-family: Calibri; font-size: 13pt; color: #333333;">
                    <li>يمكنك تعديل الإعدادات الافتراضية مثل عتبة الغياب الأسبوعي والشهري، ونقطة المواظبة الأساسية، ونقطة السلوك الأساسية، ومعاملات المواظبة والسلوك، وقيم المخالفات من خلال زر "الإعدادات الافتراضية".</li>
                </ul>

                <h3 style="color: #2980b9; font-family: Calibri;">ملاحظات هامة:</h3>
                <ul style="font-family: Calibri; font-size: 13pt; color: #333333;">
                    <li>الخلايا الملونة باللون الأصفر تشير إلى تجاوز عتبة الغياب الأسبوعي.</li>
                    <li>الخلايا الملونة باللون الأحمر الفاتح تشير إلى تجاوز عتبة الغياب الشهري.</li>
                    <li>تأكد من إدخال أرقام صحيحة فقط في خانات الغياب.</li>
                    <li>الغياب غير المبرر = المجموع الشهري للغياب - الغياب المبرر.</li>
                </ul>
            """

            text_browser.setHtml(help_text)
            layout.addWidget(text_browser)

            # إضافة زر إغلاق
            button_layout = QHBoxLayout()

            close_btn = QPushButton("إغلاق")
            close_btn.setObjectName("close_btn")
            close_btn.setFont(QFont("Calibri", 12, QFont.Bold))
            close_btn.setCursor(Qt.PointingHandCursor)
            close_btn.setFixedWidth(120)
            close_btn.clicked.connect(help_dialog.accept)

            button_layout.addStretch()
            button_layout.addWidget(close_btn)
            button_layout.addStretch()

            layout.addLayout(button_layout)

            # عرض النافذة
            help_dialog.exec_()

        except Exception as e:
            print(f"خطأ في عرض نافذة التعليمات: {e}")
            import traceback
            traceback.print_exc()

    def create_reports_folder(self):
        """إنشاء مجلد لحفظ تقارير الغياب الشهري والسنوي على سطح المكتب"""
        try:
            # الحصول على مسار سطح المكتب
            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")

            # إنشاء مسار المجلد الرئيسي على سطح المكتب
            main_folder = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
            if not os.path.exists(main_folder):
                os.makedirs(main_folder)
                print(f"تم إنشاء المجلد الرئيسي: {main_folder}")

            # إنشاء مجلد تقارير الغياب داخل المجلد الرئيسي
            reports_folder = os.path.join(main_folder, "تقارير الغياب")
            if not os.path.exists(reports_folder):
                os.makedirs(reports_folder)
                print(f"تم إنشاء مجلد تقارير الغياب: {reports_folder}")

            return reports_folder
        except Exception as e:
            print(f"خطأ في إنشاء مجلد التقارير على سطح المكتب: {e}")
            # في حالة الفشل، نعود إلى المجلد الافتراضي
            default_folder = os.path.join(os.path.dirname(os.path.abspath(__file__)), "تقارير_الغياب_الشهري_والسنوي")
            if not os.path.exists(default_folder):
                os.makedirs(default_folder)
            return default_folder

    def update_semester_field(self):
        """تحديث حقل الأسدس في سجلات الغياب بناءً على الشهر"""
        try:
            conn = sqlite3.connect(get_database_path())
            cur = conn.cursor()

            # تحديث الأسدس الأول (شتنبر، أكتوبر، نونبر، دجنبر، يناير)
            cur.execute("""
                UPDATE مسك_الغياب_الأسبوعي
                SET الأسدس = 1
                WHERE الشهر IN ('شتنبر', 'أكتوبر', 'نونبر', 'دجنبر', 'يناير')
                AND (الأسدس IS NULL OR الأسدس = 0 OR الأسدس = '')
            """)
            first_sem_count = cur.rowcount

            # تحديث الأسدس الثاني (فبراير، مارس، أبريل، ماي، يونيو)
            cur.execute("""
                UPDATE مسك_الغياب_الأسبوعي
                SET الأسدس = 2
                WHERE الشهر IN ('فبراير', 'مارس', 'أبريل', 'ماي', 'يونيو')
                AND (الأسدس IS NULL OR الأسدس = 0 OR الأسدس = '')
            """)
            second_sem_count = cur.rowcount

            conn.commit()
            print(f"تم تحديث {first_sem_count} سجل للأسدس الأول و {second_sem_count} سجل للأسدس الثاني")

            return first_sem_count + second_sem_count
        except Exception as e:
            print(f"خطأ في تحديث حقل الأسدس: {e}")
            return 0
        finally:
            conn.close()

    def printSemesterReport(self):
        """حساب المجموع الشهري للغياب وإنشاء جداول الغياب السنوي"""
        try:
            # إنشاء مجلد لحفظ التقارير
            reports_folder = self.create_reports_folder()

            # استيراد نافذة تقرير الغياب من ملف second_semester_helper.py
            from second_semester_helper import YearlyAbsenceSummaryWindow

            # إنشاء نافذة جديدة لتقرير الغياب
            absence_window = YearlyAbsenceSummaryWindow()

            # الحصول على القسم المحدد من القائمة المنسدلة
            section = self.combo_qism.currentText()

            # تعيين القسم المحدد في نافذة تقرير الغياب
            if section:
                index = absence_window.section_combo.findText(section)
                if index >= 0:
                    absence_window.section_combo.setCurrentIndex(index)

            # إنشاء جداول الغياب السنوي
            absence_window.generate_summary_tables()

            # الحصول على التاريخ الحالي بتنسيق مناسب للملف
            current_date = datetime.datetime.now().strftime("%Y%m%d")

            # تحديد اسم الملف
            file_name = f"تقرير_الغياب_الشهري_{section}_{current_date}.pdf"
            report_path = os.path.join(reports_folder, file_name)

            # حفظ التقرير في المجلد المحدد
            if hasattr(absence_window, 'save_report_to_file'):
                absence_window.save_report_to_file(report_path)
                print(f"تم حفظ التقرير في: {report_path}")

                # فتح التقرير بعد حفظه
                try:
                    os.startfile(report_path)
                    print(f"تم فتح التقرير: {report_path}")
                except Exception as open_error:
                    print(f"خطأ في فتح التقرير: {open_error}")

            # إضافة رسالة جامعة تخبر المستخدم بمكان حفظ التقرير
            self.show_info_message(
                "معاينة وحفظ التقرير",
                f"تم حساب المجموع الشهري للغياب وإنشاء جداول الغياب السنوي بنجاح.\n"
                f"تم حفظ التقرير في المسار:\n"
                f"سطح المكتب/تقارير برنامج المعين في الحراسة العامة/تقارير الغياب\n"
                f"اسم الملف: {file_name}"
            )

            print("تم حساب المجموع الشهري للغياب بنجاح!")

        except Exception as e:
            # خطأ عام في تجهيز الطباعة
            error_msg = f"حدث خطأ أثناء حساب المجموع الشهري للغياب:\n{str(e)}"
            print(error_msg)
            import traceback
            traceback.print_exc()
            self.show_message("خطأ", error_msg, QMessageBox.Critical)

    def on_table_cell_clicked(self, index):
        """
        معالجة النقر على خلايا الجدول
        عند النقر على عمود الرمز (العمود 3)، يتم فتح تقرير الغياب الشهري الخاص بالتلميذ
        وحفظه في مجلد بطاقة_غياب_تلميذ
        """
        # استخدام تأخير قصير لتحسين استجابة واجهة المستخدم
        from PyQt5.QtCore import QTimer
        QTimer.singleShot(10, lambda: self._delayed_cell_click_handler(index))

    def _delayed_cell_click_handler(self, index):
        """معالجة النقر على خلايا الجدول بعد تأخير قصير"""
        try:
            # إظهار مؤشر الانتظار
            from PyQt5.QtCore import Qt, QTimer
            from PyQt5.QtGui import QCursor
            self.setCursor(QCursor(Qt.WaitCursor))

            # التحقق من أن العمود المنقور عليه هو عمود الرمز (العمود 3)
            if index.column() == 3:  # عمود "رمز التلميذ"
                # الحصول على رمز التلميذ من الخلية المنقور عليها
                student_code = self.model.data(index, Qt.DisplayRole)
                if student_code:
                    print(f"تم النقر على رمز التلميذ: {student_code}")

                    # الحصول على اسم التلميذ من الجدول (العمود 2)
                    row = index.row()
                    student_name = self.model.data(self.model.index(row, 2), Qt.DisplayRole)

                    # استخدام تأخير قصير قبل فتح تقرير الغياب
                    # نمرر قيمة فارغة للمعلمة الثالثة لأننا سنستخدم مسار سطح المكتب بدلاً منها
                    QTimer.singleShot(10, lambda: self._open_student_absence_report(student_code, student_name, ""))
                else:
                    # إعادة المؤشر إلى الوضع الطبيعي
                    self.setCursor(QCursor(Qt.ArrowCursor))
            else:
                # إعادة المؤشر إلى الوضع الطبيعي
                self.setCursor(QCursor(Qt.ArrowCursor))
        except Exception as e:
            print(f"خطأ في معالجة النقر على خلية الجدول: {e}")
            import traceback
            traceback.print_exc()

            # إعادة المؤشر إلى الوضع الطبيعي
            from PyQt5.QtCore import Qt
            from PyQt5.QtGui import QCursor
            self.setCursor(QCursor(Qt.ArrowCursor))

    def _open_student_absence_report(self, student_code, student_name, report_folder):
        """فتح تقرير الغياب الشهري للتلميذ"""
        try:
            # الحصول على مسار سطح المكتب
            import os
            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")

            # إنشاء مسار المجلد الرئيسي على سطح المكتب
            main_folder = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
            if not os.path.exists(main_folder):
                os.makedirs(main_folder)
                print(f"تم إنشاء المجلد الرئيسي: {main_folder}")

            # إنشاء مجلد تقارير الغياب داخل المجلد الرئيسي
            reports_folder = os.path.join(main_folder, "تقارير الغياب")
            if not os.path.exists(reports_folder):
                os.makedirs(reports_folder)
                print(f"تم إنشاء مجلد تقارير الغياب: {reports_folder}")

            # عرض رسالة نجاح محسنة قبل فتح التقرير
            self.show_custom_success_message(
                "جاري إنشاء التقرير",
                f"جاري إنشاء تقرير بطاقة غياب التلميذ:\n"
                f"{student_name}\n\n"
                f"سيتم حفظ التقرير في المسار:\n"
                f"سطح المكتب/تقارير برنامج المعين في الحراسة العامة/تقارير الغياب"
            )

            # استيراد نافذة تقرير الغياب السنوي للتلميذ
            from second_semester_helper import YearlyAbsenceSummaryWindow

            # إنشاء نافذة جديدة لتقرير الغياب
            absence_window = YearlyAbsenceSummaryWindow()

            # تعيين رمز التلميذ في حقل الإدخال
            absence_window.student_code_input.setText(student_code)

            # توليد التقرير بدون عرض رسالة تأكيد
            # نستخدم محاولة لتجاوز أي رسائل قد تظهر
            try:
                # استخدام تقنية Monkey patching لتجاوز أي رسائل
                # نحاول تجاوز دالة QMessageBox.information التي تستخدم لعرض الرسائل

                # حفظ الدالة الأصلية
                original_info = QMessageBox.information

                # تعريف دالة فارغة لتجاوز عرض الرسائل
                def empty_info(*args, **kwargs):
                    return QMessageBox.Ok

                # استبدال الدالة الأصلية بالدالة الفارغة
                QMessageBox.information = empty_info

                # توليد التقرير
                absence_window.generate_student_absence_report()

                # إعادة الدالة الأصلية
                QMessageBox.information = original_info

            except Exception as e:
                print(f"خطأ أثناء محاولة تجاوز الرسائل: {e}")
                # في حالة حدوث خطأ، نستخدم الطريقة العادية
                absence_window.generate_student_absence_report()

            # الحصول على التاريخ الحالي بتنسيق مناسب للملف
            current_date = datetime.datetime.now().strftime("%Y%m%d")

            # تحديد اسم الملف (اسم التلميذ_رمز التلميذ_التاريخ.pdf)
            safe_name = student_name.replace("/", "_").replace("\\", "_").replace(":", "_")
            file_name = f"{safe_name}_{student_code}_{current_date}.pdf"
            report_path = os.path.join(reports_folder, file_name)

            # حفظ التقرير في المجلد المحدد
            if hasattr(absence_window, 'save_report_to_file'):
                absence_window.save_report_to_file(report_path)
                print(f"تم حفظ التقرير في: {report_path}")

                # عرض النافذة
                absence_window.show()

                # فتح التقرير بعد حفظه
                try:
                    os.startfile(report_path)
                    print(f"تم فتح التقرير: {report_path}")
                except Exception as open_error:
                    print(f"خطأ في فتح التقرير: {open_error}")
            else:
                print("لا توجد دالة لحفظ التقرير في كائن النافذة")
                # عرض النافذة على أي حال
                absence_window.show()

            print(f"تم فتح تقرير الغياب للتلميذ: {student_code}")
        except Exception as e:
            import traceback
            traceback.print_exc()
            self.show_message("خطأ", f"لم يتم فتح تقرير الغياب للتلميذ:\n{e}", QMessageBox.Critical)
            print(f"خطأ عند فتح تقرير الغياب للتلميذ: {e}")
        finally:
            # إعادة المؤشر إلى الوضع الطبيعي
            from PyQt5.QtCore import Qt
            from PyQt5.QtGui import QCursor
            self.setCursor(QCursor(Qt.ArrowCursor))

    def show_custom_success_message(self, title, message):
        """عرض رسالة نجاح مخصصة بالنمط المميز"""
        try:
            # استيراد الكلاسات المطلوبة
            from PyQt5.QtWidgets import QStyle, QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton
            from PyQt5.QtGui import QIcon, QPixmap, QFont
            from PyQt5.QtCore import Qt

            # إنشاء نافذة حوار جديدة
            success_dialog = QDialog(self)
            success_dialog.setWindowTitle(title)
            success_dialog.setFixedSize(450, 250)
            success_dialog.setLayoutDirection(Qt.RightToLeft)

            # إضافة أيقونة البرنامج
            try:
                app_icon = QIcon("01.ico")
                success_dialog.setWindowIcon(app_icon)
            except Exception as e:
                print(f"خطأ في تحميل أيقونة البرنامج: {e}")

            # تنسيق النافذة
            success_dialog.setStyleSheet("""
                QDialog {
                    background-color: #f0fff0;
                    border: 2px solid #2ecc71;
                    border-radius: 10px;
                }
                QLabel {
                    color: #333333;
                    font-weight: bold;
                }
                QLabel#icon_label {
                    padding: 10px;
                }
                QLabel#message_label {
                    background-color: #eafaf1;
                    border: 1px solid #2ecc71;
                    border-radius: 5px;
                    padding: 15px;
                    font-size: 14pt;
                }
                QPushButton {
                    background-color: #2ecc71;
                    color: white;
                    border-radius: 5px;
                    padding: 8px 15px;
                    font-weight: bold;
                    min-height: 35px;
                    min-width: 120px;
                }
                QPushButton:hover {
                    background-color: #27ae60;
                    border: 2px solid #2ecc71;
                }
            """)

            # إنشاء تخطيط النافذة
            layout = QVBoxLayout(success_dialog)
            layout.setContentsMargins(20, 20, 20, 20)
            layout.setSpacing(15)

            # إضافة أيقونة وعنوان
            header_layout = QHBoxLayout()

            # محاولة إضافة أيقونة النجاح
            icon_label = QLabel()
            icon_label.setObjectName("icon_label")
            icon_label.setAlignment(Qt.AlignCenter)
            try:
                # محاولة استخدام أيقونة النجاح القياسية
                success_icon = self.style().standardPixmap(QStyle.SP_MessageBoxInformation)
                success_icon = QPixmap(success_icon).scaled(48, 48, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                icon_label.setPixmap(success_icon)
            except Exception:
                # استخدام نص بديل في حالة الفشل
                icon_label.setText("✓")
                icon_label.setFont(QFont("Arial", 24))
                icon_label.setStyleSheet("color: #2ecc71;")

            header_layout.addWidget(icon_label)

            # إضافة عنوان النافذة
            title_label = QLabel(title)
            title_label.setFont(QFont("Calibri", 16, QFont.Bold))
            title_label.setStyleSheet("color: #27ae60;")
            title_label.setAlignment(Qt.AlignCenter)
            header_layout.addWidget(title_label, 1)

            layout.addLayout(header_layout)

            # إضافة رسالة النجاح
            message_label = QLabel(message)
            message_label.setObjectName("message_label")
            message_label.setFont(QFont("Calibri", 13))
            message_label.setAlignment(Qt.AlignCenter)
            message_label.setWordWrap(True)
            layout.addWidget(message_label)

            # إضافة زر الموافقة
            button_layout = QHBoxLayout()
            ok_button = QPushButton("موافق")
            ok_button.setFont(QFont("Calibri", 12, QFont.Bold))
            ok_button.setCursor(Qt.PointingHandCursor)
            ok_button.clicked.connect(success_dialog.accept)
            button_layout.addWidget(ok_button)

            layout.addLayout(button_layout)

            # عرض النافذة
            success_dialog.exec_()
            return True

        except Exception as e:
            print(f"خطأ في عرض رسالة النجاح المخصصة: {e}")
            import traceback
            traceback.print_exc()
            # استخدام الطريقة التقليدية في حالة الفشل
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.information(self, title, message)

    def printFirstSemesterReport(self):
        """طباعة تقرير الأسدس الأول للقسم المحدد في القائمة المنسدلة"""
        # التحقق من تحديد المستوى والقسم
        if not self.combo_mustawa.currentText():
            self.show_message("تنبيه", "الرجاء تحديد المستوى أولاً.", QMessageBox.Warning)
            return

        section = self.combo_qism.currentText()
        if not section:
            self.show_message("تنبيه", "الرجاء تحديد القسم.", QMessageBox.Warning)
            return

        try:
            # أولاً، نقوم بحساب المجموع الشهري للغياب وإنشاء جداول الغياب السنوي
            try:
                # استيراد نافذة تقرير الغياب من ملف second_semester_helper.py
                from second_semester_helper import YearlyAbsenceSummaryWindow

                # إنشاء نافذة جديدة لتقرير الغياب
                absence_window = YearlyAbsenceSummaryWindow()

                # الحصول على القسم المحدد من القائمة المنسدلة
                section = self.combo_qism.currentText()

                # تعيين القسم المحدد في نافذة تقرير الغياب
                if section:
                    index = absence_window.section_combo.findText(section)
                    if index >= 0:
                        absence_window.section_combo.setCurrentIndex(index)

                # تجاوز رسائل التأكيد العادية عند إنشاء الجداول التجميعية
                try:
                    # حفظ الدالة الأصلية
                    original_info = QMessageBox.information

                    # تعريف دالة فارغة لتجاوز عرض الرسائل
                    def empty_info(*args, **kwargs):
                        return QMessageBox.Ok

                    # استبدال الدالة الأصلية بالدالة الفارغة
                    QMessageBox.information = empty_info

                    # إنشاء جداول الغياب السنوي
                    absence_window.generate_summary_tables()

                    # إعادة الدالة الأصلية
                    QMessageBox.information = original_info
                except Exception as e:
                    print(f"خطأ أثناء محاولة تجاوز رسائل إنشاء الجداول التجميعية: {e}")
                    # في حالة حدوث خطأ، نستخدم الطريقة العادية
                    absence_window.generate_summary_tables()

                print("تم حساب المجموع الشهري للغياب بنجاح!")
            except Exception as e:
                # خطأ عام في تجهيز الطباعة
                error_msg = f"حدث خطأ أثناء حساب المجموع الشهري للغياب:\n{str(e)}"
                print(error_msg)
                import traceback
                traceback.print_exc()
                # لا نعرض رسالة خطأ للمستخدم هنا، نستمر في محاولة طباعة التقرير

            # --- التحقق من توفر دوال تقرير الأسدس ---
            if not SEMESTER_FUNCTIONS_AVAILABLE:
                self.show_message("خطأ", "وظائف طباعة تقرير الأسدس غير متوفرة.", QMessageBox.Critical)
                return

            # الحصول على القسم المحدد من القائمة المنسدلة
            section = self.combo_qism.currentText()

            if not section:
                self.show_message("تنبيه", "الرجاء اختيار قسم أولاً.", QMessageBox.Warning)
                return

            # تحديث حقل الأسدس قبل التحقق من وجود بيانات
            updated_records = self.update_semester_field()
            print(f"تم تحديث {updated_records} سجل في جدول مسك_الغياب_الأسبوعي")

            # تحقق من وجود بيانات للقسم المحدد
            conn = sqlite3.connect(get_database_path())
            cur = conn.cursor()

            # الحصول على السنة الدراسية الحالية
            cur.execute("SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
            result = cur.fetchone()
            school_year = result[0] if result else ""

            # التحقق من وجود تلاميذ في القسم المحدد
            cur.execute("""
                SELECT COUNT(*) FROM اللوائح
                WHERE القسم = ? AND السنة_الدراسية = ?
            """, (section, school_year))
            students_count = cur.fetchone()[0]

            # استعلام يسمح بإظهار بيانات الأسدس الأول عبر الشهور المرتبطة به
            cur.execute("""
                SELECT COUNT(*) FROM مسك_الغياب_الأسبوعي mag
                JOIN اللوائح l ON mag.رمز_التلميذ = l.الرمز
                WHERE l.القسم = ? AND l.السنة_الدراسية = ?
                AND (mag.الأسدس = 1 OR mag.الشهر IN ('شتنبر', 'أكتوبر', 'نونبر', 'دجنبر', 'يناير'))
            """, (section, school_year))
            attendance_records_count = cur.fetchone()[0]

            # تحقق إضافي للشهور المرتبطة بالأسدس الأول
            cur.execute("""
                SELECT DISTINCT mag.الشهر FROM مسك_الغياب_الأسبوعي mag
                JOIN اللوائح l ON mag.رمز_التلميذ = l.الرمز
                WHERE l.القسم = ? AND l.السنة_الدراسية = ?
                AND mag.الشهر IN ('شتنبر', 'أكتوبر', 'نونبر', 'دجنبر', 'يناير')
            """, (section, school_year))
            first_semester_months = [row[0] for row in cur.fetchall()]

            conn.close()

            # عرض معلومات تشخيصية في وحدة التحكم
            print(f"\n=== معلومات تشخيصية لتقرير الأسدس الأول ===")
            print(f"القسم المحدد: {section}")
            print(f"السنة الدراسية: {school_year}")
            print(f"عدد التلاميذ: {students_count}")
            print(f"عدد سجلات الغياب: {attendance_records_count}")
            print(f"شهور الأسدس الأول المتوفرة: {first_semester_months}")

            if students_count == 0:
                self.show_message("تنبيه",
                    f"لا يوجد تلاميذ مسجلين في القسم {section} للسنة الدراسية {school_year}.",
                    QMessageBox.Warning)
                return

            if attendance_records_count == 0:
                self.show_message("تنبيه",
                    f"لا توجد بيانات غياب مسجلة للقسم {section}.\n"
                    f"يرجى التأكد من تسجيل بيانات الغياب أولاً.",
                    QMessageBox.Warning)
                return

            # --- استدعاء دالة طباعة تقرير الأسدس للقسم الواحد ---
            print(f"محاولة طباعة تقرير الأسدس للقسم: {section}")
            try:
                # عرض رسالة نجاح محسنة قبل إنشاء التقرير
                self.show_custom_success_message(
                    "جاري إنشاء التقرير",
                    f"جاري إنشاء تقرير الأسدس الأول للقسم:\n"
                    f"{section}\n\n"
                    f"سيتم حفظ التقرير في المسار:\n"
                    f"سطح المكتب/تقارير برنامج المعين في الحراسة العامة/تقارير الغياب"
                )

                # إنشاء مجلد لحفظ التقارير
                reports_folder = self.create_reports_folder()

                # الحصول على التاريخ الحالي بتنسيق مناسب للملف
                current_date = datetime.datetime.now().strftime("%Y%m%d")

                # تحديد اسم الملف
                file_name = f"تقرير_الأسدس_الأول_{section}_{current_date}.pdf"
                report_path = os.path.join(reports_folder, file_name)

                # تجاوز رسائل التأكيد العادية
                try:
                    # حفظ الدالة الأصلية
                    original_info = QMessageBox.information

                    # تعريف دالة فارغة لتجاوز عرض الرسائل
                    def empty_info(*args, **kwargs):
                        return QMessageBox.Ok

                    # استبدال الدالة الأصلية بالدالة الفارغة
                    QMessageBox.information = empty_info

                    # استدعاء الدالة المستوردة من absence_reports.py مع تمرير مسار الملف
                    print("استدعاء دالة print_semester_report...")
                    result = print_semester_report(self, section)
                    print(f"نتيجة استدعاء print_semester_report: {result}")

                    # إعادة الدالة الأصلية
                    QMessageBox.information = original_info
                except Exception as e:
                    print(f"خطأ أثناء محاولة تجاوز الرسائل: {e}")
                    # في حالة حدوث خطأ، نستخدم الطريقة العادية
                    result = print_semester_report(self, section)
                    print(f"نتيجة استدعاء print_semester_report: {result}")

                # نسخ الملف من المسار الافتراضي إلى المجلد المطلوب
                if result:
                    try:
                        # البحث عن الملف الذي تم إنشاؤه
                        default_file = f"تقرير_الغياب_الأسدس_الأول_{section}.pdf"
                        if os.path.exists(default_file):
                            # نسخ الملف إلى المجلد المطلوب
                            import shutil
                            shutil.copy2(default_file, report_path)
                            print(f"تم نسخ التقرير إلى: {report_path}")

                            # تجاوز فتح الملف هنا لأنه سيتم فتحه من خلال دالة print_semester_report
                            # os.startfile(report_path)
                    except Exception as copy_error:
                        print(f"خطأ في نسخ الملف: {copy_error}")

                print("تم طباعة تقرير الأسدس الأول بنجاح!")

            except Exception as e:
                error_msg = f"حدث خطأ أثناء طباعة تقرير الأسدس الأول للقسم {section}:\n{str(e)}"
                print(error_msg)
                import traceback
                traceback.print_exc()
                self.show_message("خطأ طباعة الأسدس الأول",
                    f"{error_msg}",
                    QMessageBox.Critical)

        except Exception as e:
            # خطأ عام في تجهيز الطباعة
            error_msg = f"حدث خطأ عام أثناء تجهيز طباعة تقرير الأسدس الأول:\n{str(e)}"
            print(error_msg)
            import traceback
            traceback.print_exc()
            self.show_message("خطأ", error_msg, QMessageBox.Critical)

    def printSecondSemesterReport(self):
        """طباعة تقرير الأسدس الثاني للقسم المحدد في القائمة المنسدلة"""
        # التحقق من تحديد المستوى والقسم
        if not self.combo_mustawa.currentText():
            self.show_message("تنبيه", "الرجاء تحديد المستوى أولاً.", QMessageBox.Warning)
            return

        section = self.combo_qism.currentText()
        if not section:
            self.show_message("تنبيه", "الرجاء تحديد القسم.", QMessageBox.Warning)
            return

        try:
            # أولاً، نقوم بحساب المجموع الشهري للغياب وإنشاء جداول الغياب السنوي
            try:
                # استيراد نافذة تقرير الغياب من ملف second_semester_helper.py
                from second_semester_helper import YearlyAbsenceSummaryWindow

                # إنشاء نافذة جديدة لتقرير الغياب
                absence_window = YearlyAbsenceSummaryWindow()

                # الحصول على القسم المحدد من القائمة المنسدلة
                section = self.combo_qism.currentText()

                # تعيين القسم المحدد في نافذة تقرير الغياب
                if section:
                    index = absence_window.section_combo.findText(section)
                    if index >= 0:
                        absence_window.section_combo.setCurrentIndex(index)

                # تجاوز رسائل التأكيد العادية عند إنشاء الجداول التجميعية
                try:
                    # حفظ الدالة الأصلية
                    original_info = QMessageBox.information

                    # تعريف دالة فارغة لتجاوز عرض الرسائل
                    def empty_info(*args, **kwargs):
                        return QMessageBox.Ok

                    # استبدال الدالة الأصلية بالدالة الفارغة
                    QMessageBox.information = empty_info

                    # إنشاء جداول الغياب السنوي
                    absence_window.generate_summary_tables()

                    # إعادة الدالة الأصلية
                    QMessageBox.information = original_info
                except Exception as e:
                    print(f"خطأ أثناء محاولة تجاوز رسائل إنشاء الجداول التجميعية: {e}")
                    # في حالة حدوث خطأ، نستخدم الطريقة العادية
                    absence_window.generate_summary_tables()

                print("تم حساب المجموع الشهري للغياب بنجاح!")
            except Exception as e:
                # خطأ عام في تجهيز الطباعة
                error_msg = f"حدث خطأ أثناء حساب المجموع الشهري للغياب:\n{str(e)}"
                print(error_msg)
                import traceback
                traceback.print_exc()
                # لا نعرض رسالة خطأ للمستخدم هنا، نستمر في محاولة طباعة التقرير

            # --- التحقق من توفر دوال تقرير الأسدس ---
            if not SECOND_SEMESTER_FUNCTION_AVAILABLE:
                self.show_message("خطأ", "وظائف طباعة تقرير الأسدس الثاني غير متوفرة.", QMessageBox.Critical)
                return

            # الحصول على القسم المحدد من القائمة المنسدلة
            section = self.combo_qism.currentText()

            if not section:
                self.show_message("تنبيه", "الرجاء اختيار قسم أولاً.", QMessageBox.Warning)
                return

            # تحديث حقل الأسدس قبل التحقق من وجود بيانات
            updated_records = self.update_semester_field()
            print(f"تم تحديث {updated_records} سجل في جدول مسك_الغياب_الأسبوعي")

            # تحقق من وجود بيانات للقسم المحدد
            conn = sqlite3.connect(get_database_path())
            cur = conn.cursor()

            # الحصول على السنة الدراسية الحالية
            cur.execute("SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
            result = cur.fetchone()
            school_year = result[0] if result else ""

            # التحقق من وجود تلاميذ في القسم المحدد
            cur.execute("""
                SELECT COUNT(*) FROM اللوائح
                WHERE القسم = ? AND السنة_الدراسية = ?
            """, (section, school_year))
            students_count = cur.fetchone()[0]

            # استعلام يسمح بإظهار بيانات الأسدس الثاني عبر الشهور المرتبطة به
            cur.execute("""
                SELECT COUNT(*) FROM مسك_الغياب_الأسبوعي mag
                JOIN اللوائح l ON mag.رمز_التلميذ = l.الرمز
                WHERE l.القسم = ? AND l.السنة_الدراسية = ?
                AND (mag.الأسدس = 2 OR mag.الشهر IN ('فبراير', 'مارس', 'أبريل', 'ماي', 'يونيو'))
            """, (section, school_year))
            attendance_records_count = cur.fetchone()[0]

            # تحقق إضافي للشهور المرتبطة بالأسدس الثاني
            cur.execute("""
                SELECT DISTINCT mag.الشهر FROM مسك_الغياب_الأسبوعي mag
                JOIN اللوائح l ON mag.رمز_التلميذ = l.الرمز
                WHERE l.القسم = ? AND l.السنة_الدراسية = ?
                AND mag.الشهر IN ('فبراير', 'مارس', 'أبريل', 'ماي', 'يونيو')
            """, (section, school_year))
            second_semester_months = [row[0] for row in cur.fetchall()]

            conn.close()

            # عرض معلومات تشخيصية في وحدة التحكم
            print(f"\n=== معلومات تشخيصية لتقرير الأسدس الثاني ===")
            print(f"القسم المحدد: {section}")
            print(f"السنة الدراسية: {school_year}")
            print(f"عدد التلاميذ: {students_count}")
            print(f"عدد سجلات الغياب للأسدس الثاني: {attendance_records_count}")
            print(f"شهور الأسدس الثاني المتوفرة: {second_semester_months}")

            if students_count == 0:
                self.show_message("تنبيه",
                    f"لا يوجد تلاميذ مسجلين في القسم {section} للسنة الدراسية {school_year}.",
                    QMessageBox.Warning)
                return

            if attendance_records_count == 0:
                self.show_message("تنبيه",
                    f"لا توجد بيانات غياب مسجلة للأسدس الثاني للقسم {section}.\n"
                    f"يرجى التأكد من تسجيل بيانات الغياب لشهور الأسدس الثاني أولاً.",
                    QMessageBox.Warning)
                return

            # --- استدعاء دالة طباعة تقرير الأسدس الثاني للقسم المحدد ---
            print(f"محاولة طباعة تقرير الأسدس الثاني للقسم: {section}")
            try:
                # عرض رسالة نجاح محسنة قبل إنشاء التقرير
                self.show_custom_success_message(
                    "جاري إنشاء التقرير",
                    f"جاري إنشاء تقرير الأسدس الثاني للقسم:\n"
                    f"{section}\n\n"
                    f"سيتم حفظ التقرير في المسار:\n"
                    f"سطح المكتب/تقارير برنامج المعين في الحراسة العامة/تقارير الغياب"
                )

                # إنشاء مجلد لحفظ التقارير
                reports_folder = self.create_reports_folder()

                # الحصول على التاريخ الحالي بتنسيق مناسب للملف
                current_date = datetime.datetime.now().strftime("%Y%m%d")

                # تحديد اسم الملف
                file_name = f"تقرير_الأسدس_الثاني_{section}_{current_date}.pdf"
                report_path = os.path.join(reports_folder, file_name)

                # تجاوز رسائل التأكيد العادية
                try:
                    # حفظ الدالة الأصلية
                    original_info = QMessageBox.information

                    # تعريف دالة فارغة لتجاوز عرض الرسائل
                    def empty_info(*args, **kwargs):
                        return QMessageBox.Ok

                    # استبدال الدالة الأصلية بالدالة الفارغة
                    QMessageBox.information = empty_info

                    # استدعاء الدالة المخصصة للأسدس الثاني مع تمرير معلمة الأسدس
                    print("استدعاء دالة print_second_semester_report للأسدس الثاني...")
                    result = print_second_semester_report(self, section)
                    print(f"نتيجة استدعاء print_second_semester_report: {result}")

                    # إعادة الدالة الأصلية
                    QMessageBox.information = original_info
                except Exception as e:
                    print(f"خطأ أثناء محاولة تجاوز الرسائل: {e}")
                    # في حالة حدوث خطأ، نستخدم الطريقة العادية
                    result = print_second_semester_report(self, section)
                    print(f"نتيجة استدعاء print_second_semester_report: {result}")

                # نسخ الملف من المسار الافتراضي إلى المجلد المطلوب
                if result:
                    try:
                        # البحث عن الملف الذي تم إنشاؤه
                        default_file = f"تقرير_الغياب_الأسدس_الثاني_{section}.pdf"
                        if os.path.exists(default_file):
                            # نسخ الملف إلى المجلد المطلوب
                            import shutil
                            shutil.copy2(default_file, report_path)
                            print(f"تم نسخ التقرير إلى: {report_path}")

                            # تجاوز فتح الملف هنا لأنه سيتم فتحه من خلال دالة print_second_semester_report
                            # os.startfile(report_path)
                    except Exception as copy_error:
                        print(f"خطأ في نسخ الملف: {copy_error}")

                print("تم طباعة تقرير الأسدس الثاني بنجاح!")

            except Exception as e:
                error_msg = f"حدث خطأ أثناء طباعة تقرير الأسدس الثاني للقسم {section}:\n{str(e)}"
                print(error_msg)
                import traceback
                traceback.print_exc()
                self.show_message("خطأ طباعة الأسدس الثاني",
                    f"{error_msg}",
                    QMessageBox.Critical)

        except Exception as e:
            # خطأ عام في تجهيز الطباعة
            error_msg = f"حدث خطأ عام أثناء تجهيز طباعة تقرير الأسدس الثاني:\n{str(e)}"
            print(error_msg)
            import traceback
            traceback.print_exc()
            self.show_message("خطأ", error_msg, QMessageBox.Critical)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = Hirasa300Window()
    window.show()
    sys.exit(app.exec_())




