#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
أداة مساعدة لتعيين الطابعة الافتراضية
تعمل كعملية منفصلة لتجنب توقف البرنامج الرئيسي
"""

import sys
import platform
import subprocess

def main():
    """الوظيفة الرئيسية"""
    if len(sys.argv) < 2:
        print("خطأ: يجب تحديد اسم الطابعة كمعامل")
        return 1

    printer_name = sys.argv[1]
    print(f"محاولة تعيين '{printer_name}' كطابعة افتراضية...")

    success = False
    error_msg = "سبب غير معروف"
    os_name = platform.system()

    try:
        if os_name == "Windows":
            # محاولة استخدام win32print أولاً
            try:
                import win32print
                win32print.SetDefaultPrinter(printer_name)
                success = True
                print(f"تم تعيين الطابعة '{printer_name}' كطابعة افتراضية بنجاح (Windows).")
            except ImportError:
                print("مكتبة win32print غير متاحة. سيتم استخدام الطريقة البديلة.")
                # استخدام rundll32 كبديل
                try:
                    cmd = f'rundll32 printui.dll,PrintUIEntry /y /n "{printer_name}"'
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)

                    if result.returncode == 0:
                        success = True
                        print(f"تم تعيين الطابعة '{printer_name}' كطابعة افتراضية بنجاح (Windows rundll32).")
                    else:
                        error_msg = f"فشل تعيين الطابعة الافتراضية باستخدام rundll32. رمز الخطأ: {result.returncode}"
                        print(error_msg)
                except Exception as sub_e:
                    error_msg = f"فشل تعيين الطابعة الافتراضية باستخدام الطريقة البديلة: {str(sub_e)}"
                    print(error_msg)
            except Exception as e:
                error_msg = f"خطأ في تعيين الطابعة الافتراضية باستخدام win32print: {str(e)}"
                print(error_msg)

                # محاولة استخدام rundll32 كبديل
                try:
                    cmd = f'rundll32 printui.dll,PrintUIEntry /y /n "{printer_name}"'
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)

                    if result.returncode == 0:
                        success = True
                        print(f"تم تعيين الطابعة '{printer_name}' كطابعة افتراضية بنجاح (Windows rundll32).")
                    else:
                        error_msg = f"فشل تعيين الطابعة الافتراضية باستخدام rundll32. رمز الخطأ: {result.returncode}"
                        print(error_msg)
                except Exception as sub_e:
                    error_msg = f"فشل تعيين الطابعة الافتراضية باستخدام الطريقة البديلة: {str(sub_e)}"
                    print(error_msg)

        elif os_name in ["Linux", "Darwin"]:
            try:
                subprocess.run(['lpoptions', '-d', printer_name],
                            check=True, capture_output=True, text=True, timeout=5)
                success = True
                print(f"تم تعيين الطابعة الافتراضية بنجاح ({os_name} - CUPS).")
            except Exception as e:
                error_msg = f"خطأ في تعيين الطابعة الافتراضية على {os_name}: {str(e)}"
                print(error_msg)
        else:
            error_msg = f"نظام التشغيل '{os_name}' غير مدعوم لتعيين الطابعة الافتراضية تلقائيًا."
            print(error_msg)

    except Exception as e:
        error_msg = f"خطأ عام أثناء تعيين الطابعة الافتراضية: {str(e)}"
        print(error_msg)

    if success:
        print(f"تم تعيين الطابعة '{printer_name}' كطابعة افتراضية للنظام بنجاح.")
        return 0
    else:
        print(f"فشل تعيين '{printer_name}' كطابعة افتراضية. السبب: {error_msg}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
