#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3

def check_violations_table():
    """فحص هيكل جدول المخالفات"""
    try:
        conn = sqlite3.connect('data.db')
        cursor = conn.cursor()
        
        # التحقق من هيكل جدول المخالفات
        cursor.execute('PRAGMA table_info(إعدادات_الطابعة)')
        columns = cursor.fetchall()
        
        print('🔍 هيكل جدول إعدادات_الطابعة:')
        for col in columns:
            print(f'  📋 {col[1]} ({col[2]}) - {"مطلوب" if col[3] else "اختياري"}')
        
        # عرض بعض البيانات الموجودة
        cursor.execute('SELECT * FROM إعدادات_الطابعة LIMIT 3')
        sample_data = cursor.fetchall()
        
        print('\n📊 عينة من البيانات الموجودة:')
        if sample_data:
            for i, row in enumerate(sample_data, 1):
                print(f'  السجل {i}: {row}')
        else:
            print('  لا توجد بيانات في الجدول')
        
        conn.close()
        
    except Exception as e:
        print(f'❌ خطأ: {e}')

if __name__ == "__main__":
    check_violations_table()
