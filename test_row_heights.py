#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف اختبار لتجربة ارتفاعات الصفوف الجديدة في التقارير النصف الأسبوعية
"""

import datetime
import os
from split_attendance_report import SplitAttendanceReport

def create_students_list(count):
    """إنشاء قائمة طلاب بعدد محدد"""
    students = []
    for i in range(1, count + 1):
        students.append({
            "id": i,
            "name": f"الطالب رقم {i}",
            "rt": str(i)
        })
    return students

def test_row_heights():
    """اختبار ارتفاعات الصفوف المختلفة"""
    
    institution_data = {
        "academy": "الأكاديمية الجهوية للتربية والتكوين",
        "directorate": "المديرية الإقليمية",
        "institution": "الثانوية التأهيلية",
        "academic_year": "2024/2025"
    }
    
    start_date = datetime.datetime.now()
    periods = ["1", "2", "3", "4", "5", "6", "7", "8"]
    
    # إنشاء مجلد للاختبار
    test_dir = "test_row_heights"
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)
    
    report_generator = SplitAttendanceReport()
    
    # اختبار حالات مختلفة
    test_cases = [
        {"count": 25, "expected_height": 14, "description": "أقل من 35 صفاً"},
        {"count": 30, "expected_height": 14, "description": "أقل من 35 صفاً"},
        {"count": 35, "expected_height": 13, "description": "35 صفاً بالضبط"},
        {"count": 40, "expected_height": 13, "description": "بين 35-44 صفاً"},
        {"count": 44, "expected_height": 13, "description": "44 صفاً بالضبط"},
        {"count": 45, "expected_height": 12, "description": "45 صفاً أو أكثر"},
        {"count": 50, "expected_height": 12, "description": "50 صفاً"},
    ]
    
    print("اختبار ارتفاعات الصفوف في التقارير النصف الأسبوعية:")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        student_count = test_case["count"]
        expected_height = test_case["expected_height"]
        description = test_case["description"]
        
        # إنشاء قائمة الطلاب
        students = create_students_list(student_count)
        
        # اسم الملف
        file_name = f"test_{student_count}_students.pdf"
        file_path = os.path.join(test_dir, file_name)
        
        print(f"{i}. اختبار {student_count} طالب ({description})")
        print(f"   الارتفاع المتوقع: {expected_height} نقطة")
        
        # إنشاء التقرير
        success = report_generator.generate_first_half(
            file_path,
            students,
            f"قسم {student_count}",
            institution_data,
            start_date,
            1,  # نوع التقرير
            periods
        )
        
        if success:
            print(f"   ✓ تم إنشاء التقرير بنجاح: {file_name}")
        else:
            print(f"   ✗ فشل في إنشاء التقرير")
        
        print()
    
    print(f"تم الانتهاء من الاختبار!")
    print(f"يمكنك العثور على ملفات الاختبار في مجلد: {test_dir}")
    print("\nملاحظة: الارتفاعات الجديدة:")
    print("- 45 صفاً أو أكثر: 12 نقطة")
    print("- بين 35-44 صفاً: 13 نقطة")
    print("- أقل من 35 صفاً: 14 نقطة")

if __name__ == "__main__":
    test_row_heights()
