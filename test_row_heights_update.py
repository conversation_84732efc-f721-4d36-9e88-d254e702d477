#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف اختبار للتأكد من تحديث أحجام ارتفاع الصفوف في التقارير النصف الأسبوعية
"""

import datetime
import os
from split_attendance_report import SplitAttendanceReport
from split_attendance_report_10 import SplitAttendanceReport10
from split_attendance_report_second_half_10 import SplitAttendanceReportSecondHalf10

def create_test_students(count):
    """إنشاء قائمة طلاب للاختبار"""
    students = []
    for i in range(1, count + 1):
        students.append({
            "id": i,
            "name": f"الطالب رقم {i}",
            "rt": str(i)
        })
    return students

def test_row_heights_update():
    """اختبار أحجام ارتفاع الصفوف المحدثة"""
    
    # بيانات المؤسسة للاختبار
    institution_data = {
        "academy": "الأكاديمية الجهوية للتربية والتكوين لجهة الدار البيضاء سطات",
        "directorate": "المديرية الإقليمية للتربية الوطنية بالنواصر",
        "institution": "الثانوية التأهيلية ابن خلدون",
        "academic_year": "2024/2025"
    }
    
    start_date = datetime.datetime.now()
    periods_8 = ["1", "2", "3", "4", "5", "6", "7", "8"]
    periods_10 = ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10"]
    
    # إنشاء قائمة طلاب للاختبار
    students = create_test_students(30)  # 30 طالب لاختبار ارتفاع 14 نقطة
    
    # إنشاء مجلد للاختبار
    test_dir = "test_row_heights_update"
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)
    
    print("اختبار أحجام ارتفاع الصفوف المحدثة في التقارير النصف الأسبوعية:")
    print("=" * 70)
    print("التحديثات المطبقة:")
    print("- مربع المؤسسة: 20 نقطة (كان 25)")
    print("- مربع القسم: 20 نقطة (كان 25)")
    print("- صفوف الرؤوس: 18 نقطة (كان 20)")
    print("- صفوف الطلاب: ديناميكي (12/13/14 نقطة)")
    print("=" * 70)
    
    # 1. اختبار تقرير الاثنين-الأربعاء (8 حصص)
    print("1. اختبار تقرير الاثنين-الأربعاء (8 حصص)...")
    report_generator = SplitAttendanceReport()
    
    file_path = os.path.join(test_dir, "test_monday_wednesday_8_updated.pdf")
    success = report_generator.generate_first_half(
        file_path,
        students,
        "1/1",
        institution_data,
        start_date,
        1,
        periods_8
    )
    
    if success:
        print(f"   ✓ تم إنشاء التقرير بنجاح: {file_path}")
        print("   ✓ مربع المؤسسة: 20 نقطة")
        print("   ✓ مربع القسم: 20 نقطة")
        print("   ✓ صفوف الرؤوس: 18 نقطة")
        print("   ✓ صفوف الطلاب: 14 نقطة (30 طالب < 35)")
    else:
        print("   ✗ فشل في إنشاء التقرير")
    
    # 2. اختبار تقرير الاثنين-الأربعاء (10 حصص)
    print("2. اختبار تقرير الاثنين-الأربعاء (10 حصص)...")
    report_10_generator = SplitAttendanceReport10()
    
    file_path = os.path.join(test_dir, "test_monday_wednesday_10_updated.pdf")
    success = report_10_generator.generate_pdf(
        file_path,
        students,
        "2/1",
        institution_data,
        start_date,
        1,
        periods_10,
        ["الأربعاء", "الثلاثاء", "الاثنين"]
    )
    
    if success:
        print(f"   ✓ تم إنشاء التقرير بنجاح: {file_path}")
        print("   ✓ مربع المؤسسة: 20 نقطة")
        print("   ✓ مربع القسم: 20 نقطة")
        print("   ✓ صفوف الرؤوس: 18 نقطة")
        print("   ✓ صفوف الطلاب: 14 نقطة (30 طالب < 35)")
    else:
        print("   ✗ فشل في إنشاء التقرير")
    
    # 3. اختبار تقرير الخميس-السبت (10 حصص)
    print("3. اختبار تقرير الخميس-السبت (10 حصص)...")
    thursday_10_generator = SplitAttendanceReportSecondHalf10()
    
    file_path = os.path.join(test_dir, "test_thursday_saturday_10_updated.pdf")
    success = thursday_10_generator.generate_pdf(
        file_path,
        students,
        "3/1",
        institution_data,
        start_date,
        1,
        periods_10,
        ["السبت", "الجمعة", "الخميس"]
    )
    
    if success:
        print(f"   ✓ تم إنشاء التقرير بنجاح: {file_path}")
        print("   ✓ مربع المؤسسة: 20 نقطة")
        print("   ✓ مربع القسم: 20 نقطة")
        print("   ✓ صفوف الرؤوس: 18 نقطة")
        print("   ✓ صفوف الطلاب: 14 نقطة (30 طالب < 35)")
    else:
        print("   ✗ فشل في إنشاء التقرير")
    
    print("\nتم الانتهاء من اختبار أحجام ارتفاع الصفوف المحدثة!")
    print(f"يمكنك العثور على ملفات الاختبار في مجلد: {test_dir}")
    print("\nملاحظات:")
    print("- جميع التقارير النصف الأسبوعية تستخدم الآن الأحجام المحدثة")
    print("- التوفير في المساحة: 5 نقاط لكل مربع + 2 نقطة لكل صف رأس")
    print("- هذا يوفر مساحة إضافية لمزيد من الطلاب أو تحسين التخطيط")

if __name__ == "__main__":
    test_row_heights_update()
