// Licensed to the Apache Software Foundation (ASF) under one
// or more contributor license agreements.  See the NOTICE file
// distributed with this work for additional information
// regarding copyright ownership.  The ASF licenses this file
// to you under the Apache License, Version 2.0 (the
// "License"); you may not use this file except in compliance
// with the License.  You may obtain a copy of the License at
//
//   http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing,
// software distributed under the License is distributed on an
// "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
// KIND, either express or implied.  See the License for the
// specific language governing permissions and limitations
// under the License.

#ifndef PARQUET_VERSION_H
#define PARQUET_VERSION_H

#define PARQUET_VERSION_MAJOR 20
#define PARQUET_VERSION_MINOR 0
#define PARQUET_VERSION_PATCH 0

#define PARQUET_SO_VERSION "2000"
#define PARQUET_FULL_SO_VERSION "2000.0.0"

// define the parquet created by version
#define CREATED_BY_VERSION "parquet-cpp-arrow version 20.0.0"

#endif  // PARQUET_VERSION_H
