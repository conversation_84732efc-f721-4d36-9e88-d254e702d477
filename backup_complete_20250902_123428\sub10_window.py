#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import sqlite3
import traceback
import json
from datetime import datetime, date
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from PyQt5.QtSql import *
from PyQt5.QtWebEngineWidgets import QWebEngineView
from PyQt5.QtWebChannel import QWebChannel

# تم إزالة جميع الاستيرادات الخارجية لجعل الملف مستقلاً

class WebBridge(QObject):
    """جسر الاتصال بين Python و JavaScript"""
    
    # إشارات لـ JavaScript
    dataLoaded = pyqtSignal(str)  # إرسال البيانات كـ JSON
    saveResult = pyqtSignal(bool, str)  # نتيجة الحفظ
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.main_window = None
    
    def set_main_window(self, main_window):
        """تعيين النافذة الرئيسية"""
        self.main_window = main_window
    
    @pyqtSlot(str, str, str)
    def save_student_data(self, phone1, phone2, notes=""):
        """حفظ بيانات التلميذ من JavaScript"""
        try:
            print(f"🔄 محاولة حفظ البيانات - الهاتف1: {phone1}, الهاتف2: {phone2}")
            print(f"📋 الملاحظات: {notes[:50]}..." if len(notes) > 50 else f"📋 الملاحظات: {notes}")
            
            # التحقق من وجود النافذة الرئيسية
            if not self.main_window:
                print("❌ خطأ: النافذة الرئيسية غير موجودة")
                self.saveResult.emit(False, "خطأ في النظام - النافذة الرئيسية غير موجودة")
                return
            
            # التحقق من وجود معرف التلميذ
            if not self.main_window.current_record_id:
                print("❌ خطأ: معرف التلميذ غير محدد")
                self.saveResult.emit(False, "لا يوجد تلميذ محدد للحفظ")
                return
            
            print(f"📝 معرف التلميذ المحدد: {self.main_window.current_record_id}")
            
            # التحقق من وجود قاعدة البيانات
            if not hasattr(self.main_window, 'db') or not self.main_window.db:
                print("❌ خطأ: قاعدة البيانات غير متوفرة")
                self.saveResult.emit(False, "خطأ في الاتصال بقاعدة البيانات")
                return
            
            # التحقق من صحة الاتصال بقاعدة البيانات
            if not self.main_window.db.isOpen():
                print("❌ خطأ: قاعدة البيانات مغلقة")
                self.saveResult.emit(False, "قاعدة البيانات غير متاحة")
                return
            
            print("✅ جميع التحققات الأولية نجحت - بدء عملية الحفظ")

            query = QSqlQuery(self.main_window.db)
            query.prepare("""
                UPDATE السجل_العام
                SET الهاتف_الأول = ?, الهاتف_الثاني = ?, ملاحظات = ?
                WHERE الرمز = ?
            """)
            query.addBindValue(phone1)
            query.addBindValue(phone2) 
            query.addBindValue(notes)
            query.addBindValue(self.main_window.current_record_id)
            
            print(f"🔍 تنفيذ استعلام التحديث للرمز: {self.main_window.current_record_id}")

            if query.exec_():
                # التحقق من عدد الصفوف المتأثرة
                affected_rows = query.numRowsAffected()
                print(f"✅ نجح تنفيذ الاستعلام - عدد الصفوف المتأثرة: {affected_rows}")
                
                if affected_rows > 0:
                    self.saveResult.emit(True, "تم حفظ التغييرات بنجاح")
                    print("🎉 تم حفظ البيانات بنجاح")
                else:
                    print("⚠️ تحذير: لم يتم تحديث أي صفوف - قد يكون الرمز غير موجود")
                    self.saveResult.emit(False, "لم يتم العثور على التلميذ لتحديث بياناته")
            else:
                error_text = query.lastError().text()
                print(f"❌ فشل في تنفيذ الاستعلام: {error_text}")
                self.saveResult.emit(False, f"فشل في حفظ التغييرات: {error_text}")

        except Exception as e:
            print(f"❌ خطأ استثنائي في حفظ البيانات: {str(e)}")
            import traceback
            traceback.print_exc()
            self.saveResult.emit(False, f"خطأ في حفظ البيانات: {str(e)}")
    
    @pyqtSlot()
    def refresh_data(self):
        """تحديث البيانات من JavaScript"""
        if self.main_window:
            self.main_window.load_student_data()
    
    @pyqtSlot(str)
    def show_message(self, message):
        """عرض رسالة من JavaScript"""
        if self.main_window:
            QMessageBox.information(self.main_window, "رسالة", message)
    
    @pyqtSlot(str)
    def search_student_by_code(self, student_code):
        """البحث عن تلميذ برمز معين باستخدام استعلامات متعددة للتأكد من العثور على البيانات"""
        try:
            if not self.main_window:
                self.saveResult.emit(False, "خطأ في النظام")
                return

            # تنظيف رمز التلميذ
            student_code = student_code.strip()
            if not student_code:
                self.saveResult.emit(False, "يرجى إدخال رمز التلميذ")
                return

            print(f"🔍 البحث عن التلميذ برمز: {student_code}")

            # البحث في قاعدة البيانات باستخدام عدة استعلامات
            query = QSqlQuery(self.main_window.db)
            found_student = False

            # قائمة الاستعلامات للبحث بترتيب الأولوية
            search_queries = []

            # الاستعلام الأول: البحث في السنة الدراسية الحالية إذا كانت محددة
            if hasattr(self.main_window, 'current_academic_year') and self.main_window.current_academic_year:
                search_queries.append({
                    'name': f'السنة الدراسية الحالية ({self.main_window.current_academic_year})',
                    'query': """
                    SELECT
                        s.الرمز, s.الاسم_والنسب, s.النوع, s.تاريخ_الازدياد, s.مكان_الازدياد,
                        l.السنة_الدراسية, l.القسم, l.المستوى, l.رت as الرقم_الترتيبي,
                        s.الهاتف_الأول, s.الهاتف_الثاني, s.ملاحظات
                    FROM السجل_العام s
                    LEFT JOIN اللوائح l ON s.الرمز = l.الرمز
                    WHERE s.الرمز = ? AND l.السنة_الدراسية = ?
                    LIMIT 1
                    """,
                    'params': [student_code, self.main_window.current_academic_year]
                })

            # الاستعلام الثاني: البحث في أحدث سنة دراسية
            search_queries.append({
                'name': 'أحدث سنة دراسية',
                'query': """
                SELECT
                    s.الرمز, s.الاسم_والنسب, s.النوع, s.تاريخ_الازدياد, s.مكان_الازدياد,
                    l.السنة_الدراسية, l.القسم, l.المستوى, l.رت as الرقم_الترتيبي,
                    s.الهاتف_الأول, s.الهاتف_الثاني, s.ملاحظات
                FROM السجل_العام s
                LEFT JOIN اللوائح l ON s.الرمز = l.الرمز
                WHERE s.الرمز = ? AND l.السنة_الدراسية IS NOT NULL
                ORDER BY l.السنة_الدراسية DESC
                LIMIT 1
                """,
                'params': [student_code]
            })

            # الاستعلام الثالث: البحث في السجل العام فقط
            search_queries.append({
                'name': 'السجل العام فقط',
                'query': """
                SELECT
                    s.الرمز, s.الاسم_والنسب, s.النوع, s.تاريخ_الازدياد, s.مكان_الازدياد,
                    '' as السنة_الدراسية, '' as القسم, '' as المستوى, '' as الرقم_الترتيبي,
                    s.الهاتف_الأول, s.الهاتف_الثاني, s.ملاحظات
                FROM السجل_العام s
                WHERE s.الرمز = ?
                LIMIT 1
                """,
                'params': [student_code]
            })

            # الاستعلام الرابع: البحث في جدول البيانات_التلاميذ (إذا كان موجوداً)
            search_queries.append({
                'name': 'جدول البيانات_التلاميذ',
                'query': """
                SELECT
                    b.الرمز, b.الاسم_والنسب, b.النوع, b.تاريخ_الازدياد, b.مكان_الازدياد,
                    b.السنة_الدراسية, b.القسم, b.المستوى, b.رت as الرقم_الترتيبي,
                    b.الهاتف_الأول, b.الهاتف_الثاني, b.ملاحظات
                FROM البيانات_التلاميذ b
                WHERE b.الرمز = ?
                ORDER BY b.السنة_الدراسية DESC
                LIMIT 1
                """,
                'params': [student_code]
            })

            # تجربة الاستعلامات واحداً تلو الآخر
            for search_info in search_queries:
                print(f"📋 تجربة البحث في: {search_info['name']}")

                query.prepare(search_info['query'])
                for param in search_info['params']:
                    query.addBindValue(param)

                if not query.exec_():
                    print(f"خطأ في تنفيذ الاستعلام ({search_info['name']}): {query.lastError().text()}")
                    continue

                if query.next():
                    found_student = True
                    print(f"✅ تم العثور على التلميذ في: {search_info['name']}")
                    break
                else:
                    print(f"❌ لم يتم العثور على التلميذ في: {search_info['name']}")

            if not found_student:
                print(f"❌ لم يتم العثور على التلميذ برمز: {student_code} في جميع الاستعلامات")

                # إخفاء شاشة التحميل
                hide_loading_js = """
                    document.getElementById('loading').style.display = 'none';
                    document.getElementById('content').style.display = 'block';
                """
                self.main_window.web_view.page().runJavaScript(hide_loading_js)

                self.saveResult.emit(False, f"لم يتم العثور على تلميذ برمز: {student_code}")
                return
            
            # إذا تم العثور على التلميذ، استخراج البيانات
            found_code = query.value(0) or ""
            found_name = query.value(1) or ""
            found_gender = query.value(2) or ""
            found_birth_date = query.value(3) or ""
            found_birth_place = query.value(4) or ""
            found_academic_year = query.value(5) or ""
            found_class = query.value(6) or ""
            found_level = query.value(7) or ""
            found_serial = query.value(8) or ""
            found_phone1 = query.value(9) or ""
            found_phone2 = query.value(10) or ""
            found_notes = query.value(11) or ""

            print(f"✅ تم العثور على التلميذ: {found_name} (رمز: {found_code})")
            print(f"📊 البيانات: السنة={found_academic_year}, القسم={found_class}, المستوى={found_level}")

            # تحديث المتغيرات في النافذة الرئيسية
            self.main_window.current_record_id = found_code
            if found_academic_year:
                self.main_window.current_academic_year = found_academic_year

            # إنشاء البيانات المنظمة
            student_data = {
                'code': found_code,
                'name': found_name,
                'gender': found_gender,
                'birth_date': found_birth_date,
                'birth_place': found_birth_place,
                'academic_year': found_academic_year,
                'level': found_level,
                'class_name': found_class,
                'serial_number': found_serial,
                'phone1': found_phone1,
                'phone2': found_phone2,
                'notes': found_notes
            }

            print(f"📋 البيانات المنظمة: {student_data}")

            # تحديث البيانات في HTML مباشرة
            self.main_window.update_html_data(student_data)

            # إخفاء شاشة التحميل وإظهار المحتوى
            hide_loading_js = """
                document.getElementById('loading').style.display = 'none';
                document.getElementById('content').style.display = 'block';
            """
            self.main_window.web_view.page().runJavaScript(hide_loading_js)

            self.saveResult.emit(True, f"تم العثور على التلميذ: {found_name} (رمز: {found_code})")
                
        except Exception as e:
            print(f"❌ خطأ في البحث: {str(e)}")
            import traceback
            traceback.print_exc()

            # تشخيص إضافي للمشكلة
            try:
                if hasattr(self.main_window, 'db') and self.main_window.db:
                    # فحص الاتصال بقاعدة البيانات
                    test_query = QSqlQuery(self.main_window.db)
                    if test_query.exec_("SELECT COUNT(*) FROM السجل_العام"):
                        if test_query.next():
                            count = test_query.value(0)
                            print(f"📊 عدد السجلات في السجل_العام: {count}")

                    # فحص وجود الرمز في قاعدة البيانات
                    search_query = QSqlQuery(self.main_window.db)
                    search_query.prepare("SELECT الرمز, الاسم_والنسب FROM السجل_العام WHERE الرمز LIKE ?")
                    search_query.addBindValue(f"%{student_code}%")
                    if search_query.exec_():
                        similar_codes = []
                        while search_query.next() and len(similar_codes) < 5:
                            similar_codes.append(f"{search_query.value(0)} - {search_query.value(1)}")
                        if similar_codes:
                            print(f"🔍 رموز مشابهة موجودة: {similar_codes}")
                        else:
                            print("❌ لا توجد رموز مشابهة")
                else:
                    print("❌ لا يوجد اتصال بقاعدة البيانات")
            except Exception as diag_error:
                print(f"خطأ في التشخيص: {diag_error}")

            # إخفاء شاشة التحميل حتى في حالة الخطأ
            hide_loading_js = """
                document.getElementById('loading').style.display = 'none';
                document.getElementById('content').style.display = 'block';
            """
            self.main_window.web_view.page().runJavaScript(hide_loading_js)

            self.saveResult.emit(False, f"خطأ في البحث: {str(e)}")

# فحص توفر قاعدة البيانات قبل تشغيل البرنامج
def check_database_availability():
    """التحقق من وجود قاعدة البيانات ومدى إمكانية الوصول إليها"""
    db_path = "data.db"
    if not os.path.exists(db_path):
        print(f"تحذير: ملف قاعدة البيانات {db_path} غير موجود")
        return False
    
    try:
        # محاولة الاتصال بقاعدة البيانات للتأكد من صحتها
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # التحقق من وجود الجداول المطلوبة
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='students'")
        if not cursor.fetchone():
            print("تحذير: جدول students غير موجود في قاعدة البيانات")
            conn.close()
            return False
        
        conn.close()
        return True
    except Exception as e:
        print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
        return False

class StudentCardWindow(QMainWindow):
    """نافذة بطاقة التلميذ - باستخدام HTML/CSS/JavaScript"""
    
    def __init__(self, external_db=None, external_academic_year=None):
        super().__init__()
        
        # تخزين المعاملات الخارجية
        self.external_db = external_db
        self.external_academic_year = external_academic_year
        
        # متغيرات النافذة
        self.db = None
        self.current_record_id = None
        self.current_academic_year = None
        
        # إنشاء جسر الاتصال مع JavaScript
        self.web_bridge = WebBridge()
        self.web_bridge.set_main_window(self)
        
        # إعداد النافذة
        self.setupUI()
        
        # إعداد قاعدة البيانات والسنة الدراسية
        if self.connect_to_database():
            self.load_current_academic_year()
            # تحميل أول تلميذ للاختبار إذا لم يتم تمرير رمز خارجي
            if not self.current_record_id:
                print("تحميل أول تلميذ للاختبار...")
                self.load_first_student_for_testing()
        
    def setupUI(self):
        """إعداد واجهة المستخدم باستخدام HTML"""
        # إعداد النافذة الرئيسية
        self.setWindowTitle("بطاقة تلميذ")
        self.setLayoutDirection(Qt.RightToLeft)
        
        # إضافة أيقونة البرنامج
        try:
            icon_path = "01.ico"
            if os.path.exists(icon_path):
                self.setWindowIcon(QIcon(icon_path))
                print(f"✅ تم تحميل أيقونة البرنامج: {icon_path}")
            else:
                print(f"⚠️ تحذير: ملف الأيقونة غير موجود: {icon_path}")
        except Exception as e:
            print(f"❌ خطأ في تحميل الأيقونة: {e}")
        
        # فتح النافذة في كامل الشاشة
        self.showMaximized()
        
        # تطبيق نمط للنافذة الرئيسية
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f0f4f8,
                    stop: 1 #e8f2f7
                );
            }
        """)
        
        # إنشاء الواجهة المركزية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # إنشاء عارض الويب
        self.web_view = QWebEngineView()
        
        # إعداد قناة الويب للتواصل مع JavaScript
        self.web_channel = QWebChannel()
        self.web_channel.registerObject("bridge", self.web_bridge)
        self.web_view.page().setWebChannel(self.web_channel)
        
        # تحميل محتوى HTML
        self.load_html_content()
        
        layout.addWidget(self.web_view)

    def load_html_content(self):
        """تحميل محتوى HTML للبطاقة"""
        html_content = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>بطاقة التلميذ</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
        }
        
        .card-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            border: 2px solid #ddd;
            overflow: hidden;
        }

        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }
        
        .card-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="white" opacity="0.1"><polygon points="1000,100 1000,0 0,100"/></svg>');
            background-size: cover;
        }
        
        .card-header h1 {
            font-size: 28px;
            font-family: 'Calibri', sans-serif;
            font-weight: bold;
            color: #000000;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }
        
        .card-header p {
            font-size: 18px;
            font-family: 'Calibri', sans-serif;
            font-weight: bold;
            color: #000000;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }
        
        .search-section {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 12px;
            padding: 20px;
            margin-top: 20px;
            position: relative;
            z-index: 1;
        }
        
        .search-container {
            display: flex;
            align-items: center;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .search-label {
            color: #000000;
            font-weight: bold;
            font-size: 16px;
            white-space: nowrap;
        }
        
        .search-input {
            background: white;
            border: 2px solid #1565c0;
            border-radius: 8px;
            padding: 8px 12px;
            font-size: 14px;
            font-weight: bold;
            min-width: 200px;
            max-width: 300px;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .search-input:focus {
            outline: none;
            border-color: #0d47a1;
            box-shadow: 0 0 5px rgba(13, 71, 161, 0.5);
        }
        
        .search-btn {
            background: #1565c0;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
        }
        
        .search-btn:hover {
            background: #0d47a1;
        }
        
        .card-content {
            padding: 40px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .info-section {
            background: #f8f9ff;
            border-radius: 15px;
            padding: 25px;
            border-right: 5px solid #667eea;
            border: 1px solid #e0e0e0;
        }
        
        .section-title {
            color: #667eea;
            font-size: 20px;
            font-family: 'Calibri', sans-serif;
            font-weight: bold;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .field-group {
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
            justify-content: flex-start;
        }
        
        .field-label {
            background: white;
            color: #1a237e;
            font-weight: bold;
            font-size: 17px;
            font-family: 'Calibri', sans-serif;
            padding: 12px 18px;
            border-radius: 10px;
            min-width: 140px;
            text-align: center;
            white-space: nowrap;
            border: 2px solid #1a237e;
        }
        
        .field-value {
            background: white;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            padding: 12px 18px;
            font-size: 17px;
            font-family: 'Calibri', sans-serif;
            font-weight: bold;
            color: #1a1a1a;
            transition: all 0.3s ease;
            width: 300px;
            min-height: 24px;
        }
        
        .field-value:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .field-value:read-only {
            background: #f8f9fa;
            color: #666;
        }
        
        .notes-section {
            background: #fff5f5;
            border-radius: 15px;
            padding: 25px;
            border-right: 5px solid #ff6b6b;
            margin-bottom: 30px;
        }
        
        .notes-textarea {
            width: 100%;
            min-height: 120px;
            background: white;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Calibri', sans-serif;
            font-size: 17px;
            font-weight: bold;
            color: #1a1a1a;
            resize: vertical;
            transition: all 0.3s ease;
        }
        
        .notes-textarea:focus {
            outline: none;
            border-color: #ff6b6b;
            box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
        }
        
        .button-container {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }
        
        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 50px;
            font-size: 17px;
            font-family: 'Calibri', sans-serif;
            font-weight: bold;
            color: #000000;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 10px;
            min-width: 150px;
            justify-content: center;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            color: #2d3436;
        }
        
        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(250, 177, 160, 0.3);
        }
        
        .status-message {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            font-size: 17px;
            font-family: 'Calibri', sans-serif;
            font-weight: bold;
            opacity: 0;
            transition: all 0.3s ease;
        }
        
        .status-message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status-message.show {
            opacity: 1;
        }
        
        .loading {
            display: none;
            text-align: center;
padding: 20px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @media (max-width: 768px) {
            .info-grid {
                grid-template-columns: 1fr;
            }
            
            .button-container {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 100%;
                max-width: 250px;
            }
        }
    </style>
</head>
<body>
    <div class="card-container">
        <div class="card-header">
            <h1>📚 بطاقة التلميذ</h1>
            <p>نظام إدارة بيانات التلاميذ</p>
            
            <!-- قسم البحث برمز التلميذ -->
            <div class="search-section">
                <div class="search-container">
                    <label class="search-label">🔍 البحث برمز التلميذ:</label>
                    <input type="text" id="studentCodeSearch" class="search-input" placeholder="أدخل رمز التلميذ...">
                    <button class="search-btn" onclick="searchStudent()">بحث</button>
                </div>
            </div>
        </div>
        
        <div class="card-content">
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>جارٍ تحميل البيانات...</p>
            </div>
            
            <div id="content" style="display: none;">
                <div class="info-grid">
                    <!-- قسم البيانات الأساسية -->
                    <div class="info-section">
                        <div class="section-title">
                            <span>🆔</span>
                            المعلومات الأساسية
                        </div>
                        <div class="field-group">
                            <div class="field-label">الرمز</div>
                            <input type="text" id="code" class="field-value" readonly>
                        </div>
                        <div class="field-group">
                            <div class="field-label">الاسم والنسب</div>
                            <input type="text" id="name" class="field-value" readonly>
                        </div>
                        <div class="field-group">
                            <div class="field-label">النوع</div>
                            <input type="text" id="gender" class="field-value" readonly>
                        </div>
                    </div>
                    
                    <!-- قسم البيانات الشخصية -->
                    <div class="info-section">
                        <div class="section-title">
                            <span>👤</span>
                            البيانات الشخصية
                        </div>
                        <div class="field-group">
                            <div class="field-label">تاريخ الازدياد</div>
                            <input type="text" id="birthDate" class="field-value" readonly>
                        </div>
                        <div class="field-group">
                            <div class="field-label">مكان الازدياد</div>
                            <input type="text" id="birthPlace" class="field-value" readonly>
                        </div>
                    </div>
                    
                    <!-- قسم المعلومات المدرسية -->
                    <div class="info-section">
                        <div class="section-title">
                            <span>🏫</span>
                            المعلومات المدرسية
                        </div>
                        <div class="field-group">
                            <div class="field-label">السنة الدراسية</div>
                            <input type="text" id="schoolYear" class="field-value" readonly>
                        </div>
                        <div class="field-group">
                            <div class="field-label">المستوى</div>
                            <input type="text" id="level" class="field-value" readonly>
                        </div>
                        <div class="field-group">
                            <div class="field-label">القسم</div>
                            <input type="text" id="class" class="field-value" readonly>
                        </div>
                        <div class="field-group">
                            <div class="field-label">الرقم الترتيبي</div>
                            <input type="text" id="rt" class="field-value" readonly>
                        </div>
                    </div>
                    
                    <!-- قسم معلومات الاتصال -->
                    <div class="info-section">
                        <div class="section-title">
                            <span>📞</span>
                            معلومات الاتصال
                        </div>
                        <div class="field-group">
                            <div class="field-label">الهاتف الأول</div>
                            <input type="text" id="phone1" class="field-value" placeholder="أدخل رقم الهاتف الأول">
                        </div>
                        <div class="field-group">
                            <div class="field-label">الهاتف الثاني</div>
                            <input type="text" id="phone2" class="field-value" placeholder="أدخل رقم الهاتف الثاني">
                        </div>
                    </div>
                </div>
                
                <!-- قسم الملاحظات -->
                <div class="notes-section">
                    <div class="section-title">
                        <span>📝</span>
                        الملاحظات
                    </div>
                    <textarea id="notes" class="notes-textarea" placeholder="أضف ملاحظاتك هنا..."></textarea>
                </div>
                
                <!-- أزرار التحكم -->
                <div class="button-container">
                    <button class="btn btn-primary" onclick="saveData()">
                        <span>💾</span>
                        حفظ التغييرات
                    </button>
                    <button class="btn btn-secondary" onclick="refreshData()">
                        <span>🔄</span>
                        تحديث البيانات
                    </button>
                </div>
                
                <div id="statusMessage" class="status-message"></div>
            </div>
        </div>
    </div>

    <script src="qrc:///qtwebchannel/qwebchannel.js"></script>
    <script>
        let bridge = null;
        
        // تهيئة قناة التواصل مع Python
        new QWebChannel(qt.webChannelTransport, function(channel) {
            bridge = channel.objects.bridge;
            
            // الاستماع للإشارات من Python
            bridge.dataLoaded.connect(function(jsonData) {
                loadStudentData(JSON.parse(jsonData));
            });
            
            bridge.saveResult.connect(function(success, message) {
                console.log('📨 استلام نتيجة الحفظ من Python:');
                console.log('✅ نجح:', success);
                console.log('💬 الرسالة:', message);
                
                showMessage(success ? 'success' : 'error', message);
                
                // مسح حقل البحث عند النجاح
                if (success && document.getElementById('studentCodeSearch')) {
                    document.getElementById('studentCodeSearch').value = '';
                    console.log('🧹 تم مسح حقل البحث');
                }
                
                // إعادة تحميل البيانات عند النجاح للتأكد من التحديث
                if (success && bridge) {
                    console.log('🔄 إعادة تحميل البيانات للتأكد من التحديث');
                    setTimeout(() => {
                        bridge.refresh_data();
                    }, 500);
                }
            });
            
            // إخفاء شاشة التحميل وعرض المحتوى
            hideLoading();
        });
        
        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('content').style.display = 'block';
        }
        
        function loadStudentData(data) {
            // تحميل البيانات في الحقول
            document.getElementById('code').value = data.code || '';
            document.getElementById('name').value = data.name || '';
            document.getElementById('gender').value = data.gender || '';
            document.getElementById('birthDate').value = data.birth_date || '';
            document.getElementById('birthPlace').value = data.birth_place || '';
            document.getElementById('schoolYear').value = data.academic_year || '';
            document.getElementById('level').value = data.level || '';
            document.getElementById('class').value = data.class_name || '';
            document.getElementById('rt').value = data.serial_number || '';
            document.getElementById('phone1').value = data.phone1 || '';
            document.getElementById('phone2').value = data.phone2 || '';
            document.getElementById('notes').value = data.notes || '';
        }
        
        // دالة تحديث البيانات (للاستدعاء من Python)
        function updateStudentData(data) {
            loadStudentData(data);
            hideLoading();
        }
        
        function saveData() {
            console.log('🔄 بدء عملية حفظ البيانات');
            
            const phone1 = document.getElementById('phone1').value;
            const phone2 = document.getElementById('phone2').value;
            const notes = document.getElementById('notes').value;
            
            console.log('📞 الهاتف الأول:', phone1);
            console.log('📞 الهاتف الثاني:', phone2);
            console.log('📝 الملاحظات:', notes.length > 50 ? notes.substring(0, 50) + '...' : notes);
            
            if (!bridge) {
                console.error('❌ خطأ: bridge غير متوفر');
                showMessage('error', 'خطأ في الاتصال مع النظام');
                return;
            }
            
            console.log('✅ bridge متوفر - إرسال البيانات');
            
            try {
                bridge.save_student_data(phone1, phone2, notes);
                console.log('📤 تم إرسال البيانات إلى Python');
            } catch (error) {
                console.error('❌ خطأ في إرسال البيانات:', error);
                showMessage('error', 'خطأ في إرسال البيانات');
            }
        }
        
        function refreshData() {
            if (bridge) {
                bridge.refresh_data();
            }
        }
        
        function searchStudent() {
            const studentCode = document.getElementById('studentCodeSearch').value.trim();
            
            if (!studentCode) {
                showMessage('error', 'يرجى إدخال رمز التلميذ');
                return;
            }
            
            // إظهار شاشة التحميل
            showLoading();
            
            if (bridge) {
                bridge.search_student_by_code(studentCode);
            } else {
                showMessage('error', 'خطأ في الاتصال مع النظام');
                hideLoading();
            }
        }
        
        function showLoading() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('content').style.display = 'none';
        }
        
        // إضافة حدث الضغط على Enter في حقل البحث
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('studentCodeSearch');
            if (searchInput) {
                searchInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        searchStudent();
                    }
                });
            }
        });
        
        function showMessage(type, message) {
            const statusDiv = document.getElementById('statusMessage');
            statusDiv.className = `status-message ${type} show`;
            statusDiv.textContent = message;
            
            // إخفاء الرسالة بعد 3 ثوانٍ
            setTimeout(() => {
                statusDiv.classList.remove('show');
            }, 3000);
        }
        
        // تأثيرات التفاعل
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير التركيز للحقول
            const inputs = document.querySelectorAll('.field-value, .notes-textarea');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.style.transform = 'scale(1.02)';
                });
                
                input.addEventListener('blur', function() {
                    this.parentElement.style.transform = 'scale(1)';
                });
            });
        });
    </script>
</body>
</html>
        """
        
        self.web_view.setHtml(html_content)

    def send_data_to_web(self, data):
        """إرسال البيانات إلى واجهة الويب"""
        json_data = json.dumps(data, ensure_ascii=False)
        self.web_bridge.dataLoaded.emit(json_data)

    def connect_to_database(self):
        """الاتصال بقاعدة البيانات"""
        try:
            print("🔌 بدء عملية الاتصال بقاعدة البيانات...")
            
            if self.external_db:
                self.db = self.external_db
                print("✅ تم استخدام قاعدة البيانات الخارجية")
                
                # التحقق من صحة الاتصال
                if self.db.isOpen():
                    print("✅ قاعدة البيانات الخارجية متصلة بنجاح")
                    return True
                else:
                    print("❌ قاعدة البيانات الخارجية غير متصلة")
                    return False
            else:
                print("🗃️ إنشاء اتصال جديد بقاعدة البيانات المحلية...")
                
                # التحقق من وجود ملف قاعدة البيانات
                db_file = "data.db"
                if not os.path.exists(db_file):
                    print(f"❌ ملف قاعدة البيانات غير موجود: {db_file}")
                    return False
                
                print(f"📁 ملف قاعدة البيانات موجود: {db_file}")
                
                self.db = QSqlDatabase.addDatabase("QSQLITE", "student_card_html_connection")
                self.db.setDatabaseName(db_file)

                if not self.db.open():
                    error_text = self.db.lastError().text()
                    print(f"❌ فشل في الاتصال بقاعدة البيانات: {error_text}")
                    return False
                
                print("✅ تم الاتصال بقاعدة البيانات المحلية بنجاح")
                
                # اختبار قاعدة البيانات
                test_query = QSqlQuery(self.db)
                if test_query.exec_("SELECT COUNT(*) FROM السجل_العام"):
                    if test_query.next():
                        count = test_query.value(0)
                        print(f"📊 عدد السجلات في السجل_العام: {count}")
                    else:
                        print("⚠️ تحذير: لا يمكن قراءة عدد السجلات")
                else:
                    print(f"⚠️ تحذير: فشل في اختبار قاعدة البيانات: {test_query.lastError().text()}")
                    
            return True
            
        except Exception as e:
            print(f"❌ خطأ استثنائي في الاتصال بقاعدة البيانات: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

    def load_current_academic_year(self):
        """تحميل السنة الدراسية الحالية"""
        try:
            if self.external_academic_year:
                self.current_academic_year = self.external_academic_year
                print(f"تم استخدام السنة الدراسية الخارجية: {self.current_academic_year}")
            else:
                # محاولة تحميل السنة الدراسية من قاعدة البيانات
                if hasattr(self, 'db') and self.db:
                    query = QSqlQuery(self.db)
                    query.exec_("SELECT academic_year FROM settings LIMIT 1")
                    if query.next():
                        self.current_academic_year = query.value(0)
                        print(f"تم تحميل السنة الدراسية: {self.current_academic_year}")
                    else:
                        # استخدام السنة الحالية كافتراضي
                        current_year = datetime.now().year
                        self.current_academic_year = f"{current_year}-{current_year + 1}"
                        print(f"تم استخدام السنة الدراسية الافتراضية: {self.current_academic_year}")
                else:
                    current_year = datetime.now().year
                    self.current_academic_year = f"{current_year}-{current_year + 1}"
                    print(f"تم استخدام السنة الدراسية الافتراضية: {self.current_academic_year}")
        except Exception as e:
            print(f"خطأ في تحميل السنة الدراسية: {e}")
            current_year = datetime.now().year
            self.current_academic_year = f"{current_year}-{current_year + 1}"

    def load_student_data(self):
        """تحميل بيانات التلميذ وعرضها في HTML باستخدام الاستعلام المركزي"""
        try:
            print(f"التشخيص load_student_data: current_record_id = {self.current_record_id}")
            print(f"التشخيص load_student_data: current_academic_year = {self.current_academic_year}")
            
            if not self.current_record_id:
                print("التشخيص: current_record_id فارغ - لن يتم تحميل البيانات")
                return

            # تحميل البيانات الأساسية باستخدام الاستعلام المركزي
            if hasattr(self, 'db') and self.db:
                query = QSqlQuery(self.db)
                
                # البحث أولاً في السنة الدراسية الحالية
                if self.current_academic_year:
                    central_query = """
                    SELECT 
                        s.الرمز,
                        s.الاسم_والنسب,
                        s.النوع,
                        s.تاريخ_الازدياد,
                        s.مكان_الازدياد,
                        l.السنة_الدراسية,
                        l.القسم,
                        l.المستوى,
                        l.رت as الرقم_الترتيبي,
                        s.الهاتف_الأول,
                        s.الهاتف_الثاني,
                        s.ملاحظات
                    FROM السجل_العام s
                    LEFT JOIN اللوائح l ON s.الرمز = l.الرمز
                    WHERE s.الرمز = ? AND l.السنة_الدراسية = ?
                    ORDER BY l.السنة_الدراسية DESC
                    LIMIT 1
                    """
                    query.prepare(central_query)
                    query.addBindValue(self.current_record_id)
                    query.addBindValue(self.current_academic_year)
                else:
                    # إذا لم تكن السنة الدراسية محددة، ابحث في جميع السنوات
                    central_query = """
                    SELECT 
                        s.الرمز,
                        s.الاسم_والنسب,
                        s.النوع,
                        s.تاريخ_الازدياد,
                        s.مكان_الازدياد,
                        l.السنة_الدراسية,
                        l.القسم,
                        l.المستوى,
                        l.رت as الرقم_الترتيبي,
                        s.الهاتف_الأول,
                        s.الهاتف_الثاني,
                        s.ملاحظات
                    FROM السجل_العام s
                    LEFT JOIN اللوائح l ON s.الرمز = l.الرمز
                    WHERE s.الرمز = ? AND l.السنة_الدراسية IS NOT NULL
                    ORDER BY l.السنة_الدراسية DESC
                    LIMIT 1
                    """
                    query.prepare(central_query)
                    query.addBindValue(self.current_record_id)
                
                print(f"التشخيص: تنفيذ الاستعلام المركزي للرمز: {self.current_record_id}, السنة: {self.current_academic_year}")

                if query.exec_():
                    print("التشخيص: تم تنفيذ الاستعلام المركزي بنجاح")
                    if query.next():
                        print("التشخيص: تم العثور على بيانات التلميذ")
                        
                        # تجميع البيانات
                        student_data = {
                            'code': query.value(0) or "",
                            'name': query.value(1) or "",
                            'gender': query.value(2) or "",
                            'birth_date': query.value(3) or "",
                            'birth_place': query.value(4) or "",
                            'academic_year': query.value(5) or "",
                            'class_name': query.value(6) or "",
                            'level': query.value(7) or "",
                            'serial_number': query.value(8) or "",
                            'phone1': query.value(9) or "",
                            'phone2': query.value(10) or "",
                            'notes': query.value(11) or ""
                        }

                        print(f"التشخيص: تم تحديث البيانات - الاسم: {student_data['name']}")
                        
                        # تحديث البيانات في HTML
                        self.update_html_data(student_data)
                        
                    else:
                        print(f"التشخيص: لم يتم العثور على بيانات للتلميذ: {self.current_record_id}")
                        # إضافة استعلام تشخيصي للتحقق من وجود البيانات
                        debug_query = QSqlQuery(self.db)
                        debug_query.prepare("SELECT COUNT(*) FROM السجل_العام WHERE الرمز = ?")
                        debug_query.addBindValue(self.current_record_id)
                        if debug_query.exec_() and debug_query.next():
                            count = debug_query.value(0)
                            print(f"التشخيص: عدد السجلات للرمز {self.current_record_id}: {count}")
                        
                        # التحقق من السنة الدراسية
                        year_query = QSqlQuery(self.db)
                        year_query.prepare("SELECT DISTINCT l.السنة_الدراسية FROM اللوائح l WHERE l.الرمز = ?")
                        year_query.addBindValue(self.current_record_id)
                        if year_query.exec_():
                            years = []
                            while year_query.next():
                                years.append(year_query.value(0))
                            print(f"التشخيص: السنوات الدراسية المتاحة للرمز {self.current_record_id}: {years}")
                        
                        # إخفاء شاشة التحميل حتى لو لم نجد البيانات
                        hide_loading_js = """
                            document.getElementById('loading').style.display = 'none';
                            document.getElementById('content').style.display = 'block';
                        """
                        self.web_view.page().runJavaScript(hide_loading_js)
                else:
                    print(f"التشخيص: فشل في تنفيذ الاستعلام: {query.lastError().text()}")
                    # إخفاء شاشة التحميل حتى في حالة الخطأ
                    hide_loading_js = """
                        document.getElementById('loading').style.display = 'none';
                        document.getElementById('content').style.display = 'block';
                    """
                    self.web_view.page().runJavaScript(hide_loading_js)
            else:
                print("التشخيص: قاعدة البيانات غير متوفرة")

        except Exception as e:
            print(f"خطأ في تحميل بيانات التلميذ: {e}")
            import traceback
            traceback.print_exc()
            # إخفاء شاشة التحميل حتى في حالة الخطأ
            hide_loading_js = """
                document.getElementById('loading').style.display = 'none';
                document.getElementById('content').style.display = 'block';
            """
            self.web_view.page().runJavaScript(hide_loading_js)

    def update_html_data(self, data):
        """تحديث البيانات في HTML باستخدام JSON آمن"""
        try:
            import json
            # طباعة البيانات للتشخيص
            print(f"تحديث البيانات: {data}")
            
            # تنظيف البيانات من القيم الفارغة None وتجنب الرموز الخاصة
            cleaned_data = {}
            for key, value in data.items():
                if value is not None:
                    # تنظيف الرموز الخاصة التي قد تسبب مشاكل في JavaScript
                    clean_value = str(value).replace("'", "&#39;").replace('"', "&#34;").replace('\n', ' ').replace('\r', ' ')
                    cleaned_data[key] = clean_value
                else:
                    cleaned_data[key] = ""
            
            # استخدام JSON.parse بدلاً من التحديث المباشر لتجنب مشاكل الرموز الخاصة
            js_code = f"""
                console.log('تحديث البيانات باستخدام الاستعلام المركزي');
                
                try {{
                    var studentData = {json.dumps(cleaned_data, ensure_ascii=False)};
                    console.log('البيانات المستلمة:', studentData);
                    
                    // تحديث الحقول باستخدام البيانات المُنظفة
                    if (document.getElementById('code')) document.getElementById('code').value = studentData.code || '';
                    if (document.getElementById('name')) document.getElementById('name').value = studentData.name || '';
                    if (document.getElementById('gender')) document.getElementById('gender').value = studentData.gender || '';
                    if (document.getElementById('birthDate')) document.getElementById('birthDate').value = studentData.birth_date || '';
                    if (document.getElementById('birthPlace')) document.getElementById('birthPlace').value = studentData.birth_place || '';
                    if (document.getElementById('schoolYear')) document.getElementById('schoolYear').value = studentData.academic_year || '';
                    if (document.getElementById('level')) document.getElementById('level').value = studentData.level || '';
                    if (document.getElementById('class')) document.getElementById('class').value = studentData.class_name || '';
                    if (document.getElementById('rt')) document.getElementById('rt').value = studentData.serial_number || '';
                    if (document.getElementById('phone1')) document.getElementById('phone1').value = studentData.phone1 || '';
                    if (document.getElementById('phone2')) document.getElementById('phone2').value = studentData.phone2 || '';
                    if (document.getElementById('notes')) document.getElementById('notes').value = studentData.notes || '';
                    
                    console.log('تم تحديث جميع الحقول بنجاح باستخدام الاستعلام المركزي');
                    
                    // التأكد من إخفاء شاشة التحميل وإظهار المحتوى
                    document.getElementById('loading').style.display = 'none';
                    document.getElementById('content').style.display = 'block';
                    
                }} catch (error) {{
                    console.error('خطأ في تحديث الحقول:', error);
                }}
            """
            
            # تنفيذ الكود مع callback للتحقق من النتيجة
            def callback_result(result):
                print(f"نتيجة تنفيذ JavaScript: {result}")
            
            self.web_view.page().runJavaScript(js_code, callback_result)
            print("تم إرسال البيانات إلى HTML باستخدام الاستعلام المركزي")
        except Exception as e:
            print(f"خطأ في تحديث HTML: {e}")
            import traceback
            traceback.print_exc()

    def save_contact_info(self):
        """حفظ معلومات الاتصال والملاحظات"""
        try:
            if not self.current_record_id:
                self.show_message("تنبيه", "لا يوجد تلميذ محدد للحفظ")
                return

            # الحصول على البيانات من JavaScript
            js_code = """
                JSON.stringify({
                    phone1: document.getElementById('phone1').value,
                    phone2: document.getElementById('phone2').value,
                    notes: document.getElementById('notes').value
                });
            """
            
            def handle_data(result):
                try:
                    import json
                    contact_data = json.loads(result)
                    
                    if hasattr(self, 'db') and self.db:
                        query = QSqlQuery(self.db)
                        query.prepare("""
                            UPDATE السجل_العام
                            SET الهاتف_الأول = ?, الهاتف_الثاني = ?, ملاحظات = ?
                            WHERE الرمز = ?
                        """)
                        query.addBindValue(contact_data['phone1'])
                        query.addBindValue(contact_data['phone2'])
                        query.addBindValue(contact_data['notes'])
                        query.addBindValue(self.current_record_id)

                        if query.exec_():
                            self.show_message("نجح", "تم حفظ التغييرات بنجاح")
                        else:
                            self.show_message("خطأ", f"فشل في حفظ التغييرات: {query.lastError().text()}")
                    
                except Exception as e:
                    self.show_message("خطأ", f"خطأ في حفظ البيانات: {str(e)}")
            
            self.web_view.page().runJavaScript(js_code, handle_data)

        except Exception as e:
            self.show_message("خطأ", f"خطأ في حفظ البيانات: {str(e)}")

    def show_message(self, title, message):
        """عرض رسالة للمستخدم"""
        try:
            from PyQt5.QtWidgets import QMessageBox
            if title == "نجح":
                QMessageBox.information(self, title, message)
            elif title == "خطأ":
                QMessageBox.critical(self, title, message)
            else:
                QMessageBox.warning(self, title, message)
        except:
            print(f"{title}: {message}")

    def show_student_data(self, student_code, academic_year=None):
        """عرض بيانات تلميذ محدد"""
        try:
            print(f"show_student_data: تم استدعاء عرض بيانات التلميذ {student_code}")
            
            # تعيين رمز التلميذ والسنة الدراسية
            self.current_record_id = student_code
            if academic_year:
                self.current_academic_year = academic_year
            
            # تحميل البيانات
            self.load_student_data()
            
            # إظهار النافذة في كامل الشاشة
            self.showMaximized()
            self.activateWindow()
            
        except Exception as e:
            print(f"خطأ في عرض بيانات التلميذ: {e}")
            import traceback
            traceback.print_exc()

    def closeEvent(self, event):
        """معالجة حدث إغلاق النافذة"""
        try:
            # إغلاق اتصال قاعدة البيانات إذا كان محلياً
            if hasattr(self, 'db') and self.db and not self.external_db:
                self.db.close()
                print("تم إغلاق اتصال قاعدة البيانات")
        except Exception as e:
            print(f"خطأ في إغلاق قاعدة البيانات: {e}")

        # قبول حدث الإغلاق
        event.accept()

    def load_first_student_for_testing(self):
        """تحميل أول تلميذ متوفر للاختبار"""
        try:
            if hasattr(self, 'db') and self.db:
                query = QSqlQuery(self.db)
                query.prepare("""
                    SELECT DISTINCT l.الرمز 
                    FROM اللوائح l
                    WHERE l.السنة_الدراسية = ?
                    ORDER BY l.الرمز ASC
                    LIMIT 1
                """)
                query.addBindValue(self.current_academic_year)
                
                if query.exec_() and query.next():
                    student_code = query.value(0)
                    print(f"تم العثور على أول تلميذ للاختبار: {student_code}")
                    self.current_record_id = student_code
                    
                    # تأخير تحميل البيانات قليلاً للتأكد من تحميل HTML
                    from PyQt5.QtCore import QTimer
                    QTimer.singleShot(1000, self.load_student_data)
                else:
                    print("لم يتم العثور على أي طلاب في قاعدة البيانات")
            else:
                print("قاعدة البيانات غير متوفرة")
        except Exception as e:
            print(f"خطأ في تحميل أول تلميذ: {e}")


def main():
    """الدالة الرئيسية لتشغيل النافذة"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # التحقق من وجود قاعدة البيانات
    if not check_database_availability():
        print("⚠️ تحذير: قاعدة البيانات غير متوفرة - سيتم تشغيل النافذة بوضع التجريب")
    
    # إنشاء النافذة
    window = StudentCardWindow()
    window.showMaximized()  # فتح في كامل الشاشة
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
