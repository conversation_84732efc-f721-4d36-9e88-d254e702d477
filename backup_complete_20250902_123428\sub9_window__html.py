import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout)
from PyQt5.QtWebEngineWidgets import QWebEngineView
from PyQt5.QtGui import QFont, QIcon
from PyQt5.QtCore import Qt, QObject, pyqtSignal, pyqtSlot
from PyQt5.QtSql import QSqlQuery
import sqlite3
import json
from database_config import get_database_path, get_database_connection

class WebBridge(QObject):
    """فئة الجسر بين JavaScript و Python"""
    
    # إشارات للتفاعل مع النافذة الرئيسية
    dataRequested = pyqtSignal()
    listsDataRequested = pyqtSignal(str, str)  # code, name
    searchRequested = pyqtSignal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_window = parent
        
    @pyqtSlot()
    def refreshData(self):
        """طلب تحديث البيانات"""
        try:
            self.dataRequested.emit()
        except Exception as e:
            print(f"خطأ في تحديث البيانات: {e}")
            self.showMessage("حدث خطأ أثناء تحديث البيانات", "error")
    
    @pyqtSlot(str)
    def searchGeneral(self, search_text):
        """البحث في السجل العام"""
        try:
            self.searchRequested.emit(search_text)
        except Exception as e:
            print(f"خطأ في البحث: {e}")
            self.showMessage("حدث خطأ أثناء البحث", "error")
    
    @pyqtSlot(str, str)
    def loadListsData(self, code, name):
        """تحميل بيانات اللوائح للرمز المحدد"""
        try:
            self.listsDataRequested.emit(code, name)
        except Exception as e:
            print(f"خطأ في تحميل بيانات اللوائح: {e}")
            self.showMessage("حدث خطأ أثناء تحميل بيانات اللوائح", "error")
    
    @pyqtSlot(str)
    def copyCodeToClipboard(self, code):
        """نسخ الرمز إلى الحافظة"""
        try:
            from PyQt5.QtGui import QClipboard
            from PyQt5.QtWidgets import QApplication
            clipboard = QApplication.clipboard()
            clipboard.setText(code)
            self.showMessage(f"تم نسخ الرمز: {code}", "success")
        except Exception as e:
            print(f"خطأ في نسخ الرمز: {e}")
            self.showMessage("حدث خطأ أثناء نسخ الرمز", "error")
    
    def showMessage(self, message, message_type="info"):
        """عرض رسالة للمستخدم عبر JavaScript فقط"""
        try:
            message_data = json.dumps({"message": message, "type": message_type}, ensure_ascii=False)
            if self.parent_window and hasattr(self.parent_window, 'web_view'):
                self.parent_window.web_view.page().runJavaScript(f"showNotification({message_data});")
        except Exception as e:
            print(f"خطأ في عرض الرسالة: {e}")

class GeneralRecordHtmlWindow(QMainWindow):
    """نافذة بحث السجل العام واللوائح باستخدام HTML"""
    
    def __init__(self, parent=None, db=None, academic_year=None):
        super().__init__(parent)
        self.db = db
        self.db_path = get_database_path()
        self.using_qsql = bool(db and hasattr(db, 'isOpen') and db.isOpen())
        self.current_academic_year = academic_year or "2024/2025"
        
        self.setWindowTitle("نافذة بحث السجل العام واللوائح")
        self.setMinimumSize(1200, 800)
        
        # إعداد أيقونة النافذة
        try:
            icon_path = os.path.join(os.path.dirname(__file__), "01.ico")
            if os.path.exists(icon_path):
                self.setWindowIcon(QIcon(icon_path))
        except:
            pass
        
        self.setup_ui()
        self.setup_bridge()
        self.load_html_content()
        
        # إعداد خاص عند الدمج في النافذة الرئيسية
        if parent is not None:
            self.setup_for_embedding()
        
    def setup_for_embedding(self):
        """إعدادات خاصة عند دمج النافذة في النافذة الرئيسية"""
        try:
            # ضمان ظهور أسهم التمرير
            from PyQt5.QtWebEngineWidgets import QWebEngineSettings
            settings = self.web_view.settings()
            settings.setAttribute(QWebEngineSettings.ShowScrollBars, True)
            settings.setAttribute(QWebEngineSettings.ScrollAnimatorEnabled, True)
            settings.setAttribute(QWebEngineSettings.SpatialNavigationEnabled, True)
            
            # إعدادات إضافية للنافذة
            self.setMinimumSize(1000, 700)
            
            # تطبيق أنماط إضافية عبر JavaScript
            self.web_view.loadFinished.connect(self.apply_embedding_fixes)
            
        except Exception as e:
            print(f"خطأ في إعداد النافذة للدمج: {e}")
    
    def apply_embedding_fixes(self):
        """تطبيق إصلاحات JavaScript للدمج"""
        try:
            js_code = """
            // إجبار ظهور أسهم التمرير
            document.querySelectorAll('.table-wrapper').forEach(function(wrapper) {
                wrapper.style.overflowY = 'scroll';
                wrapper.style.overflowX = 'auto';
                wrapper.style.scrollbarWidth = 'auto';
                wrapper.style.msOverflowStyle = 'auto';
            });
            
            // التأكد من أن الجسم يدعم التمرير
            document.body.style.overflow = 'auto';
            document.documentElement.style.overflow = 'auto';
            
            console.log('تم تطبيق إصلاحات التمرير للدمج');
            """
            
            self.web_view.page().runJavaScript(js_code)
            
        except Exception as e:
            print(f"خطأ في تطبيق إصلاحات JavaScript: {e}")
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # إنشاء عارض الويب
        self.web_view = QWebEngineView()
        
        # إعدادات محسنة لتقليل التشنج
        from PyQt5.QtWebEngineWidgets import QWebEngineSettings
        settings = self.web_view.settings()
        settings.setAttribute(QWebEngineSettings.ShowScrollBars, True)
        settings.setAttribute(QWebEngineSettings.ScrollAnimatorEnabled, False)  # تعطيل الأنيميشن لتقليل التشنج
        settings.setAttribute(QWebEngineSettings.SpatialNavigationEnabled, True)
        settings.setAttribute(QWebEngineSettings.LocalContentCanAccessRemoteUrls, False)
        settings.setAttribute(QWebEngineSettings.LocalContentCanAccessFileUrls, False)
        settings.setAttribute(QWebEngineSettings.JavascriptCanOpenWindows, False)
        settings.setAttribute(QWebEngineSettings.JavascriptCanAccessClipboard, True)
        settings.setAttribute(QWebEngineSettings.LinksIncludedInFocusChain, False)
        settings.setAttribute(QWebEngineSettings.PlaybackRequiresUserGesture, False)
        
        # إعدادات خاصة للأداء
        self.web_view.setMinimumSize(800, 600)
        
        layout.addWidget(self.web_view)
        
    def setup_bridge(self):
        """إعداد الجسر بين Python و JavaScript"""
        self.bridge = WebBridge(self)
        
        # ربط الإشارات
        self.bridge.dataRequested.connect(self.load_general_data)
        self.bridge.listsDataRequested.connect(self.handle_legacy_lists_request)
        self.bridge.searchRequested.connect(self.search_general_data)
        
    def load_html_content(self):
        """تحميل محتوى HTML"""
        html_content = self.generate_html()
        self.web_view.setHtml(html_content)
        
        # تحميل البيانات بعد تحميل HTML
        self.web_view.loadFinished.connect(self.on_load_finished)
        
    def on_load_finished(self):
        """يتم استدعاؤها عند انتهاء تحميل الصفحة"""
        # إعداد القناة للتواصل مع JavaScript
        from PyQt5.QtWebChannel import QWebChannel
        
        self.channel = QWebChannel()
        self.channel.registerObject("bridge", self.bridge)
        self.web_view.page().setWebChannel(self.channel)
        
        # تطبيق إصلاحات التمرير
        self.apply_scrollbar_fixes()
        
        # لا نحمل البيانات تلقائياً - نتركها فارغة حتى يضغط المستخدم على زر التحديث
        # from PyQt5.QtCore import QTimer
        # QTimer.singleShot(500, self.load_general_data)
        
    def apply_scrollbar_fixes(self):
        """تطبيق إصلاحات خاصة لأسهم التمرير"""
        try:
            scrollbar_js = """
            // إجبار ظهور أسهم التمرير في جميع العناصر
            function forceScrollbars() {
                // للجداول
                const tableWrappers = document.querySelectorAll('.table-wrapper');
                tableWrappers.forEach(function(wrapper) {
                    wrapper.style.overflow = 'auto';
                    wrapper.style.overflowY = 'scroll';
                    wrapper.style.overflowX = 'auto';
                    
                    // إضافة محتوى وهمي مؤقت للتأكد من ظهور التمرير
                    const originalHeight = wrapper.style.maxHeight;
                    wrapper.style.maxHeight = '200px';
                    
                    // التأكد من أن المحتوى أطول من الحاوية
                    const table = wrapper.querySelector('table');
                    if (table) {
                        const tbody = table.querySelector('tbody');
                        if (tbody && tbody.children.length === 0) {
                            // إضافة صفوف وهمية مؤقتاً
                            for (let i = 0; i < 10; i++) {
                                const row = document.createElement('tr');
                                row.innerHTML = '<td colspan="7" style="height: 30px;">جاري التحميل...</td>';
                                tbody.appendChild(row);
                            }
                            
                            // إزالة الصفوف الوهمية بعد ثانية
                            setTimeout(() => {
                                if (tbody.children.length > 1) {
                                    while (tbody.children.length > 1) {
                                        tbody.removeChild(tbody.lastChild);
                                    }
                                }
                            }, 1000);
                        }
                    }
                });
                
                // للجسم
                document.body.style.overflow = 'auto';
                document.documentElement.style.overflow = 'auto';
                
                console.log('تم تطبيق إصلاحات أسهم التمرير');
            }
            
            // تطبيق الإصلاحات فوراً
            forceScrollbars();
            
            // إعادة تطبيق الإصلاحات عند تحديث البيانات
            setTimeout(forceScrollbars, 100);
            setTimeout(forceScrollbars, 500);
            setTimeout(forceScrollbars, 1000);
            """
            
            self.web_view.page().runJavaScript(scrollbar_js)
            
        except Exception as e:
            print(f"خطأ في تطبيق إصلاحات أسهم التمرير: {e}")
        
    def generate_html(self):
        """إنشاء محتوى HTML للنافذة"""
        return '''
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نافذة بحث السجل العام واللوائح</title>
    <script src="qrc:///qtwebchannel/qwebchannel.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Calibri', 'Tahoma', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
            padding: 20px;
            /* إعدادات التمرير للجسم */
            overflow: auto;
            scrollbar-width: auto;
            -ms-overflow-style: auto;
            /* تحسينات الأداء لمنع التشنج */
            transform: translateZ(0);
            backface-visibility: hidden;
            perspective: 1000;
        }
        
        /* تخصيص أسهم التمرير للجسم */
        body::-webkit-scrollbar {
            width: 14px;
        }
        
        body::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 7px;
        }
        
        body::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 7px;
        }
        
        body::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
            backdrop-filter: blur(10px);
            /* إعدادات التمرير للحاوية */
            overflow: visible;
            min-height: 600px;
            /* تحسينات الأداء */
            transform: translateZ(0);
            will-change: auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #4CAF50;
        }
        
        .header h1 {
            color: #0d47a1;
            font-family: 'Calibri', sans-serif;
            font-size: 22pt;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        .search-section {
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 25px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        
        .search-container {
            display: flex;
            gap: 15px;
            align-items: center;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .search-label {
            color: #0d47a1;
            font-family: 'Calibri', sans-serif;
            font-weight: bold;
            font-size: 17px;
            min-width: 80px;
        }
        
        .search-input {
            flex: 1;
            min-width: 300px;
            padding: 12px 20px;
            border: 2px solid #dce1e6;
            border-radius: 10px;
            font-family: 'Calibri', sans-serif;
            font-size: 14px;
            font-weight: bold;
            background-color: #f7f9fc;
            transition: all 0.3s ease;
        }
        
        .search-input:focus {
            border: 2px solid #1976d2;
            background-color: white;
            outline: none;
        }
        
        .search-input:disabled {
            background-color: #f5f5f5;
            color: #999;
            cursor: not-allowed;
            border-color: #ddd;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            font-family: 'Calibri', sans-serif;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .btn-clear {
            background: linear-gradient(45deg, #f44336, #e53935);
            color: white;
            min-width: 80px;
        }
        
        .btn-refresh {
            background: linear-gradient(45deg, #9b59b6, #8e44ad);
            color: white;
        }
        
        .results-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 10px;
        }
        
        .results-count {
            font-family: 'Calibri', sans-serif;
            font-weight: bold;
            color: #0d47a1;
            font-size: 17px;
        }
        
        .table-section {
            margin-bottom: 30px;
        }
        
        .table-title {
            font-family: 'Calibri', sans-serif;
            font-size: 17px;
            font-weight: bold;
            color: #0d47a1;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #1976d2;
        }
        
        .table-container {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .table-wrapper {
            max-height: 200px;
            overflow-y: auto;
            overflow-x: auto;
            border-radius: 10px;
            /* إجبار ظهور أسهم التمرير */
            scrollbar-width: auto; /* Firefox */
            -ms-overflow-style: auto; /* IE and Edge */
        }
        
        /* تخصيص أسهم التمرير للمتصفحات WebKit */
        .table-wrapper::-webkit-scrollbar {
            width: 14px;
            height: 14px;
            background: #f8f9fa;
        }
        
        .table-wrapper::-webkit-scrollbar-track {
            background: #e9ecef;
            border-radius: 7px;
            border: 1px solid #dee2e6;
        }
        
        .table-wrapper::-webkit-scrollbar-thumb {
            background: #6c757d;
            border-radius: 7px;
            border: 1px solid #495057;
        }
        
        .table-wrapper::-webkit-scrollbar-thumb:hover {
            background: #495057;
        }
        
        .table-wrapper::-webkit-scrollbar-corner {
            background: #e9ecef;
        }
        
        /* إجبار ظهور التمرير دائماً */
        .table-wrapper {
            scrollbar-width: thin; /* Firefox */
            scrollbar-color: #6c757d #e9ecef; /* Firefox */
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .table th {
            background: linear-gradient(45deg, #e3f2fd, #bbdefb);
            color: #0d47a1;
            padding: 15px 10px;
            text-align: center;
            font-family: 'Calibri', sans-serif;
            font-weight: bold;
            font-size: 17px;
            position: sticky;
            top: 0;
            z-index: 10;
            border-bottom: 2px solid #1976d2;
        }
        
        .table td {
            padding: 12px 10px;
            text-align: center;
            border-bottom: 1px solid #ecf0f1;
            font-family: 'Calibri', sans-serif;
            font-size: 17px;
            font-weight: bold;
            color: #1a1a1a;
        }
        
        .table tbody tr:hover {
            background-color: #e3f2fd;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }
        
        .table tbody tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .table tbody tr.selected {
            background-color: #bbdefb !important;
            color: #01579b;
            transition: background-color 0.2s ease;
        }
        
        .code-cell {
            color: #3498db;
            font-weight: bold;
            cursor: pointer;
            transition: color 0.2s ease;
        }
        
        .code-cell:hover {
            color: #2980b9;
            text-decoration: underline;
        }
        
        /* منع التحديد النصي للجداول لتقليل التشنج */
        .table tbody tr {
            user-select: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
        }
        
        /* تحسين أداء العرض */
        .table-wrapper {
            will-change: scroll-position;
            contain: layout style paint;
        }
        
        .table {
            will-change: contents;
        }
        
        .loading {
            text-align: center;
            padding: 50px;
            font-size: 18px;
            color: #7f8c8d;
            animation: pulse 1.5s ease-in-out infinite alternate;
        }
        
        @keyframes pulse {
            from { opacity: 0.6; }
            to { opacity: 1; }
        }
        
        .no-data {
            text-align: center;
            padding: 50px;
            font-size: 16px;
            color: #95a5a6;
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            font-family: 'Calibri', sans-serif;
            max-width: 400px;
            direction: rtl;
            text-align: right;
            animation: slideIn 0.3s ease-out;
        }
        
        .notification-success { 
            background: #d4edda; 
            color: #155724; 
            border: 1px solid #c3e6cb; 
        }
        
        .notification-warning { 
            background: #fff3cd; 
            color: #856404; 
            border: 1px solid #ffeaa7; 
        }
        
        .notification-error { 
            background: #f8d7da; 
            color: #721c24; 
            border: 1px solid #f5c6cb; 
        }
        
        .notification-info { 
            background: #d1ecf1; 
            color: #0c5460; 
            border: 1px solid #bee5eb; 
        }
        
        .notification-content { 
            display: flex; 
            align-items: center; 
            justify-content: space-between; 
        }
        
        .notification-message { 
            flex: 1; 
            margin-right: 10px; 
        }
        
        .notification-close {
            background: none; 
            border: none; 
            font-size: 18px; 
            cursor: pointer;
            color: inherit; 
            opacity: 0.7; 
            padding: 0; 
            width: 20px; 
            height: 20px;
        }
        
        .notification-close:hover { 
            opacity: 1; 
        }
        
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .search-container {
                flex-direction: column;
            }
            
            .search-input {
                min-width: 100%;
            }
            
            .table th,
            .table td {
                padding: 8px 5px;
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 نافذة بحث السجل العام واللوائح</h1>
            <p>نظام متطور للبحث في بيانات السجل العام واللوائح الدراسية</p>
        </div>
        
        <div class="search-section">
            <div class="search-container">
                <span class="search-label">البحث:</span>
                <input type="text" id="searchInput" class="search-input" 
                       placeholder="اكتب للبحث في السجل العام..." 
                       oninput="debouncedSearch()">
                <button class="btn btn-clear" onclick="clearSearch()">✕ مسح</button>
                <button class="btn btn-refresh" onclick="refreshData()">🔄 تحديث</button>
            </div>
        </div>
        
        <div class="results-info">
            <span id="resultsCount" class="results-count">النتائج: 0</span>
            <span class="results-count">السنة الدراسية الحالية: 2024/2025</span>
        </div>
        
        <div class="table-section">
            <div class="table-title">📋 السجل العام</div>
            <div class="table-container">
                <div class="table-wrapper">
                    <table class="table" id="generalTable">
                        <thead>
                            <tr>
                                <th style="width: 80px;">الرمز</th>
                                <th style="width: 200px;">الاسم والنسب</th>
                                <th style="width: 80px;">النوع</th>
                                <th style="width: 120px;">تاريخ الازدياد</th>
                                <th style="width: 150px;">مكان الازدياد</th>
                                <th style="width: 120px;">الهاتف الأول</th>
                                <th style="width: 120px;">الهاتف الثاني</th>
                            </tr>
                        </thead>
                        <tbody id="generalTableBody">
                            <tr>
                                <td colspan="7" class="no-data"> اكتب في حقل البحث لعرض النتائج</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="table-section">
            <div class="table-title">📚 اللوائح المرتبطة</div>
            <div class="table-container">
                <div class="table-wrapper">
                    <table class="table" id="listsTable">
                        <thead>
                            <tr>
                                <th style="width: 120px;">السنة الدراسية</th>
                                <th style="width: 200px;">المستوى</th>
                                <th style="width: 200px;">الاسم والنسب</th>
                                <th style="width: 120px;">القسم</th>
                                <th style="width: 80px;">رت</th>
                            </tr>
                        </thead>
                        <tbody id="listsTableBody">
                            <tr>
                                <td colspan="5" class="no-data">انقر على صف في السجل العام لعرض اللوائح المرتبطة</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        let bridge;
        let generalData = [];
        let listsData = [];
        let filteredData = [];
        let selectedCode = null;
        let selectedName = null;
        let searchTimeout = null; // للـ debouncing
        
        // دالة debouncing للبحث لمنع التشنج أثناء الكتابة
        function debouncedSearch() {
            clearTimeout(searchTimeout);
            
            // إذا كان الحقل فارغاً، نطبق فوراً بدون انتظار
            const searchText = document.getElementById('searchInput').value.trim();
            if (!searchText) {
                searchGeneral();
                return;
            }
            
            // تأخير للنصوص غير الفارغة
            searchTimeout = setTimeout(searchGeneral, 300); // تأخير 300ms
        }
        
        // إعداد القناة مع Python
        function setupBridge() {
            try {
                if (typeof QWebChannel !== 'undefined') {
                    if (typeof qt !== 'undefined' && qt.webChannelTransport) {
                        new QWebChannel(qt.webChannelTransport, function(channel) {
                            bridge = channel.objects.bridge;
                            console.log('تم إنشاء الجسر بنجاح');
                        });
                    } else {
                        console.log('qt.webChannelTransport غير متوفر، إعادة المحاولة...');
                        setTimeout(setupBridge, 200);
                    }
                } else {
                    console.log('QWebChannel غير متوفر، إعادة المحاولة...');
                    setTimeout(setupBridge, 200);
                }
            } catch (error) {
                console.log('خطأ في إعداد الجسر:', error);
                setTimeout(setupBridge, 200);
            }
        }
        
        // بدء إعداد الجسر عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            setupBridge();
        });
        
        // محاولة إضافية بعد تحميل النافذة
        window.addEventListener('load', function() {
            if (!bridge) {
                setTimeout(setupBridge, 300);
            }
            
            // تطبيق إعدادات التمرير مرة واحدة فقط
            setTimeout(reapplyScrollbarSettings, 800);
            
            // إعداد الجداول فارغة عند تحميل النافذة
            setTimeout(initializeEmptyTables, 100);
        });
        
        // تحميل جميع البيانات مرة واحدة - عند أول بحث أو عند الضغط على زر التحديث
        function initializeData(data) {
            generalData = data.general;
            listsData = data.lists;
            
            // إذا كان هناك نص في حقل البحث، نطبق البحث
            const searchText = document.getElementById('searchInput').value.toLowerCase().trim();
            if (searchText) {
                filteredData = generalData.filter(row => {
                    return row.some(cell => 
                        cell && cell.toString().toLowerCase().includes(searchText)
                    );
                });
                updateGeneralTable(filteredData);
            } else {
                // إذا لم يكن هناك نص بحث، نعرض رسالة البحث
                document.getElementById('generalTableBody').innerHTML = 
                    '<tr><td colspan="7" class="no-data">🔍 اكتب في حقل البحث لعرض النتائج</td></tr>';
                updateResultsCount(0);
            }
            
            // تفعيل حقل البحث وزر المسح حسب الحاجة
            const searchInput = document.getElementById('searchInput');
            const clearBtn = document.querySelector('.btn-clear');
            if (searchInput) {
                searchInput.disabled = false;
                searchInput.placeholder = 'اكتب للبحث في السجل العام...';
            }
            if (clearBtn) {
                clearBtn.disabled = !searchText; // تفعيل فقط إذا كان هناك نص
            }
            
            // تطبيق إعدادات التمرير مرة واحدة بعد التحميل
            setTimeout(reapplyScrollbarSettings, 300);
        }
        
        // عدم تحميل البيانات تلقائياً عند بدء الصفحة
        function initializeEmptyTables() {
            // إعداد الجداول فارغة
            document.getElementById('generalTableBody').innerHTML = 
                '<tr><td colspan="7" class="no-data"> اكتب في حقل البحث لعرض النتائج</td></tr>';
            document.getElementById('listsTableBody').innerHTML = 
                '<tr><td colspan="5" class="no-data">انقر على صف في السجل العام لعرض اللوائح المرتبطة</td></tr>';
            updateResultsCount(0);
            
            // تفعيل حقل البحث ولكن تعطيل زر المسح حتى يتم إدخال نص
            const searchInput = document.getElementById('searchInput');
            const clearBtn = document.querySelector('.btn-clear');
            if (searchInput) {
                searchInput.disabled = false;
                searchInput.placeholder = 'اكتب للبحث في السجل العام...';
            }
            if (clearBtn) {
                clearBtn.disabled = true; // معطل حتى يتم البحث
            }
        }
        
        function updateGeneralTable(data) {
            const tableBody = document.getElementById('generalTableBody');
            
            if (!data || data.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="7" class="no-data">📭 لا توجد بيانات للعرض</td></tr>';
                updateResultsCount(0);
                return;
            }
            
            // استخدام DocumentFragment لتحسين الأداء
            const fragment = document.createDocumentFragment();
            
            data.forEach((row, index) => {
                const tr = document.createElement('tr');
                tr.onclick = function() { selectGeneralRecord(row[0], row[1], this); };
                
                tr.innerHTML = `
                    <td class="code-cell" onclick="event.stopPropagation(); copyCode('${row[0]}')">${row[0] || ''}</td>
                    <td style="text-align: right;">${row[1] || ''}</td>
                    <td>${row[2] || ''}</td>
                    <td>${row[3] || ''}</td>
                    <td style="text-align: right;">${row[4] || ''}</td>
                    <td>${row[5] || ''}</td>
                    <td>${row[6] || ''}</td>
                `;
                
                fragment.appendChild(tr);
            });
            
            // مسح المحتوى السابق وإضافة الجديد
            tableBody.innerHTML = '';
            tableBody.appendChild(fragment);
            updateResultsCount(data.length);
        }
        
        function updateListsTable(data) {
            const tableBody = document.getElementById('listsTableBody');
            
            if (!data || data.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="5" class="no-data">📭 لا توجد لوائح مرتبطة بهذا الرمز</td></tr>';
                return;
            }
            
            // استخدام DocumentFragment لتحسين الأداء
            const fragment = document.createDocumentFragment();
            
            data.forEach((row, index) => {
                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>${row[0] || ''}</td>
                    <td style="text-align: right;">${row[1] || ''}</td>
                    <td style="text-align: right;">${selectedName || ''}</td>
                    <td style="text-align: right;">${row[2] || ''}</td>
                    <td>${row[3] || ''}</td>
                `;
                fragment.appendChild(tr);
            });
            
            // مسح المحتوى السابق وإضافة الجديد
            tableBody.innerHTML = '';
            tableBody.appendChild(fragment);
        }
        
        function selectGeneralRecord(code, name, rowElement) {
            // منع التشنج بالتحقق من التحديد المسبق
            if (selectedCode === code && rowElement.classList.contains('selected')) {
                return; // الصف محدد مسبقاً، لا حاجة للمعالجة
            }
            
            // إزالة التحديد السابق بطريقة محسنة
            const previousSelected = document.querySelector('#generalTableBody tr.selected');
            if (previousSelected) {
                previousSelected.classList.remove('selected');
            }
            
            // إضافة التحديد الجديد
            rowElement.classList.add('selected');
            
            selectedCode = code;
            selectedName = name;
            
            // البحث المحلي في بيانات اللوائح مع تحسين الأداء
            const relatedLists = listsData.filter(row => row[0] === code);
            const formattedLists = relatedLists.map(row => [
                row[1], // السنة الدراسية
                row[2], // المستوى  
                row[3], // القسم
                row[4]  // رت
            ]);
            
            // تحديث جدول اللوائح مع تأخير قصير لمنع التشنج
            requestAnimationFrame(() => {
                updateListsTable(formattedLists);
            });
        }
        
        function copyCode(code) {
            // منع التشنج عند النسخ
            try {
                if (bridge && typeof bridge.copyCodeToClipboard === 'function') {
                    bridge.copyCodeToClipboard(code);
                } else {
                    // Fallback للنسخ المباشر
                    if (navigator.clipboard && navigator.clipboard.writeText) {
                        navigator.clipboard.writeText(code).then(() => {
                            showNotification({message: `تم نسخ الرمز: ${code}`, type: 'success'});
                        }).catch(() => {
                            console.log('فشل في نسخ الرمز:', code);
                        });
                    } else {
                        // Fallback أقدم للمتصفحات القديمة
                        const textArea = document.createElement('textarea');
                        textArea.value = code;
                        document.body.appendChild(textArea);
                        textArea.select();
                        document.execCommand('copy');
                        document.body.removeChild(textArea);
                        showNotification({message: `تم نسخ الرمز: ${code}`, type: 'success'});
                    }
                }
            } catch (error) {
                console.error('خطأ في نسخ الرمز:', error);
                showNotification({message: 'حدث خطأ أثناء النسخ', type: 'error'});
            }
        }
        
        function searchGeneral() {
            const searchText = document.getElementById('searchInput').value.toLowerCase().trim();
            
            // إذا كانت البيانات غير محملة، نحملها أولاً
            if (!generalData || generalData.length === 0) {
                // تحميل البيانات من قاعدة البيانات
                loadDataForSearch();
                return;
            }
            
            // تفعيل زر المسح عند وجود نص في حقل البحث
            const clearBtn = document.querySelector('.btn-clear');
            if (clearBtn) {
                clearBtn.disabled = searchText.length === 0;
            }
            
            // استخدام requestAnimationFrame لمنع التشنج أثناء البحث
            requestAnimationFrame(() => {
                if (!searchText) {
                    // إذا كان حقل البحث فارغاً، نظهر رسالة البحث
                    document.getElementById('generalTableBody').innerHTML = 
                        '<tr><td colspan="7" class="no-data">🔍 اكتب في حقل البحث لعرض النتائج</td></tr>';
                    updateResultsCount(0);
                } else {
                    filteredData = generalData.filter(row => {
                        return row.some(cell => 
                            cell && cell.toString().toLowerCase().includes(searchText)
                        );
                    });
                    updateGeneralTable(filteredData);
                }
                
                // مسح جدول اللوائح عند تغيير البحث
                document.getElementById('listsTableBody').innerHTML = 
                    '<tr><td colspan="5" class="no-data">انقر على صف في السجل العام لعرض اللوائح المرتبطة</td></tr>';
                
                // إعادة تعيين المتغيرات
                selectedCode = null;
                selectedName = null;
            });
        }
        
        // دالة لتحميل البيانات عند أول بحث
        function loadDataForSearch() {
            // عرض رسالة تحميل
            document.getElementById('generalTableBody').innerHTML = 
                '<tr><td colspan="7" class="loading">جاري تحميل البيانات للبحث...</td></tr>';
            
            if (bridge && typeof bridge.refreshData === 'function') {
                bridge.refreshData();
            } else {
                console.log('الجسر غير متاح أو الدالة غير موجودة');
                showNotification({message: 'الجسر غير متاح، الرجاء المحاولة مرة أخرى', type: 'error'});
                
                // إعادة رسالة البحث في حالة الخطأ
                document.getElementById('generalTableBody').innerHTML = 
                    '<tr><td colspan="7" class="no-data">🔍 اكتب في حقل البحث لعرض النتائج</td></tr>';
            }
        }
        
        function clearSearch() {
            // مسح timeout البحث إذا كان موجوداً
            clearTimeout(searchTimeout);
            
            document.getElementById('searchInput').value = '';
            
            // تعطيل زر المسح
            const clearBtn = document.querySelector('.btn-clear');
            if (clearBtn) {
                clearBtn.disabled = true;
            }
            
            // استخدام requestAnimationFrame لمنع التشنج
            requestAnimationFrame(() => {
                // عرض رسالة البحث بدلاً من جميع البيانات
                document.getElementById('generalTableBody').innerHTML = 
                    '<tr><td colspan="7" class="no-data">🔍 اكتب في حقل البحث لعرض النتائج</td></tr>';
                updateResultsCount(0);
                
                // مسح جدول اللوائح وإعادة تعيين المتغيرات
                document.getElementById('listsTableBody').innerHTML = 
                    '<tr><td colspan="5" class="no-data">انقر على صف في السجل العام لعرض اللوائح المرتبطة</td></tr>';
                selectedCode = null;
                selectedName = null;
            });
        }
        
        function refreshData() {
            // منع النقر المتكرر على زر التحديث
            const refreshBtn = document.querySelector('.btn-refresh');
            if (refreshBtn.disabled) {
                return;
            }
            
            refreshBtn.disabled = true;
            refreshBtn.textContent = '⏳ جاري التحديث...';
            
            // حفظ نص البحث الحالي
            const searchInput = document.getElementById('searchInput');
            const currentSearchText = searchInput ? searchInput.value : '';
            
            // تعطيل حقل البحث وزر المسح أثناء التحديث
            const clearBtn = document.querySelector('.btn-clear');
            if (searchInput) {
                searchInput.disabled = true;
                searchInput.placeholder = 'جاري تحديث البيانات...';
            }
            if (clearBtn) {
                clearBtn.disabled = true;
            }
            
            // مسح البيانات المحلية وعرض رسالة التحميل
            generalData = [];
            listsData = [];
            filteredData = [];
            selectedCode = null;
            selectedName = null;
            
            document.getElementById('generalTableBody').innerHTML = 
                '<tr><td colspan="7" class="loading">جاري تحديث البيانات...</td></tr>';
            document.getElementById('listsTableBody').innerHTML = 
                '<tr><td colspan="5" class="no-data">انقر على صف في السجل العام لعرض اللوائح المرتبطة</td></tr>';
            updateResultsCount(0);
            
            if (bridge && typeof bridge.refreshData === 'function') {
                bridge.refreshData();
                
                // إعادة تفعيل الزر بعد 2 ثانية
                setTimeout(() => {
                    refreshBtn.disabled = false;
                    refreshBtn.textContent = '🔄 تحديث';
                    
                    // إعادة تفعيل حقل البحث واستعادة النص السابق
                    if (searchInput) {
                        searchInput.disabled = false;
                        searchInput.value = currentSearchText;
                        searchInput.placeholder = 'اكتب للبحث في السجل العام...';
                    }
                }, 2000);
            } else {
                console.log('الجسر غير متاح أو الدالة غير موجودة');
                showNotification({message: 'الجسر غير متاح، الرجاء المحاولة مرة أخرى', type: 'error'});
                
                // إعادة تفعيل الزر فوراً في حالة الخطأ
                refreshBtn.disabled = false;
                refreshBtn.textContent = '🔄 تحديث';
                
                // إعادة الحالة السابقة في حالة الخطأ
                if (searchInput) {
                    searchInput.disabled = false;
                    searchInput.value = currentSearchText;
                    searchInput.placeholder = 'اكتب للبحث في السجل العام...';
                }
                if (clearBtn) {
                    clearBtn.disabled = !currentSearchText;
                }
                
                // عرض رسالة البحث
                document.getElementById('generalTableBody').innerHTML = 
                    '<tr><td colspan="7" class="no-data">🔍 اكتب في حقل البحث لعرض النتائج</td></tr>';
            }
        }
        
        function updateResultsCount(count) {
            document.getElementById('resultsCount').textContent = `النتائج: ${count}`;
        }
        
        // دالة لإعادة تطبيق إعدادات التمرير - محسنة
        function reapplyScrollbarSettings() {
            try {
                const tableWrappers = document.querySelectorAll('.table-wrapper');
                tableWrappers.forEach(function(wrapper) {
                    // تطبيق الإعدادات بطريقة محسنة
                    wrapper.style.cssText += 'overflow-y: scroll; overflow-x: auto; scrollbar-width: auto; -ms-overflow-style: auto;';
                });
                console.log('تم تطبيق إعدادات التمرير');
            } catch (error) {
                console.error('خطأ في تطبيق إعدادات التمرير:', error);
            }
        }
        
        // دالة لعرض الإشعارات
        function showNotification(notificationData) {
            try {
                const notification = document.createElement('div');
                notification.className = `notification notification-${notificationData.type || 'info'}`;
                notification.innerHTML = `
                    <div class="notification-content">
                        <span class="notification-message">${notificationData.message}</span>
                        <button class="notification-close" onclick="this.parentElement.parentElement.remove()">×</button>
                    </div>
                `;
                
                document.body.appendChild(notification);
                
                setTimeout(() => {
                    if (notification.parentElement) {
                        notification.remove();
                    }
                }, 5000);
                
            } catch (error) {
                console.error('خطأ في عرض الإشعار:', error);
                console.log(`إشعار [${notificationData.type}]: ${notificationData.message}`);
            }
        }
    </script>
</body>
</html>
        '''
        
    def load_general_data(self):
        """تحميل جميع البيانات من قاعدة البيانات مرة واحدة"""
        try:
            # تحميل بيانات السجل العام
            general_query_str = """
                SELECT الرمز, الاسم_والنسب, النوع, تاريخ_الازدياد, 
                       مكان_الازدياد, الهاتف_الأول, الهاتف_الثاني
                FROM السجل_العام
                ORDER BY الرمز
            """
            
            # تحميل جميع بيانات اللوائح
            lists_query_str = """
                SELECT الرمز, السنة_الدراسية, المستوى, القسم, رت
                FROM اللوائح
                ORDER BY الرمز, السنة_الدراسية DESC
            """

            general_records = []
            lists_records = []

            if self.using_qsql:
                # تحميل السجل العام
                query = QSqlQuery(self.db)
                if query.exec_(general_query_str):
                    while query.next():
                        record = [str(query.value(i)) if query.value(i) is not None else '' for i in range(7)]
                        general_records.append(record)
                
                # تحميل اللوائح
                if query.exec_(lists_query_str):
                    while query.next():
                        record = [str(query.value(i)) if query.value(i) is not None else '' for i in range(5)]
                        lists_records.append(record)
            else:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                # تحميل السجل العام
                cursor.execute(general_query_str)
                general_records = [[str(value) if value is not None else '' for value in record] for record in cursor.fetchall()]
                
                # تحميل اللوائح
                cursor.execute(lists_query_str)
                lists_records = [[str(value) if value is not None else '' for value in record] for record in cursor.fetchall()]
                
                conn.close()

            # إرسال جميع البيانات إلى JavaScript مرة واحدة
            data_json = json.dumps({
                'general': general_records,
                'lists': lists_records
            }, ensure_ascii=False)
            
            self.web_view.page().runJavaScript(f"initializeData({data_json});")
            
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
            self.bridge.showMessage(f"حدث خطأ أثناء تحميل البيانات: {str(e)}", "error")
    
    def search_general_data(self, search_text):
        """البحث في بيانات السجل العام"""
        # يتم التعامل مع البحث في JavaScript مباشرة للسرعة
        pass
    
    def handle_legacy_lists_request(self, code, name):
        """للتوافق مع النظام القديم - لكن البيانات معالجة محلياً الآن"""
        # لا نحتاج فعل شيء لأن البيانات موجودة محلياً في JavaScript
        pass
    
    def load_lists_data(self, code, name):
        """تحميل بيانات اللوائح للرمز المحدد - لم تعد مستخدمة"""
        # هذه الدالة لم تعد مستخدمة لأن البيانات محملة محلياً
        pass

    def ensure_maximized(self):
        """التأكد من عرض النافذة في كامل الشاشة"""
        self.showMaximized()
        from PyQt5.QtWidgets import QApplication
        QApplication.processEvents()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # تطبيق ثيم
    app.setStyle('Fusion')
    
    # إنشاء اتصال بقاعدة البيانات للاختبار
    db_path = get_database_path()
    from PyQt5.QtSql import QSqlDatabase
    db = QSqlDatabase.addDatabase("QSQLITE")
    db.setDatabaseName(db_path)
    
    if db.open():
        window = GeneralRecordHtmlWindow(db=db, academic_year="2024/2025")
    else:
        window = GeneralRecordHtmlWindow()
    
    window.show()
    sys.exit(app.exec_())
