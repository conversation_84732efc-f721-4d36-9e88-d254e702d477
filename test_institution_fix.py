#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف اختبار للتأكد من إزالة تكرار اسم المؤسسة في التقارير النصف الأسبوعية (10 حصص)
"""

import datetime
import os
from split_attendance_report_10 import SplitAttendanceReport10
from split_attendance_report_second_half_10 import SplitAttendanceReportSecondHalf10

def create_test_students(count):
    """إنشاء قائمة طلاب للاختبار"""
    students = []
    for i in range(1, count + 1):
        students.append({
            "id": i,
            "name": f"الطالب رقم {i}",
            "rt": str(i)
        })
    return students

def test_institution_fix():
    """اختبار إزالة تكرار اسم المؤسسة"""
    
    # بيانات المؤسسة للاختبار
    institution_data = {
        "academy": "الأكاديمية الجهوية للتربية والتكوين لجهة الدار البيضاء سطات",
        "directorate": "المديرية الإقليمية للتربية الوطنية بالنواصر",
        "institution": "الثانوية التأهيلية ابن خلدون",
        "academic_year": "2024/2025"
    }
    
    start_date = datetime.datetime.now()
    periods_10 = ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10"]
    
    # إنشاء قائمة طلاب للاختبار
    students = create_test_students(30)
    
    # إنشاء مجلد للاختبار
    test_dir = "test_institution_fix"
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)
    
    print("اختبار إزالة تكرار اسم المؤسسة في التقارير النصف الأسبوعية (10 حصص):")
    print("=" * 70)
    print("الآن اسم المؤسسة يظهر مرة واحدة فقط في مربع المؤسسة")
    print("=" * 70)
    
    # 1. اختبار تقرير الاثنين-الأربعاء (10 حصص)
    print("1. اختبار تقرير الاثنين-الأربعاء (10 حصص)...")
    report_10_generator = SplitAttendanceReport10()
    
    file_path = os.path.join(test_dir, "test_monday_wednesday_10_fixed.pdf")
    success = report_10_generator.generate_pdf(
        file_path,
        students,
        "1/1",
        institution_data,
        start_date,
        1,
        periods_10,
        ["الأربعاء", "الثلاثاء", "الاثنين"]
    )
    
    if success:
        print(f"   ✓ تم إنشاء التقرير بنجاح: {file_path}")
        print("   ✓ اسم المؤسسة يظهر مرة واحدة فقط في مربع المؤسسة")
    else:
        print("   ✗ فشل في إنشاء التقرير")
    
    # 2. اختبار تقرير الخميس-السبت (10 حصص)
    print("2. اختبار تقرير الخميس-السبت (10 حصص)...")
    thursday_10_generator = SplitAttendanceReportSecondHalf10()
    
    file_path = os.path.join(test_dir, "test_thursday_saturday_10_fixed.pdf")
    success = thursday_10_generator.generate_pdf(
        file_path,
        students,
        "2/1",
        institution_data,
        start_date,
        1,
        periods_10,
        ["السبت", "الجمعة", "الخميس"]
    )
    
    if success:
        print(f"   ✓ تم إنشاء التقرير بنجاح: {file_path}")
        print("   ✓ اسم المؤسسة يظهر مرة واحدة فقط في مربع المؤسسة")
    else:
        print("   ✗ فشل في إنشاء التقرير")
    
    print("\nتم الانتهاء من اختبار إزالة تكرار اسم المؤسسة!")
    print(f"يمكنك العثور على ملفات الاختبار في مجلد: {test_dir}")
    print("\nملاحظات:")
    print("- تم إزالة اسم المؤسسة من الرأس (العمود الأوسط)")
    print("- اسم المؤسسة يظهر الآن فقط في مربع المؤسسة")
    print("- الشعار لا يزال يظهر في العمود الأوسط")

if __name__ == "__main__":
    test_institution_fix()
