#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف اختبار للتخطيط الجديد للتقارير النصف الأسبوعية
اسم المؤسسة وعنوان القسم في العمود الأيسر مقابل السنة الدراسية
"""

import datetime
import os
from split_attendance_report import SplitAttendanceReport
from split_attendance_report_10 import SplitAttendanceReport10
from split_attendance_report_second_half_10 import SplitAttendanceReportSecondHalf10

def create_test_students(count):
    """إنشاء قائمة طلاب للاختبار"""
    students = []
    for i in range(1, count + 1):
        students.append({
            "id": i,
            "name": f"الطالب رقم {i}",
            "rt": str(i)
        })
    return students

def test_new_layout():
    """اختبار التخطيط الجديد للتقارير النصف الأسبوعية"""
    
    # بيانات المؤسسة للاختبار
    institution_data = {
        "academy": "الأكاديمية الجهوية للتربية والتكوين لجهة الدار البيضاء سطات",
        "directorate": "المديرية الإقليمية للتربية الوطنية بالنواصر",
        "institution": "الثانوية التأهيلية ابن خلدون",
        "academic_year": "2024/2025"
    }
    
    start_date = datetime.datetime.now()
    periods_8 = ["1", "2", "3", "4", "5", "6", "7", "8"]
    periods_10 = ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10"]
    
    # إنشاء قائمة طلاب للاختبار
    students = create_test_students(35)  # 35 طالب لاختبار ارتفاع 13 نقطة
    
    # إنشاء مجلد للاختبار
    test_dir = "test_new_layout"
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)
    
    print("اختبار التخطيط الجديد للتقارير النصف الأسبوعية:")
    print("=" * 60)
    print("الميزات الجديدة:")
    print("- اسم المؤسسة وعنوان القسم في العمود الأيسر")
    print("- السنة الدراسية في العمود الأيمن")
    print("- الشعار في العمود الأوسط")
    print("=" * 60)
    
    # 1. اختبار تقرير الاثنين-الأربعاء (8 حصص)
    print("1. اختبار تقرير الاثنين-الأربعاء (8 حصص)...")
    report_generator = SplitAttendanceReport()
    
    file_path = os.path.join(test_dir, "test_monday_wednesday_8.pdf")
    success = report_generator.generate_first_half(
        file_path,
        students,
        "1/1",
        institution_data,
        start_date,
        1,
        periods_8
    )
    
    if success:
        print(f"   ✓ تم إنشاء التقرير بنجاح: {file_path}")
    else:
        print("   ✗ فشل في إنشاء التقرير")
    
    # 2. اختبار تقرير الاثنين-الأربعاء (10 حصص)
    print("2. اختبار تقرير الاثنين-الأربعاء (10 حصص)...")
    report_10_generator = SplitAttendanceReport10()
    
    file_path = os.path.join(test_dir, "test_monday_wednesday_10.pdf")
    success = report_10_generator.generate_pdf(
        file_path,
        students,
        "2/1",
        institution_data,
        start_date,
        1,
        periods_10,
        ["الأربعاء", "الثلاثاء", "الاثنين"]
    )
    
    if success:
        print(f"   ✓ تم إنشاء التقرير بنجاح: {file_path}")
    else:
        print("   ✗ فشل في إنشاء التقرير")
    
    # 3. اختبار تقرير الخميس-السبت (8 حصص)
    print("3. اختبار تقرير الخميس-السبت (8 حصص)...")
    
    file_path = os.path.join(test_dir, "test_thursday_saturday_8.pdf")
    success = report_generator.generate_second_half(
        file_path,
        students,
        "3/1",
        institution_data,
        start_date,
        1,
        periods_8
    )
    
    if success:
        print(f"   ✓ تم إنشاء التقرير بنجاح: {file_path}")
    else:
        print("   ✗ فشل في إنشاء التقرير")
    
    # 4. اختبار تقرير الخميس-السبت (10 حصص)
    print("4. اختبار تقرير الخميس-السبت (10 حصص)...")
    thursday_10_generator = SplitAttendanceReportSecondHalf10()
    
    file_path = os.path.join(test_dir, "test_thursday_saturday_10.pdf")
    success = thursday_10_generator.generate_pdf(
        file_path,
        students,
        "4/1",
        institution_data,
        start_date,
        1,
        periods_10,
        ["السبت", "الجمعة", "الخميس"]
    )
    
    if success:
        print(f"   ✓ تم إنشاء التقرير بنجاح: {file_path}")
    else:
        print("   ✗ فشل في إنشاء التقرير")
    
    print("\nتم الانتهاء من اختبار التخطيط الجديد!")
    print(f"يمكنك العثور على ملفات الاختبار في مجلد: {test_dir}")
    print("\nملاحظات:")
    print("- تم استخدام 35 طالب لاختبار ارتفاع الصف 13 نقطة")
    print("- اسم المؤسسة وعنوان القسم الآن في العمود الأيسر")
    print("- السنة الدراسية في العمود الأيمن")

if __name__ == "__main__":
    test_new_layout()
