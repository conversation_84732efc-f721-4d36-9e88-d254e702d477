﻿@echo off
title تنظيف Visual Studio Code وتحرير الذاكرة
echo ===============================
echo    تنظيف VS Code + تحرير RAM
echo ===============================

REM إغلاق Visual Studio Code
echo.
echo ➤ إغلاق Visual Studio Code ...
taskkill /IM Code.exe /F >nul 2>&1

REM تحديد مجلد VS Code
set "CODE_DIR=%APPDATA%\Code"
set "USER_DIR=%APPDATA%\Code\User"

REM حذف مجلدات الكاش والتخزين المؤقت
echo.
echo ➤ حذف مجلدات التخزين المؤقت ...
for %%F in (
    Cache
    CachedData
    CachedExtensionVSIXs
    Code Cache
    Crashpad
    GPUCache
    DawnGraphiteCache
    DawnWebGPUCache
    logs
    Service Worker
    WebStorage
    VideoDecodeStats
) do (
    if exist "%CODE_DIR%\%%F" (
        echo     حذف: %%F
        rmdir /S /Q "%CODE_DIR%\%%F"
    )
)

REM حذف سجل Timeline للمشاريع
if exist "%USER_DIR%\workspaceStorage" (
    echo     حذف: workspaceStorage
    rmdir /S /Q "%USER_DIR%\workspaceStorage"
)

REM تحرير الذاكرة
echo.
echo ➤ تحرير الذاكرة (RAM) ...
%windir%\system32\rundll32.exe advapi32.dll,ProcessIdleTasks

echo.
echo ✅ تم تنظيف Visual Studio Code وتحرير الذاكرة بنجاح.
pause
