"""
نافذة حوار إنشاء اختصار سطح المكتب
"""

import os
import sys
import json
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                           QPushButton, QFileDialog, QCheckBox, QGroupBox,
                           QLineEdit, QMessageBox, QTextEdit, QFrame)
from PyQt5.QtCore import Qt, QSettings
from PyQt5.QtGui import QFont, QPixmap, QIcon

class ShortcutDialog(QDialog):
    """نافذة حوار لسؤال المستخدم عن إنشاء اختصار على سطح المكتب"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.selected_icon_path = None
        self.exe_path = sys.executable if getattr(sys, 'frozen', False) else None
        self.settings_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'shortcut_settings.json')
        
        self.setup_ui()
        self.load_settings()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إنشاء اختصار سطح المكتب")
        self.setFixedSize(500, 400)
        self.setModal(True)
        
        # تعيين الخط
        font = QFont("Arial", 10)
        self.setFont(font)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان النافذة
        title_label = QLabel("إنشاء اختصار البرنامج")
        title_font = QFont("Arial", 14, QFont.Bold)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        main_layout.addWidget(title_label)
        
        # خط فاصل
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        main_layout.addWidget(line)
        
        # نص التوضيح
        description = QLabel(
            "هل تريد إنشاء اختصار للبرنامج على سطح المكتب؟\n"
            "يمكنك أيضاً اختيار أيقونة مخصصة للاختصار."
        )
        description.setWordWrap(True)
        description.setAlignment(Qt.AlignCenter)
        description.setStyleSheet("color: #34495e; margin: 10px 0;")
        main_layout.addWidget(description)
        
        # مجموعة اختيار الأيقونة
        icon_group = QGroupBox("اختيار الأيقونة")
        icon_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                margin-top: 10px;
                padding: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        icon_layout = QVBoxLayout()
        
        # مسار الأيقونة
        icon_path_layout = QHBoxLayout()
        self.icon_path_edit = QLineEdit()
        self.icon_path_edit.setPlaceholderText("اختر ملف الأيقونة (.ico)")
        self.icon_path_edit.setReadOnly(True)
        
        browse_button = QPushButton("استعراض...")
        browse_button.clicked.connect(self.browse_icon)
        browse_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        
        icon_path_layout.addWidget(QLabel("مسار الأيقونة:"))
        icon_path_layout.addWidget(self.icon_path_edit)
        icon_path_layout.addWidget(browse_button)
        
        # زر الأيقونة الافتراضية
        default_icon_button = QPushButton("استخدام الأيقونة الافتراضية (01.ico)")
        default_icon_button.clicked.connect(self.use_default_icon)
        default_icon_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #219a52;
            }
        """)
        
        icon_layout.addLayout(icon_path_layout)
        icon_layout.addWidget(default_icon_button)
        icon_group.setLayout(icon_layout)
        main_layout.addWidget(icon_group)
        
        # خيار عدم إظهار هذه النافذة مرة أخرى
        self.dont_ask_checkbox = QCheckBox("لا تسأل مرة أخرى (تذكر اختياري)")
        self.dont_ask_checkbox.setStyleSheet("margin: 10px 0;")
        main_layout.addWidget(self.dont_ask_checkbox)
        
        # منطقة الأزرار
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)
        
        # زر إنشاء الاختصار
        create_button = QPushButton("إنشاء الاختصار")
        create_button.clicked.connect(self.accept_with_shortcut)
        create_button.setStyleSheet("""
            QPushButton {
                background-color: #2ecc71;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #27ae60;
            }
        """)
        
        # زر عدم الإنشاء
        skip_button = QPushButton("لا، شكراً")
        skip_button.clicked.connect(self.reject_without_shortcut)
        skip_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        
        # زر الإلغاء
        cancel_button = QPushButton("إلغاء")
        cancel_button.clicked.connect(self.reject)
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        buttons_layout.addWidget(create_button)
        buttons_layout.addWidget(skip_button)
        buttons_layout.addWidget(cancel_button)
        
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
        
    def browse_icon(self):
        """استعراض ملف أيقونة"""
        file_dialog = QFileDialog()
        file_dialog.setWindowTitle("اختر ملف الأيقونة")
        file_dialog.setNameFilter("Icon Files (*.ico);;All Files (*)")
        file_dialog.setAcceptMode(QFileDialog.AcceptOpen)
        file_dialog.setFileMode(QFileDialog.ExistingFile)
        
        if file_dialog.exec_():
            selected_files = file_dialog.selectedFiles()
            if selected_files:
                self.selected_icon_path = selected_files[0]
                self.icon_path_edit.setText(self.selected_icon_path)
                
    def use_default_icon(self):
        """استخدام الأيقونة الافتراضية 01.ico"""
        if self.exe_path:
            default_icon = os.path.join(os.path.dirname(self.exe_path), "01.ico")
            if os.path.exists(default_icon):
                self.selected_icon_path = default_icon
                self.icon_path_edit.setText(default_icon)
                QMessageBox.information(self, "تم", "تم اختيار الأيقونة الافتراضية 01.ico")
            else:
                QMessageBox.warning(self, "تحذير", "الأيقونة الافتراضية 01.ico غير موجودة في مجلد البرنامج")
        else:
            QMessageBox.warning(self, "تحذير", "البرنامج غير محزم، لا يمكن العثور على الأيقونة الافتراضية")
            
    def accept_with_shortcut(self):
        """قبول مع إنشاء اختصار"""
        self.result_data = {
            'create_shortcut': True,
            'icon_path': self.selected_icon_path,
            'dont_ask_again': self.dont_ask_checkbox.isChecked()
        }
        self.save_settings()
        self.accept()
        
    def reject_without_shortcut(self):
        """رفض بدون إنشاء اختصار"""
        self.result_data = {
            'create_shortcut': False,
            'icon_path': None,
            'dont_ask_again': self.dont_ask_checkbox.isChecked()
        }
        self.save_settings()
        self.accept()
        
    def save_settings(self):
        """حفظ الإعدادات"""
        if hasattr(self, 'result_data') and self.result_data['dont_ask_again']:
            try:
                with open(self.settings_file, 'w', encoding='utf-8') as f:
                    json.dump(self.result_data, f, ensure_ascii=False, indent=2)
            except Exception as e:
                print(f"خطأ في حفظ الإعدادات: {e}")
                
    def load_settings(self):
        """تحميل الإعدادات المحفوظة"""
        try:
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    return settings
        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {e}")
        return None
        
    def get_result(self):
        """الحصول على نتيجة الحوار"""
        return getattr(self, 'result_data', None)

def show_shortcut_dialog(parent=None):
    """عرض نافذة حوار الاختصار"""
    dialog = ShortcutDialog(parent)
    
    # التحقق من الإعدادات المحفوظة
    saved_settings = dialog.load_settings()
    if saved_settings and saved_settings.get('dont_ask_again', False):
        # المستخدم اختار عدم السؤال مرة أخرى، استخدام الاختيار المحفوظ
        return saved_settings
    
    # عرض النافذة
    if dialog.exec_() == QDialog.Accepted:
        return dialog.get_result()
    else:
        return None