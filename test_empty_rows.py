#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف اختبار لتجربة إضافة الصفوف الفارغة في تقارير الغياب
"""

import datetime
import os
from attendance_report import AttendanceReport
from split_attendance_report import SplitAttendanceReport

def test_empty_rows():
    """اختبار إضافة الصفوف الفارغة"""
    
    # بيانات وهمية للاختبار
    students = [
        {"id": 1, "name": "أحمد محمد", "rt": "1"},
        {"id": 2, "name": "فاطمة علي", "rt": "2"},
        {"id": 3, "name": "محمد حسن", "rt": "3"},
        {"id": 4, "name": "عائشة أحمد", "rt": "4"},
        {"id": 5, "name": "يوسف إبراهيم", "rt": "5"},
    ]
    
    class_name = "1/1"
    institution_data = {
        "academy": "الأكاديمية الجهوية للتربية والتكوين",
        "directorate": "المديرية الإقليمية",
        "institution": "الثانوية التأهيلية",
        "academic_year": "2024/2025"
    }
    
    start_date = datetime.datetime.now()
    periods = ["1", "2", "3", "4", "5", "6", "7", "8"]
    
    # اختبار تقرير الغياب الأسبوعي
    print("اختبار تقرير الغياب الأسبوعي...")
    report_generator = AttendanceReport()
    
    # إنشاء مجلد للاختبار
    test_dir = "test_reports"
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)
    
    # اختبار التقرير الأسبوعي
    weekly_file = os.path.join(test_dir, "test_weekly_report.pdf")
    success = report_generator.generate_pdf(
        weekly_file,
        students,
        class_name,
        institution_data,
        start_date,
        1,  # نوع التقرير
        periods
    )
    
    if success:
        print(f"✓ تم إنشاء التقرير الأسبوعي بنجاح: {weekly_file}")
    else:
        print("✗ فشل في إنشاء التقرير الأسبوعي")
    
    # اختبار تقرير النصف الأسبوعي
    print("اختبار تقرير النصف الأسبوعي...")
    split_report_generator = SplitAttendanceReport()
    
    split_file = os.path.join(test_dir, "test_split_report.pdf")
    success = split_report_generator.generate_first_half(
        split_file,
        students,
        class_name,
        institution_data,
        start_date,
        1,  # نوع التقرير
        periods
    )
    
    if success:
        print(f"✓ تم إنشاء التقرير النصف أسبوعي بنجاح: {split_file}")
    else:
        print("✗ فشل في إنشاء التقرير النصف أسبوعي")
    
    print("\nتم الانتهاء من الاختبار!")
    print(f"يمكنك العثور على ملفات الاختبار في مجلد: {test_dir}")

if __name__ == "__main__":
    test_empty_rows()
