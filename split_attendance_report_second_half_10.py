import os
import datetime
from reportlab.lib import colors as reportlab_colors
from reportlab.lib.pagesizes import A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, PageBreak
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.lib.units import cm

import arabic_reshaper
from bidi.algorithm import get_display

class SplitAttendanceReportSecondHalf10:
    def __init__(self):
        self.arabic_font = self._load_arabic_fonts()

    def _load_arabic_fonts(self):
        arabic_fonts = [
            ("Calibri", "c:/windows/fonts/calibri.ttf"),
            ("ARIALUNI", "c:/windows/fonts/ARIALUNI.TTF"),
            ("Arial", "c:/windows/fonts/arial.ttf"),
            ("TraditionalArabic", "c:/windows/fonts/trado.ttf"),
            ("SimplifiedArabic", "c:/windows/fonts/simpo.ttf"),
            ("ArabicTypesetting", "c:/windows/fonts/arabtype.ttf"),
            ("Tahoma", "c:/windows/fonts/tahoma.ttf")
        ]
        for font_name, font_path in arabic_fonts:
            if os.path.exists(font_path):
                pdfmetrics.registerFont(TTFont(font_name, font_path))
                return font_name
        return 'Helvetica'

    def arabic_text(self, text):
        reshaped_text = arabic_reshaper.reshape(text)
        bidi_text = get_display(reshaped_text)
        return bidi_text

    def _is_student_hidden_from_attendance(self, rt_value):
        """
        التحقق الذكي من إخفاء التلميذ من لائحة الغياب
        يتحقق من وجود علامة X في عمود رت
        """
        if not rt_value:
            return False

        rt_value = str(rt_value).strip()

        # التلميذ مخفي إذا كان عمود رت يحتوي على "X" (إما X فقط أو رقم + مسافة + X)
        return rt_value == "X" or rt_value.endswith(" X")

    def generate_pdf(self, file_path, students, class_name, institution_data, start_date, report_type, periods=None, days=None):
        # فلترة التلاميذ - إزالة التلاميذ الذين لديهم علامة X في عمود رت
        filtered_students = []
        hidden_count = 0

        print(f"=== تشخيص فلترة التلاميذ في {class_name} (split_attendance_report_second_half_10.py) ===")
        print(f"عدد التلاميذ قبل الفلترة: {len(students)}")

        for i, student in enumerate(students):
            # التحقق من وجود علامة X في عمود رت
            rt_value = student.get('rt', '') or student.get('id', '') or ''
            rt_value = str(rt_value).strip()

            # طباعة تشخيص للتلاميذ الأوائل والأواخر
            if i < 3 or i >= len(students) - 3:
                print(f"التلميذ {i+1}: {student.get('name', 'بدون اسم')}, rt='{rt_value}'")

            # التحقق الذكي: هل التلميذ مخفي من لائحة الغياب؟
            is_hidden = self._is_student_hidden_from_attendance(rt_value)

            if is_hidden:
                hidden_count += 1
                print(f"تم إخفاء التلميذ: {student.get('name', 'بدون اسم')}, rt='{rt_value}'")
            else:
                filtered_students.append(student)

        print(f"عدد التلاميذ المخفيين: {hidden_count}")
        print(f"عدد التلاميذ بعد الفلترة: {len(filtered_students)}")
        print("=" * 50)

        # استخدام القائمة المفلترة بدلاً من القائمة الأصلية
        students = filtered_students

        # التأكد من وجود قيم الحصص - 10 حصص ثابتة
        if periods is None or len(periods) != 10:
            periods = [str(i) for i in range(1, 11)]  # القيم الافتراضية من "1" إلى "10"

        # افتراضياً، استخدم أيام النصف الثاني من الأسبوع
        if days is None:
            days = ["السبت", "الجمعة", "الخميس"]

        # تغيير حجم الصفحة إلى A4
        doc = SimpleDocTemplate(
            file_path,
            pagesize=A4,
            rightMargin=0.2*cm,
            leftMargin=0.2*cm,
            topMargin=0.2*cm,
            bottomMargin=0.2*cm
        )

        # إنشاء عناصر التقرير
        elements = self._get_report_elements(students, class_name, institution_data, start_date, days, periods, report_type)

        # بناء المستند
        doc.build(elements)
        return True

    def _get_report_elements(self, students, class_name, institution_data, start_date, days, periods, report_type=1):
        """
        إنشاء عناصر التقرير لقسم واحد
        """
        # فلترة التلاميذ - إزالة التلاميذ الذين لديهم علامة X في عمود رت
        filtered_students = []
        hidden_count = 0

        print(f"=== تشخيص فلترة التلاميذ في {class_name} (_get_report_elements النصف الثاني 10 حصص) ===")
        print(f"عدد التلاميذ قبل الفلترة: {len(students)}")

        for i, student in enumerate(students):
            # التحقق من وجود علامة X في عمود رت
            rt_value = student.get('rt', '') or student.get('id', '') or ''
            rt_value = str(rt_value).strip()

            # طباعة تشخيص للتلاميذ الأوائل والأواخر
            if i < 3 or i >= len(students) - 3:
                print(f"التلميذ {i+1}: {student.get('name', 'بدون اسم')}, rt='{rt_value}'")

            # التحقق الذكي: هل التلميذ مخفي من لائحة الغياب؟
            is_hidden = self._is_student_hidden_from_attendance(rt_value)

            if is_hidden:
                hidden_count += 1
                print(f"تم إخفاء التلميذ: {student.get('name', 'بدون اسم')}, rt='{rt_value}'")
            else:
                filtered_students.append(student)

        print(f"عدد التلاميذ المخفيين: {hidden_count}")
        print(f"عدد التلاميذ بعد الفلترة: {len(filtered_students)}")
        print("=" * 50)

        # استخدام القائمة المفلترة بدلاً من القائمة الأصلية
        students = filtered_students

        elements = []

        # إنشاء الرأس
        header_elements = self._create_header(institution_data, start_date)
        elements.extend(header_elements)

        # إضافة مساحة مقللة
        elements.append(Spacer(1, 0.1*cm))

        # إضافة مربع المؤسسة
        institution_box = self._create_institution_box(institution_data)
        elements.append(institution_box)
        elements.append(Spacer(1, 0.1*cm))

        # إضافة عنوان الورقة
        class_title = self._create_class_title(class_name)
        elements.append(class_title)
        elements.append(Spacer(1, 0.1*cm))

        # إنشاء جدول الحضور والعناصر المرتبطة به
        attendance_elements = self._create_attendance_table(students, class_name, days, periods, start_date)
        elements.extend(attendance_elements)

        return elements

    def _create_header(self, institution_data, start_date):
        """إنشاء رأس التقرير"""
        elements = []
        
        # تحديد نسب عرض الأعمدة
        right_column_width = 0.25  # 25% للعمود الأيمن (السنة الدراسية)
        center_column_width = 0.5   # 50% للعمود الأوسط (الشعار والمؤسسة)
        left_column_width = 0.25    # 25% للعمود الأيسر (فارغ)

        # إنشاء العمود الأيمن للسنة الدراسية
        right_column = []
        if 'academic_year' in institution_data:
            # إضافة مساحة فارغة لإنزال السنة الدراسية 20 نقطة
            from reportlab.platypus import Spacer
            right_column.append(Spacer(1, 20))  # 20 نقطة من المساحة الفارغة

            year_text = f"السنة الدراسية: {institution_data['academic_year']}"
            year_style = ParagraphStyle(
                'YearStyle',
                fontName=self.arabic_font,
                fontSize=12,
                alignment=2,  # يمين
                leading=14,
                textColor=reportlab_colors.blue
            )
            right_column.append(Paragraph(self.arabic_text(year_text), year_style))
        else:
            right_column.append(Paragraph("", getSampleStyleSheet()['Normal']))

        # إنشاء العمود الأوسط للشعار والمؤسسة
        center_column = []
        
        # إضافة الشعار إذا كان متوفراً
        if 'ImagePath1' in institution_data and institution_data['ImagePath1'] and os.path.exists(institution_data['ImagePath1']):
            try:
                from reportlab.platypus import Image
                logo = Image(institution_data['ImagePath1'])
                logo.drawWidth = 150
                logo.drawHeight = 50
                center_column.append(logo)
            except Exception as e:
                print(f"خطأ في تحميل الشعار: {str(e)}")



        # إنشاء العمود الأيسر فارغ
        left_column = []
        left_column.append(Paragraph("", getSampleStyleSheet()['Normal']))

        # إنشاء جدول الرأس
        data = [[right_column, center_column, left_column]]
        
        # إنشاء الجدول - استخدام عرض A4
        available_width = A4[0] - 0.4 * cm
        header_table = Table(data, colWidths=[
            available_width * right_column_width,
            available_width * center_column_width,
            available_width * left_column_width
        ])

        header_table.setStyle(TableStyle([
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('ALIGN', (0, 0), (0, 0), 'RIGHT'),
            ('ALIGN', (1, 0), (1, 0), 'CENTER'),
            ('ALIGN', (2, 0), (2, 0), 'LEFT'),
        ]))

        elements.append(header_table)
        return elements

    def _create_institution_box(self, institution_data):
        """إنشاء مربع المؤسسة"""
        # الحصول على اسم المؤسسة
        institution_name = institution_data.get('institution', 'المؤسسة غير محددة')
        institution_text = self.arabic_text(f"المؤسسة : {institution_name}")

        # إنشاء مربع النص للمؤسسة
        institution_box = Table(
            [[institution_text]],
            colWidths=[300],  # العرض 300
            rowHeights=[20]   # الارتفاع 20
        )

        # تنسيق مربع النص
        institution_box.setStyle(TableStyle([
            ('FONT', (0, 0), (0, 0), self.arabic_font, 12),
            ('ALIGN', (0, 0), (0, 0), 'CENTER'),
            ('VALIGN', (0, 0), (0, 0), 'MIDDLE'),
            ('TEXTCOLOR', (0, 0), (0, 0), reportlab_colors.black),
            ('BACKGROUND', (0, 0), (0, 0), reportlab_colors.white),
            ('GRID', (0, 0), (0, 0), 1, reportlab_colors.black),
        ]))

        return institution_box

    def _create_class_title(self, class_name):
        """إنشاء عنوان الورقة"""
        class_text = self.arabic_text(f"ورقة الغياب لقسم : {class_name}")
        
        # إنشاء مربع للقسم
        class_table = Table(
            [[class_text]],
            colWidths=[300],  # عرض مربع العنوان
            rowHeights=[20]   # ارتفاع مربع العنوان
        )
        
        # تنسيق مربع القسم
        class_table.setStyle(TableStyle([
            ('FONT', (0, 0), (0, 0), self.arabic_font, 14),
            ('ALIGN', (0, 0), (0, 0), 'CENTER'),
            ('VALIGN', (0, 0), (0, 0), 'MIDDLE'),
            ('TEXTCOLOR', (0, 0), (0, 0), reportlab_colors.black),
            ('BACKGROUND', (0, 0), (0, 0), reportlab_colors.white),
        ]))
        
        return class_table

    def _create_attendance_table(self, students, class_name, days, periods, start_date):
        """إنشاء جدول الحضور مع 10 حصص للنصف الثاني من الأسبوع"""

        # تحويل أسماء الأيام إلى العربية
        day_names_ar = {
            "الاثنين": "الاثنين",
            "الثلاثاء": "الثلاثاء",
            "الأربعاء": "الأربعاء",
            "الخميس": "الخميس",
            "الجمعة": "الجمعة",
            "السبت": "السبت"
        }

        selected_days_ar = [day_names_ar.get(day, day) for day in days]

        # حساب التواريخ - تاريخ كامل مع ترتيب صحيح للنصف الثاني
        # الأيام مرتبة: ["السبت", "الجمعة", "الخميس"]
        # لكن التواريخ يجب أن تكون: [الخميس+3, الجمعة+4, السبت+5]
        selected_dates = []
        day_order = {"الخميس": 3, "الجمعة": 4, "السبت": 5}

        for day in days:
            if day in day_order:
                date = start_date + datetime.timedelta(days=day_order[day])
                selected_dates.append(date.strftime("%d/%m/%Y"))
            else:
                # في حالة عدم وجود اليوم في القاموس، استخدم الترتيب الافتراضي
                date = start_date + datetime.timedelta(days=len(selected_dates) + 3)
                selected_dates.append(date.strftime("%d/%m/%Y"))

        # إنشاء بيانات الجدول
        data = []

        # صف الأيام
        day_row = []
        for day in selected_days_ar:
            day_row.append(self.arabic_text(day))  # تطبيق النص العربي لإصلاح الانقلاب
            day_row.extend([""] * 9)  # 9 خلايا فارغة للامتداد (10 حصص - 1 لاسم اليوم)
        day_row.append(self.arabic_text("الاسم والنسب"))
        day_row.append(self.arabic_text("رت"))
        data.append(day_row)

        # صف التواريخ
        date_row = []
        for date in selected_dates:
            date_row.append(date)
            date_row.extend([""] * 9)  # 9 خلايا فارغة للامتداد
        date_row.append("")
        date_row.append("")
        data.append(date_row)

        # صف الحصص
        period_row = []
        for _ in range(len(days)):
            period_row.extend(periods)  # إضافة جميع الحصص العشرة
        period_row.append("")
        period_row.append("")
        data.append(period_row)

        # صفوف الطلاب
        for student in students:
            student_row = []
            # الحصول على رقم الترتيب الفعلي من جدول اللوائح
            rt_value = student.get("rt", "") or student.get("id", "")
            # إزالة علامة X إذا كانت موجودة لعرض الرقم الأصلي فقط
            if str(rt_value).endswith(" X"):
                rt_display = str(rt_value)[:-2].strip()
            elif str(rt_value) == "X":
                rt_display = ""
            else:
                rt_display = str(rt_value)

            # إضافة خلايا لكل يوم من الأيام المختارة
            for _ in range(len(days)):
                student_row.append(rt_display)  # رقم الترتيب الفعلي من جدول اللوائح
                student_row.extend([""] * 9)  # 9 خلايا فارغة للحصص
            student_row.append(self.arabic_text(student["name"]))
            student_row.append(rt_display)  # رقم الترتيب الفعلي في العمود الأخير
            data.append(student_row)

        # إضافة صفوف فارغة (حد أقصى 7 صفوف بشرط عدم تجاوز 50 صفاً إجمالياً)
        current_rows = len(data)  # عدد الصفوف الحالية (رؤوس + طلاب)
        max_total_rows = 50
        max_empty_rows = 7

        # حساب عدد الصفوف الفارغة المطلوبة
        empty_rows_needed = min(max_empty_rows, max_total_rows - current_rows - 1)  # -1 لصف توقيع الأساتذة

        if empty_rows_needed > 0:
            for _ in range(empty_rows_needed):
                empty_row = []
                # إضافة خلايا فارغة لكل يوم
                for _ in range(len(days)):
                    empty_row.append("")  # خلية فارغة لرقم الترتيب
                    empty_row.extend([""] * 9)  # 9 خلايا فارغة للحصص (10 حصص)

                # إضافة خلايا فارغة لمعلومات الطالب
                empty_row.append("")  # الاسم والنسب
                empty_row.append("")  # رت
                data.append(empty_row)

        # إضافة صف "توقيع الأساتذة"
        footer_row = []
        for _ in range(len(days) * 10):  # 10 حصص لكل يوم
            footer_row.append("")  # خلايا فارغة للأيام
        footer_row.append(self.arabic_text("توقيع الأساتذة"))
        footer_row.append("")
        data.append(footer_row)

        # إعداد عرض الأعمدة - مخصص لـ 10 حصص
        available_width = A4[0] - 0.4 * cm
        attendance_width = (available_width - 5.2 * cm) / (len(days) * 10)  # 10 حصص
        col_widths = [attendance_width] * (len(days) * 10)
        col_widths.append(3.8 * cm)  # عرض الاسم والنسب
        col_widths.append(1.2 * cm)  # رت

        # تحديد ارتفاع الصفوف - ديناميكي حسب عدد الطلاب
        row_heights = [18, 18, 18]  # صفوف الرؤوس

        # حساب العدد الإجمالي للصفوف (طلاب + صفوف فارغة)
        total_student_rows = len(students) + (empty_rows_needed if empty_rows_needed > 0 else 0)

        # تحديد ارتفاع الصف حسب العدد الإجمالي
        if total_student_rows >= 45:
            student_row_height = 12
        elif total_student_rows >= 35:
            student_row_height = 13
        else:
            student_row_height = 14

        # إضافة ارتفاعات صفوف الطلاب
        for _ in range(len(students)):
            row_heights.append(student_row_height)  # ارتفاع ديناميكي للطلاب

        # إضافة ارتفاعات الصفوف الفارغة
        if empty_rows_needed > 0:
            for _ in range(empty_rows_needed):
                row_heights.append(student_row_height)  # نفس ارتفاع صفوف الطلاب

        row_heights.append(50)  # ارتفاع الصف الأخير

        # إنشاء الجدول
        table = Table(data, colWidths=col_widths, rowHeights=row_heights)

        # تنسيق الجدول
        table_style = TableStyle([
            ('FONT', (0, 0), (-1, -1), self.arabic_font, 10),  # خط أصغر لـ 10 حصص
            ('GRID', (0, 0), (-1, -1), 1, reportlab_colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('BACKGROUND', (0, 0), (-1, 2), reportlab_colors.lightgrey),
            ('FONTSIZE', (0, 0), (-1, 2), 13),  # زيادة حجم الخط للأيام والتواريخ
            ('TEXTCOLOR', (0, 0), (-1, 1), reportlab_colors.black),  # أسود غامق للأيام والتواريخ
        ])

        # إضافة دمج الخلايا لصفوف الأيام والتواريخ
        col_index = 0
        for day_index in range(len(days)):
            # دمج خلايا اليوم (10 خلايا لكل يوم)
            start_col = col_index
            end_col = col_index + 9
            table_style.add('SPAN', (start_col, 0), (end_col, 0))  # صف الأيام
            table_style.add('SPAN', (start_col, 1), (end_col, 1))  # صف التواريخ
            col_index += 10

        # دمج خلايا الاسم والنسب ورت
        table_style.add('SPAN', (-2, 0), (-2, 2))  # امتداد خلية الاسم والنسب
        table_style.add('SPAN', (-1, 0), (-1, 2))  # امتداد خلية رت

        table.setStyle(table_style)

        # إنشاء قائمة العناصر لإرجاعها
        table_elements = [table]

        # إضافة ملاحظة أسفل الجدول
        from reportlab.lib.styles import ParagraphStyle
        from reportlab.platypus import Paragraph, Spacer

        note_style = ParagraphStyle('Note', fontName=self.arabic_font, fontSize=11, alignment=1)
        note_text = self.arabic_text("المرجو من السادة الأساتذة عدم قبول أي تلميذ(ة) غير مسجل(ة) في اللائحة ولا يتوفر على ورقة السماح بالدخول")
        table_elements.append(Spacer(1, 0.1 * cm))  # مساحة صغيرة
        table_elements.append(Paragraph(note_text, note_style))

        return table_elements

    def generate_multi_class_pdf(self, file_path, class_data_list, institution_data, start_date, report_type, periods=None, days=None):
        """
        توليد ملف PDF يحتوي على عدة أقسام، كل قسم في صفحة مستقلة

        Args:
            file_path: مسار حفظ الملف
            class_data_list: قائمة من القواميس، كل قاموس يحتوي على اسم القسم وطلابه
            institution_data: بيانات المؤسسة
            start_date: تاريخ بداية الأسبوع
            report_type: نوع التقرير
            periods: قيم الحصص المستخدمة في التقرير
            days: أيام الأسبوع المطلوبة
        """
        # التأكد من وجود قيم الحصص - 10 حصص ثابتة
        if periods is None or len(periods) != 10:
            periods = [str(i) for i in range(1, 11)]  # القيم الافتراضية من "1" إلى "10"

        # افتراضياً، استخدم أيام النصف الثاني من الأسبوع
        if days is None:
            days = ["السبت", "الجمعة", "الخميس"]

        # إنشاء وثيقة PDF
        doc = SimpleDocTemplate(
            file_path,
            pagesize=A4,
            rightMargin=0.2*cm,
            leftMargin=0.2*cm,
            topMargin=0.2*cm,
            bottomMargin=0.2*cm
        )

        # قائمة العناصر لجميع الأقسام
        all_elements = []

        # إنشاء تقرير لكل قسم
        for i, class_data in enumerate(class_data_list):
            class_name = class_data['class_name']
            students = class_data['students']

            # إنشاء عناصر التقرير لهذا القسم
            class_elements = self._get_report_elements(students, class_name, institution_data, start_date, days, periods, report_type)
            all_elements.extend(class_elements)

            # إضافة فاصل صفحة بين الأقسام (إلا للقسم الأخير)
            if i < len(class_data_list) - 1:
                all_elements.append(PageBreak())

        # بناء المستند
        try:
            doc.build(all_elements)
            return True
        except Exception as e:
            print(f"خطأ في إنشاء ملف PDF متعدد الأقسام: {str(e)}")
            return False
