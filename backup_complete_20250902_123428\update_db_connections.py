"""
سكريبت لتحديث جميع استخدامات sqlite3.connect("data.db") في ملف معين
"""
import sys

def update_database_connections(filename):
    """تحديث جميع استخدامات قاعدة البيانات في الملف المحدد"""
    try:
        # قراءة الملف
        with open(filename, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        updated_lines = []
        changes_made = 0
        
        for i, line in enumerate(lines):
            if 'sqlite3.connect("data.db")' in line:
                # استبدال الاستخدام
                new_line = line.replace('sqlite3.connect("data.db")', 'sqlite3.connect(get_database_path())')
                updated_lines.append(new_line)
                changes_made += 1
                print(f"السطر {i+1}: تم التحديث")
            else:
                updated_lines.append(line)
        
        # كتابة الملف المحدث
        with open(filename, 'w', encoding='utf-8') as f:
            f.writelines(updated_lines)
        
        print(f"تم تحديث {changes_made} استخدام في {filename}")
        
    except Exception as e:
        print(f"خطأ في معالجة {filename}: {e}")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        filename = sys.argv[1]
    else:
        filename = "sub37_window.py"
    
    print(f"تحديث الملف: {filename}")
    update_database_connections(filename)
