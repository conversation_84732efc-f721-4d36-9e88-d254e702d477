"""
نافذة إدارة الأقسام والحراسة - Python + HTML
تم إعادة تصميم النافذة لتستخدم منهجية Python + HTML الحديثة

الميزات:
- واجهة HTML جميلة ومتجاوبة مع تبويبات
- إدارة الأقسام وتعيينها للحراسات المختلفة
- ثلاثة تبويبات منفصلة: المستويات، الأقسام، الأقسام المسندة
- تكامل كامل مع قاعدة البيانات
- تصميم عصري ومرن
"""

import sys
import os
import json
import sqlite3
from datetime import datetime
from database_config import get_database_path, get_database_connection
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QMessageBox
from PyQt5.QtWebEngineWidgets import QWebEngineView
from PyQt5.QtWebChannel import QWebChannel
from PyQt5.QtCore import QObject, pyqtSlot, pyqtSignal, Qt
from PyQt5.QtGui import QIcon

class SectionsManagementEngine(QObject):
    """محرك إدارة الأقسام والحراسة"""

    # إشارات للتواصل مع واجهة HTML
    logUpdated = pyqtSignal(str, str, str)  # message, status, timestamp
    dataUpdated = pyqtSignal(str)  # data JSON

    def __init__(self):
        super().__init__()
        self.db_path = get_database_path()
        self.current_year = ""
        self.current_guard = "حراسة رقم 1"

    def emit_log(self, message, status="info"):
        """إرسال رسالة إلى واجهة HTML"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.logUpdated.emit(message, status, timestamp)

    @pyqtSlot(result=str)
    def getYears(self):
        """الحصول على السنوات الدراسية"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT DISTINCT السنة_الدراسية FROM البنية_التربوية ORDER BY السنة_الدراسية DESC")
            years = cursor.fetchall()
            conn.close()
            
            result = [year[0] for year in years if year[0]]
            return json.dumps(result, ensure_ascii=False)
            
        except Exception as e:
            self.emit_log(f"❌ خطأ في جلب السنوات: {str(e)}", "error")
            return json.dumps([], ensure_ascii=False)

    @pyqtSlot(str, result=str)
    def getLevels(self, year):
        """الحصول على المستويات للسنة المحددة"""
        try:
            self.current_year = year
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # التأكد من وجود عمود ترتيب_المستويات
            try:
                cursor.execute("ALTER TABLE البنية_التربوية ADD COLUMN ترتيب_المستويات INTEGER DEFAULT 99")
                conn.commit()
            except:
                pass
            
            query = """
                SELECT السنة_الدراسية, المستوى, SUM(مجموع_التلاميذ) as مجموع
                FROM البنية_التربوية
                WHERE السنة_الدراسية = ?
                GROUP BY السنة_الدراسية, المستوى
                ORDER BY ترتيب_المستويات, المستوى
            """
            cursor.execute(query, (year,))
            levels = cursor.fetchall()
            conn.close()
            
            result = []
            for level in levels:
                result.append({
                    "السنة_الدراسية": level[0],
                    "المستوى": level[1],
                    "مجموع_التلاميذ": level[2]
                })
            
            return json.dumps(result, ensure_ascii=False)
            
        except Exception as e:
            self.emit_log(f"❌ خطأ في جلب المستويات: {str(e)}", "error")
            return json.dumps([], ensure_ascii=False)

    @pyqtSlot(str, str, result=str)
    def getSections(self, year, level):
        """الحصول على الأقسام للمستوى المحدد"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT ب.السنة_الدراسية, ب.القسم, ب.مجموع_التلاميذ, ب.الأقسام_المسندة
                FROM البنية_التربوية ب
                WHERE ب.المستوى = ? AND ب.السنة_الدراسية = ?
                ORDER BY ب.ترتيب_المستويات, ب.المستوى, ب.القسم
            """, (level, year))
            sections = cursor.fetchall()
            conn.close()
            
            result = []
            for section in sections:
                result.append({
                    "السنة_الدراسية": section[0],
                    "القسم": section[1],
                    "مجموع_التلاميذ": section[2],
                    "الأقسام_المسندة": section[3] or ""
                })
            
            # ترتيب الأقسام رقمياً
            result.sort(key=lambda x: self.section_sort_key(x["القسم"]))
            
            return json.dumps(result, ensure_ascii=False)
            
        except Exception as e:
            self.emit_log(f"❌ خطأ في جلب الأقسام: {str(e)}", "error")
            return json.dumps([], ensure_ascii=False)

    @pyqtSlot(str, str, result=str)
    def getAssignedSections(self, year, guard):
        """الحصول على الأقسام المسندة للحراسة"""
        try:
            self.current_guard = guard
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            params = [guard]
            query = "SELECT السنة_الدراسية, القسم, مجموع_التلاميذ FROM البنية_التربوية WHERE الأقسام_المسندة = ?"
            
            if year:
                query += " AND السنة_الدراسية = ?"
                params.append(year)
            
            query += " ORDER BY ترتيب_المستويات, المستوى, القسم"
            
            cursor.execute(query, params)
            sections = cursor.fetchall()
            conn.close()
            
            result = []
            for section in sections:
                result.append({
                    "السنة_الدراسية": section[0],
                    "القسم": section[1],
                    "مجموع_التلاميذ": section[2]
                })
            
            return json.dumps(result, ensure_ascii=False)
            
        except Exception as e:
            self.emit_log(f"❌ خطأ في جلب الأقسام المسندة: {str(e)}", "error")
            return json.dumps([], ensure_ascii=False)

    @pyqtSlot(str, str, str)
    def assignSection(self, year, section, guard):
        """تعيين قسم لحراسة"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE البنية_التربوية 
                SET الأقسام_المسندة = ? 
                WHERE القسم = ? AND السنة_الدراسية = ?
            """, (guard, section, year))
            
            conn.commit()
            conn.close()
            
            self.emit_log(f"✅ تم تعيين القسم {section} إلى {guard}", "success")
            self.dataUpdated.emit("sections_assigned")
            
        except Exception as e:
            self.emit_log(f"❌ خطأ في تعيين القسم: {str(e)}", "error")

    @pyqtSlot(str, str)
    def assignAllToGuard(self, year, guard):
        """تعيين جميع أقسام السنة لحراسة معينة"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE البنية_التربوية 
                SET الأقسام_المسندة = ? 
                WHERE السنة_الدراسية = ?
            """, (guard, year))
            
            updated_count = cursor.rowcount
            conn.commit()
            conn.close()
            
            self.emit_log(f"✅ تم تعيين {updated_count} قسم إلى {guard}", "success")
            self.dataUpdated.emit("all_sections_assigned")
            
        except Exception as e:
            self.emit_log(f"❌ خطأ في تعيين جميع الأقسام: {str(e)}", "error")

    @pyqtSlot(str)
    def updateLevelsOrder(self, levels_order_json):
        """تحديث ترتيب المستويات"""
        try:
            levels_order = json.loads(levels_order_json)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # التأكد من وجود عمود ترتيب_المستويات
            try:
                cursor.execute("ALTER TABLE البنية_التربوية ADD COLUMN ترتيب_المستويات INTEGER DEFAULT 99")
                conn.commit()
            except:
                pass
            
            # تعيين القيمة الافتراضية
            cursor.execute("UPDATE البنية_التربوية SET ترتيب_المستويات = 99")
            
            # تحديث ترتيب المستويات
            for level_data in levels_order:
                level = level_data["level"]
                order = level_data["order"]
                cursor.execute("""
                    UPDATE البنية_التربوية 
                    SET ترتيب_المستويات = ? 
                    WHERE المستوى LIKE ?
                """, (order, f"%{level}%"))
            
            conn.commit()
            conn.close()
            
            self.emit_log("✅ تم تحديث ترتيب المستويات بنجاح", "success")
            self.dataUpdated.emit("levels_order_updated")
            
        except Exception as e:
            self.emit_log(f"❌ خطأ في تحديث ترتيب المستويات: {str(e)}", "error")

    @pyqtSlot()
    def showLevelsOrderDialog(self):
        """عرض نافذة ترتيب المستويات باستخدام Python + HTML"""
        try:
            # إنشاء نافذة فرعية بتقنية HTML
            self.levels_window = LevelsOrderWindow(self)
            self.levels_window.show()
            
            # رسالة تأكيد
            self.emit_log("🗃️ تم فتح نافذة ترتيب المستويات", "info")
            
        except Exception as e:
            self.emit_log(f"❌ خطأ في عرض نافذة ترتيب المستويات: {str(e)}", "error")
            print(f"خطأ في showLevelsOrderDialog: {e}")
            import traceback
            traceback.print_exc()

    def getCurrentLevelsOrder(self):
        """استرجاع ترتيب المستويات الحالي من قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # التأكد من وجود العمود ترتيب_المستويات
            try:
                cursor.execute("ALTER TABLE البنية_التربوية ADD COLUMN ترتيب_المستويات INTEGER DEFAULT 99")
                conn.commit()
            except:
                pass

            # جلب المستويات الفريدة مع قيمة الترتيب الخاصة بكل منها
            query = """
                SELECT المستوى, ترتيب_المستويات
                FROM (
                    SELECT المستوى, ترتيب_المستويات, COUNT(*) as ظهور
                    FROM البنية_التربوية
                    GROUP BY المستوى
                )
                ORDER BY ترتيب_المستويات, المستوى
            """
            cursor.execute(query)
            results = cursor.fetchall()

            # تحويل النتائج إلى قائمة
            levels_data = [(level, order) for level, order in results]
            conn.close()
            return levels_data
            
        except Exception as e:
            self.emit_log(f"❌ خطأ في استرجاع ترتيب المستويات: {str(e)}", "error")
            return [
                ["جذع مشترك", 1],
                ["أولى بكالوريا", 2],
                ["ثانية بكالوريا", 3],
                ["الأولى", 10],
                ["الثانية", 11],
                ["الثالثة", 12]
            ]

    def section_sort_key(self, section_name):
        """دالة مساعدة لترتيب الأقسام رقمياً"""
        try:
            parts = section_name.split('-')
            num_part = parts[-1]
            if num_part.isdigit():
                return int(num_part)
            if section_name.isdigit():
                return int(section_name)
        except:
            pass
        return section_name

    @pyqtSlot()
    def refreshData(self):
        """تحديث جميع البيانات"""
        try:
            self.emit_log("🔄 جاري تحديث البيانات...", "info")
            self.dataUpdated.emit("refresh")
            self.emit_log("✅ تم تحديث البيانات بنجاح", "success")
            
        except Exception as e:
            self.emit_log(f"❌ خطأ في تحديث البيانات: {str(e)}", "error")


class SectionsManagementWindow(QMainWindow):
    """نافذة إدارة الأقسام والحراسة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("🏫 إدارة الأقسام والحراسة")
        
        # إعداد النافذة لتفتح في كامل الشاشة مع زر الإغلاق فقط
        self.setWindowFlags(Qt.Window | Qt.WindowCloseButtonHint | Qt.WindowTitleHint)
        
        # فتح النافذة في كامل الشاشة
        self.showMaximized()
        
        # إضافة أيقونة البرنامج
        icon_path = "01.ico"
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))

        # إنشاء محرك إدارة الأقسام
        self.sections_engine = SectionsManagementEngine()

        # إعداد الواجهة
        self.setup_ui()
        self.setup_web_channel()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(0, 0, 0, 0)

        # إنشاء عرض الويب
        self.web_view = QWebEngineView()
        layout.addWidget(self.web_view)

        # تحميل واجهة HTML
        html_content = self.get_complete_html()
        self.web_view.setHtml(html_content)

    def setup_web_channel(self):
        """إعداد قناة التواصل"""
        self.channel = QWebChannel()
        self.channel.registerObject("sectionsEngine", self.sections_engine)
        self.web_view.page().setWebChannel(self.channel)
        self.web_view.loadFinished.connect(self.on_page_loaded)

    def on_page_loaded(self):
        """استدعاء عند انتهاء تحميل الصفحة"""
        self.channel.registerObject("sectionsEngine", self.sections_engine)

    def center_window(self):
        """توسيط النافذة في الشاشة إذا لم تكن في كامل الشاشة"""
        from PyQt5.QtWidgets import QDesktopWidget
        desktop = QDesktopWidget()
        screen_rect = desktop.screenGeometry()
        window_rect = self.geometry()
        x = (screen_rect.width() - window_rect.width()) // 2
        y = (screen_rect.height() - window_rect.height()) // 2
        self.move(x, y)

    def closeEvent(self, event):
        """معالجة إغلاق النافذة"""
        event.accept()

    def get_complete_html(self):
        """HTML كامل مع CSS و JavaScript"""
        return """<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="utf-8">
    <title>إدارة الأقسام والحراسة</title>
    <script src="qrc:///qtwebchannel/qwebchannel.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Calibri', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            color: #333;
            overflow-x: hidden;
        }        .container {
            max-width: 1250px;
            margin: 0 auto;
            padding: 15px;
            min-height: 100vh;
        }        /* أدوات التحكم العلوية */
        .controls {
            background: rgba(255,255,255,0.9);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            display: flex;
            gap: 30px;
            align-items: flex-end;
            flex-wrap: wrap;
        }

        .control-group {
            flex: 1;
            min-width: 250px;
        }        .control-group:last-child {
            margin-bottom: 0;
        }        .action-buttons {
            display: flex;
            gap: 5px;
            margin-top: 10px;
        }

        .control-group label {
            display: block;
            font-family: 'Calibri', sans-serif;
            font-size: 14pt;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }        .control-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-family: 'Calibri', sans-serif;
            font-size: 13pt;
            font-weight: bold;
            background: white;
            color: #333;
            transition: border-color 0.3s ease;
            height: 48px;
        }

        .control-group select:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            flex: 1;
            font-family: 'Calibri', sans-serif;
            font-size: 12pt;
            font-weight: bold;
            color: white;
            padding: 0 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(0,0,0,0.2);
            min-width: 140px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(86, 171, 47, 0.4);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12 0%, #f8c471 100%);
        }

        .btn-warning:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(243, 156, 18, 0.4);
        }

        /* تبويبات */
        .tabs {
            display: flex;
            margin-bottom: 20px;
            background: rgba(255,255,255,0.9);
            border-radius: 15px;
            padding: 5px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .tab {
            flex: 1;
            padding: 12px 15px;
            background: transparent;
            border: none;
            cursor: pointer;
            font-family: 'Calibri', sans-serif;
            font-size: 13pt;
            font-weight: bold;
            color: #666;
            transition: all 0.3s ease;
            border-radius: 10px;
            text-align: center;
        }

        .tab.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 3px 10px rgba(102, 126, 234, 0.3);
        }

        .tab:hover {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
        }

        .tab.active:hover {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        /* محتوى التبويبات */
        .tab-content {
            display: none;
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
            min-height: 400px;
        }

        .tab-content.active {
            display: block;
        }        /* الجداول */
        .table-container {
            background: white;
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow-x: auto;
            max-height: 400px;
            overflow-y: auto;
        }

        .table-container::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        .table-container::-webkit-scrollbar-track {
            background: rgba(0,0,0,0.1);
            border-radius: 4px;
        }

        .table-container::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 4px;
        }

        .table-container::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
        }        table {
            width: 100%;
            border-collapse: collapse;
            font-family: 'Calibri', sans-serif;
            font-size: 14pt;
        }th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 8px;
            text-align: center;
            font-weight: bold;
            border: 2px solid #5a6fd8;
            font-size: 13pt;
        }        td {
            padding: 10px 8px;
            border: 2px solid #bbb;
            text-align: center;
            cursor: pointer;
            transition: background-color 0.3s ease;
            font-family: 'Calibri', sans-serif;
            font-size: 14pt;
            font-weight: bold;
            color: #000;
        }

        tr:nth-child(even) {
            background: #f9f9f9;
        }

        tr:hover {
            background: #e3f2fd;
        }

        .clickable-row {
            cursor: pointer;
        }

        .clickable-row:hover {
            background: #bbdefb !important;
        }

        /* رسائل الحالة */
        .message-box {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 10px;
            font-family: 'Calibri', sans-serif;
            font-size: 13pt;
            font-weight: bold;
            color: white;
            display: none;
            z-index: 1000;
            box-shadow: 0 5px 20px rgba(0,0,0,0.3);
            backdrop-filter: blur(10px);
            max-width: 300px;
        }

        .message-box.success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }

        .message-box.error {
            background: linear-gradient(135deg, #e74c3c 0%, #f39c12 100%);
        }

        .message-box.warning {
            background: linear-gradient(135deg, #f39c12 0%, #f8c471 100%);
        }

        .message-box.info {
            background: linear-gradient(135deg, #3498db 0%, #85c1e9 100%);
        }

        .message-box.show {
            display: block;
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* تحسينات للشاشات الصغيرة */
        @media (max-width: 700px) {
            .container {
                padding: 10px;
            }
            
            .tabs {
                flex-direction: column;
            }
            
            .tab {
                margin-bottom: 5px;
            }
            
            .action-buttons {
                flex-direction: column;
            }
            
            .btn {
                width: 100%;
                margin-bottom: 10px;
            }
        }

        /* مؤشر التحميل */
        .loading {
            text-align: center;
            padding: 20px;
            font-family: 'Calibri', sans-serif;
            font-size: 14pt;
            color: #666;
        }

        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #999;
            font-family: 'Calibri', sans-serif;
            font-size: 14pt;
        }

        .empty-state .icon {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.5;
        }

        /* نافذة التأكيد المخصصة */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 2000;
            backdrop-filter: blur(5px);
        }

        .modal-overlay.show {
            display: flex;
            animation: fadeIn 0.3s ease-out;
        }

        .confirmation-modal {
            background: white;
            border-radius: 20px;
            padding: 30px;
            max-width: 450px;
            width: 90%;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            text-align: center;
            position: relative;
            transform: scale(0.7);
            animation: modalSlideIn 0.3s ease-out forwards;
        }

        .modal-icon {
            font-size: 64px;
            margin-bottom: 20px;
            color: #667eea;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .modal-title {
            font-family: 'Calibri', sans-serif;
            font-size: 18pt;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            line-height: 1.4;
        }

        .modal-message {
            font-family: 'Calibri', sans-serif;
            font-size: 14pt;
            color: #666;
            margin-bottom: 25px;
            line-height: 1.6;
        }

        .modal-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .modal-btn {
            font-family: 'Calibri', sans-serif;
            font-size: 13pt;
            font-weight: bold;
            padding: 12px 25px;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 100px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .modal-btn-confirm {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
        }

        .modal-btn-confirm:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(86, 171, 47, 0.4);
        }

        .modal-btn-cancel {
            background: linear-gradient(135deg, #e74c3c 0%, #f39c12 100%);
            color: white;
        }

        .modal-btn-cancel:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes modalSlideIn {
            from {
                transform: scale(0.7) translateY(-50px);
                opacity: 0;
            }
            to {
                transform: scale(1) translateY(0);
                opacity: 1;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- أدوات التحكم -->
        <div class="controls">
            <div class="control-group">
                <select id="year-select" onchange="onYearChange()">
                    <option value="">اختر السنة الدراسية</option>
                </select>
            </div>

            <div class="control-group">
                <select id="guard-select" onchange="onGuardChange()">
                    <option value="حراسة رقم 1">حراسة رقم 1</option>
                    <option value="حراسة رقم 2">حراسة رقم 2</option>
                    <option value="حراسة رقم 3">حراسة رقم 3</option>
                    <option value="حراسة رقم 4">حراسة رقم 4</option>
                    <option value="حراسة رقم 5">حراسة رقم 5</option>
                </select>
            </div>

            <div class="action-buttons">
                <button class="btn btn-primary" onclick="refreshAllData()">
                    🔄 تحديث البيانات
                </button>
                <button class="btn btn-success" onclick="assignAllToCurrentGuard()">
                    ✅ تعيين الكل
                </button>
                <button class="btn btn-warning" onclick="showLevelsOrderDialog()">
                    📋 ترتيب المستويات
                </button>
            </div>
        </div>

        <!-- التبويبات -->
        <div class="tabs">
            <button class="tab active" onclick="showTab('levels')">📚 المستويات</button>
            <button class="tab" onclick="showTab('sections')">🏛️ الأقسام</button>
            <button class="tab" onclick="showTab('assigned')">✅ الأقسام المسندة</button>
        </div>

        <!-- محتوى تبويب المستويات -->
        <div id="levels-content" class="tab-content active">
            <div class="table-container">
                <table id="levels-table">
                    <thead>
                        <tr>
                            <th style="width: 40%;">السنة الدراسية</th>
                            <th style="width: 40%;">المستوى</th>
                            <th style="width: 20%;">مجموع التلاميذ</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td colspan="3" class="loading">جاري تحميل المستويات...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- محتوى تبويب الأقسام -->
        <div id="sections-content" class="tab-content">
            <div class="table-container">
                <table id="sections-table">
                    <thead>
                        <tr>
                            <th style="width: 25%;">السنة</th>
                            <th style="width: 35%;">القسم</th>
                            <th style="width: 20%;">التلاميذ</th>
                            <th style="width: 20%;">الحراسة</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td colspan="4" class="empty-state">
                                <div class="icon">📋</div>
                                <div>اختر مستوى من تبويب المستويات أولاً</div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- محتوى تبويب الأقسام المسندة -->
        <div id="assigned-content" class="tab-content">
            <div class="table-container">
                <table id="assigned-table">
                    <thead>
                        <tr>
                            <th style="width: 30%;">السنة الدراسية</th>
                            <th style="width: 40%;">القسم</th>
                            <th style="width: 30%;">مجموع التلاميذ</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td colspan="3" class="loading">جاري تحميل الأقسام المسندة...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- رسائل الحالة -->
        <div class="message-box" id="messageBox"></div>

        <!-- نافذة التأكيد المخصصة -->
        <div class="modal-overlay" id="confirmationModal">
            <div class="confirmation-modal">
                <div class="modal-icon"></div>
                <div class="modal-title" id="modalTitle">تأكيد العملية</div>
                <div class="modal-message" id="modalMessage">هل أنت متأكد من تعيين جميع الأقسام؟</div>
                <div class="modal-buttons">
                    <button class="modal-btn modal-btn-confirm" id="modalConfirm">✅ نعم، تأكيد</button>
                    <button class="modal-btn modal-btn-cancel" id="modalCancel">❌ إلغاء</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let sectionsEngine = null;
        let isChannelReady = false;
        let currentTab = 'levels';
        let currentYear = '';
        let currentGuard = 'حراسة رقم 1';
        let currentLevel = '';

        // إعداد قناة التواصل مع Python
        function initializeChannel() {
            if (typeof qt !== 'undefined' && qt.webChannelTransport) {
                new QWebChannel(qt.webChannelTransport, function(channel) {
                    sectionsEngine = channel.objects.sectionsEngine;
                    isChannelReady = true;

                    console.log('🚀 QWebChannel initialized successfully');

                    // ربط الإشارات
                    if (sectionsEngine) {
                        sectionsEngine.logUpdated.connect(handleLogUpdate);
                        sectionsEngine.dataUpdated.connect(handleDataUpdate);

                        // تحميل البيانات الأولية
                        loadYears();

                        console.log('✅ تم تهيئة نظام إدارة الأقسام بنجاح');
                    }
                });
            } else {
                console.log('⚠️ QWebChannel not available, retrying...');
                setTimeout(initializeChannel, 100);
            }
        }

        // تحميل السنوات الدراسية
        function loadYears() {
            if (sectionsEngine) {
                sectionsEngine.getYears(function(result) {
                    try {
                        let years;
                        if (typeof result === 'string') {
                            years = JSON.parse(result);
                        } else {
                            years = result;
                        }
                        
                        const yearSelect = document.getElementById('year-select');
                        yearSelect.innerHTML = '<option value="">اختر السنة الدراسية</option>';
                        
                        years.forEach(year => {
                            const option = document.createElement('option');
                            option.value = year;
                            option.textContent = year;
                            yearSelect.appendChild(option);
                        });
                        
                        if (years.length > 0) {
                            yearSelect.value = years[0];
                            currentYear = years[0];
                            loadLevels(currentYear);
                            loadAssignedSections();
                        }
                        
                    } catch (error) {
                        console.error('خطأ في تحليل بيانات السنوات:', error);
                    }
                });
            }
        }

        // تحميل المستويات
        function loadLevels(year) {
            if (sectionsEngine && year) {
                sectionsEngine.getLevels(year, function(result) {
                    try {
                        let levels;
                        if (typeof result === 'string') {
                            levels = JSON.parse(result);
                        } else {
                            levels = result;
                        }
                        
                        fillLevelsTable(levels);
                        
                    } catch (error) {
                        console.error('خطأ في تحليل بيانات المستويات:', error);
                    }
                });
            }
        }

        // ملء جدول المستويات
        function fillLevelsTable(levels) {
            const table = document.getElementById('levels-table');
            const tbody = table.querySelector('tbody');
            tbody.innerHTML = '';

            if (levels.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="3" class="empty-state">
                            <div class="icon">📚</div>
                            <div>لا توجد مستويات للسنة المحددة</div>
                        </td>
                    </tr>
                `;
                return;
            }

            levels.forEach(level => {
                const tr = document.createElement('tr');
                tr.className = 'clickable-row';
                tr.onclick = () => onLevelClick(level.المستوى);
                
                tr.innerHTML = `
                    <td>${level.السنة_الدراسية}</td>
                    <td>${level.المستوى}</td>
                    <td>${level.مجموع_التلاميذ}</td>
                `;
                
                tbody.appendChild(tr);
            });
        }

        // عند النقر على مستوى
        function onLevelClick(level) {
            currentLevel = level;
            showMessage(`📚 تم اختيار المستوى: ${level}`, 'info');
            loadSections(currentYear, level);
            showTab('sections');
        }

        // تحميل الأقسام
        function loadSections(year, level) {
            if (sectionsEngine && year && level) {
                sectionsEngine.getSections(year, level, function(result) {
                    try {
                        let sections;
                        if (typeof result === 'string') {
                            sections = JSON.parse(result);
                        } else {
                            sections = result;
                        }
                        
                        fillSectionsTable(sections);
                        
                    } catch (error) {
                        console.error('خطأ في تحليل بيانات الأقسام:', error);
                    }
                });
            }
        }

        // ملء جدول الأقسام
        function fillSectionsTable(sections) {
            const table = document.getElementById('sections-table');
            const tbody = table.querySelector('tbody');
            tbody.innerHTML = '';

            if (sections.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="4" class="empty-state">
                            <div class="icon">🏛️</div>
                            <div>لا توجد أقسام للمستوى المحدد</div>
                        </td>
                    </tr>
                `;
                return;
            }

            sections.forEach(section => {
                const tr = document.createElement('tr');
                tr.className = 'clickable-row';
                tr.onclick = () => onSectionClick(section);
                
                tr.innerHTML = `
                    <td>${section.السنة_الدراسية}</td>
                    <td>${section.القسم}</td>
                    <td>${section.مجموع_التلاميذ}</td>
                    <td>${section.الأقسام_المسندة || 'غير مسند'}</td>
                `;
                
                tbody.appendChild(tr);
            });
        }

        // عند النقر على قسم
        function onSectionClick(section) {
            if (sectionsEngine) {
                sectionsEngine.assignSection(section.السنة_الدراسية, section.القسم, currentGuard);
                // سيتم تحديث البيانات عبر الإشارة
            }
        }

        // تحميل الأقسام المسندة
        function loadAssignedSections() {
            if (sectionsEngine) {
                sectionsEngine.getAssignedSections(currentYear, currentGuard, function(result) {
                    try {
                        let sections;
                        if (typeof result === 'string') {
                            sections = JSON.parse(result);
                        } else {
                            sections = result;
                        }
                        
                        fillAssignedSectionsTable(sections);
                        
                    } catch (error) {
                        console.error('خطأ في تحليل بيانات الأقسام المسندة:', error);
                    }
                });
            }
        }

        // ملء جدول الأقسام المسندة
        function fillAssignedSectionsTable(sections) {
            const table = document.getElementById('assigned-table');
            const tbody = table.querySelector('tbody');
            tbody.innerHTML = '';

            if (sections.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="3" class="empty-state">
                            <div class="icon">✅</div>
                            <div>لا توجد أقسام مسندة لهذه الحراسة</div>
                        </td>
                    </tr>
                `;
                return;
            }

            let totalStudents = 0;
            sections.forEach(section => {
                totalStudents += parseInt(section.مجموع_التلاميذ) || 0;
                
                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>${section.السنة_الدراسية}</td>
                    <td>${section.القسم}</td>
                    <td>${section.مجموع_التلاميذ}</td>
                `;
                
                tbody.appendChild(tr);
            });

            // إضافة صف المجموع
            const totalRow = document.createElement('tr');
            totalRow.style.fontWeight = 'bold';
            totalRow.style.backgroundColor = '#e3f2fd';
            totalRow.innerHTML = `
                <td>المجموع</td>
                <td>${sections.length} قسم</td>
                <td>${totalStudents} تلميذ</td>
            `;
            tbody.appendChild(totalRow);
        }

        // تغيير السنة الدراسية
        function onYearChange() {
            const yearSelect = document.getElementById('year-select');
            currentYear = yearSelect.value;
            
            if (currentYear) {
                loadLevels(currentYear);
                loadAssignedSections();
                // مسح جدول الأقسام
                document.getElementById('sections-table').querySelector('tbody').innerHTML = `
                    <tr>
                        <td colspan="4" class="empty-state">
                            <div class="icon">📋</div>
                            <div>اختر مستوى من تبويب المستويات أولاً</div>
                        </td>
                    </tr>
                `;
            }
        }

        // تغيير الحراسة
        function onGuardChange() {
            const guardSelect = document.getElementById('guard-select');
            currentGuard = guardSelect.value;
            loadAssignedSections();
        }

        // عرض التبويب
        function showTab(tabName) {
            // إخفاء جميع التبويبات
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // إزالة التمييز من جميع أزرار التبويبات
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // إظهار التبويب المحدد
            document.getElementById(tabName + '-content').classList.add('active');
            
            // تمييز زر التبويب المحدد
            event.target.classList.add('active');
            
            currentTab = tabName;
        }

        // تحديث جميع البيانات
        function refreshAllData() {
            if (sectionsEngine) {
                sectionsEngine.refreshData();
                setTimeout(() => {
                    loadYears();
                }, 500);
            } else {
                showMessage('❌ النظام غير جاهز بعد', 'error');
            }
        }

        // تعيين جميع الأقسام للحراسة الحالية
        function assignAllToCurrentGuard() {
            if (!currentYear) {
                showMessage('⚠️ الرجاء اختيار سنة دراسية أولاً', 'warning');
                return;
            }
            
            // عرض نافذة التأكيد المخصصة
            showConfirmationModal(
                '🎯 تعيين جميع الأقسام',
                `هل أنت متأكد من تعيين جميع أقسام سنة <strong>${currentYear}</strong> إلى <strong>${currentGuard}</strong>؟<br><br>📋 سيتم تطبيق هذا التغيير على جميع الأقسام`,
                function() {
                    // تأكيد العملية
                    if (sectionsEngine) {
                        sectionsEngine.assignAllToGuard(currentYear, currentGuard);
                    }
                }
            );
        }

        // عرض نافذة التأكيد المخصصة
        function showConfirmationModal(title, message, onConfirm) {
            const modal = document.getElementById('confirmationModal');
            const modalTitle = document.getElementById('modalTitle');
            const modalMessage = document.getElementById('modalMessage');
            const confirmBtn = document.getElementById('modalConfirm');
            const cancelBtn = document.getElementById('modalCancel');

            modalTitle.textContent = title;
            modalMessage.innerHTML = message;
            
            modal.classList.add('show');

            // معالج زر التأكيد
            confirmBtn.onclick = function() {
                hideConfirmationModal();
                if (onConfirm) onConfirm();
            };

            // معالج زر الإلغاء
            cancelBtn.onclick = function() {
                hideConfirmationModal();
            };

            // إغلاق النافذة عند النقر خارجها
            modal.onclick = function(e) {
                if (e.target === modal) {
                    hideConfirmationModal();
                }
            };
        }

        // إخفاء نافذة التأكيد
        function hideConfirmationModal() {
            const modal = document.getElementById('confirmationModal');
            modal.classList.remove('show');
        }

        // عرض نافذة ترتيب المستويات
        function showLevelsOrderDialog() {
            if (sectionsEngine) {
                sectionsEngine.showLevelsOrderDialog();
            } else {
                showMessage('❌ النظام غير جاهز بعد', 'error');
            }
        }

        // معالجة تحديث السجل
        function handleLogUpdate(message, status, timestamp) {
            showMessage(message, status);
        }

        // معالجة تحديث البيانات
        function handleDataUpdate(action) {
            if (action === 'sections_assigned' || action === 'all_sections_assigned') {
                // تحديث الأقسام والأقسام المسندة
                if (currentLevel) {
                    loadSections(currentYear, currentLevel);
                }
                loadAssignedSections();
            } else if (action === 'levels_order_updated') {
                loadLevels(currentYear);
            } else if (action === 'refresh') {
                loadYears();
            }
        }

        // عرض الرسائل
        function showMessage(message, type) {
            const messageBox = document.getElementById('messageBox');
            messageBox.textContent = message;
            messageBox.className = `message-box ${type} show`;
            
            setTimeout(() => {
                messageBox.classList.remove('show');
            }, 3000);
        }

        // تهيئة النظام عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            initializeChannel();
        });

        // إغلاق النافذة المنبثقة بالضغط على Escape
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const modal = document.getElementById('confirmationModal');
                if (modal.classList.contains('show')) {
                    hideConfirmationModal();
                }
            }
        });
    </script>
</body>
</html>"""


class LevelsOrderWindow(QMainWindow):
    """نافذة ترتيب المستويات باستخدام Python + HTML"""
    
    def __init__(self, parent_engine):
        super().__init__()
        self.parent_engine = parent_engine
        self.setWindowTitle("🗃️ ترتيب المستويات")
        self.setFixedSize(700, 600)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # إضافة أيقونة البرنامج
        icon_path = "01.ico"
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))

        # إنشاء محرك إدارة ترتيب المستويات
        self.levels_engine = LevelsOrderEngine(parent_engine)

        # إعداد الواجهة
        self.setup_ui()
        self.setup_web_channel()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(0, 0, 0, 0)

        # إنشاء عرض الويب
        self.web_view = QWebEngineView()
        layout.addWidget(self.web_view)

        # تحميل واجهة HTML
        html_content = self.get_complete_html()
        self.web_view.setHtml(html_content)

    def setup_web_channel(self):
        """إعداد قناة التواصل"""
        self.channel = QWebChannel()
        self.channel.registerObject("levelsEngine", self.levels_engine)
        self.web_view.page().setWebChannel(self.channel)

    def get_complete_html(self):
        """HTML كامل لنافذة ترتيب المستويات"""
        return """<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="utf-8">
    <title>ترتيب المستويات</title>
    <script src="qrc:///qtwebchannel/qwebchannel.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Calibri', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            color: #333;
            padding: 20px;
        }

        .container {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 30px;
            max-width: 650px;
            margin: 0 auto;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            min-height: 500px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
        }

        .header .icon {
            font-size: 48px;
            margin-bottom: 15px;
            color: #667eea;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .header .title {
            font-family: 'Calibri', sans-serif;
            font-size: 24pt;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }

        .header .description {
            font-family: 'Calibri', sans-serif;
            font-size: 14pt;
            color: #666;
            line-height: 1.6;
        }

        .levels-container {
            margin: 20px 0;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .levels-table {
            width: 100%;
            border-collapse: collapse;
            font-family: 'Calibri', sans-serif;
        }

        .levels-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            text-align: center;
            font-weight: bold;
            font-size: 16pt;
            border: none;
        }

        .levels-table th:first-child {
            text-align: right;
        }

        .levels-table td {
            padding: 15px 20px;
            border-bottom: 1px solid #e0e0e0;
            transition: background-color 0.3s ease;
        }

        .levels-table tr:hover {
            background: #f8f9ff;
        }

        .levels-table tr:last-child td {
            border-bottom: none;
        }

        .level-name {
            font-family: 'Calibri', sans-serif;
            font-size: 16pt;
            font-weight: bold;
            color: #333;
            text-align: right;
        }

        .level-order {
            width: 100px;
            padding: 12px 15px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-family: 'Calibri', sans-serif;
            font-size: 16pt;
            font-weight: bold;
            text-align: center;
            background: #f9f9f9;
            transition: all 0.3s ease;
        }

        .level-order:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 10px rgba(102, 126, 234, 0.3);
        }

        .level-order:hover {
            border-color: #667eea;
        }

        .note {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border: 1px solid #90caf9;
            border-radius: 12px;
            padding: 20px;
            margin: 25px 0;
            font-family: 'Calibri', sans-serif;
            font-size: 14pt;
            color: #1565c0;
            text-align: center;
            box-shadow: 0 3px 10px rgba(21, 101, 192, 0.1);
        }

        .note .icon {
            font-size: 20px;
            margin-left: 8px;
            vertical-align: middle;
        }

        .buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }

        .btn {
            font-family: 'Calibri', sans-serif;
            font-size: 14pt;
            font-weight: bold;
            padding: 12px 30px;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 120px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .btn-save {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
        }

        .btn-save:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(86, 171, 47, 0.4);
        }

        .btn-cancel {
            background: linear-gradient(135deg, #e74c3c 0%, #f39c12 100%);
            color: white;
        }

        .btn-cancel:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
        }

        .loading {
            text-align: center;
            padding: 40px;
            font-family: 'Calibri', sans-serif;
            font-size: 16pt;
            color: #666;
        }

        .success-message {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            font-family: 'Calibri', sans-serif;
            font-size: 14pt;
            font-weight: bold;
            margin: 20px 0;
            display: none;
        }

        .error-message {
            background: linear-gradient(135deg, #e74c3c 0%, #f39c12 100%);
            color: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            font-family: 'Calibri', sans-serif;
            font-size: 14pt;
            font-weight: bold;
            margin: 20px 0;
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="icon">🗃️</div>
            <div class="title">ترتيب المستويات</div>
            <div class="description">يمكنك تعديل قيم الترتيب لكل مستوى دراسي أدناه</div>
        </div>

        <div class="success-message" id="successMessage"></div>
        <div class="error-message" id="errorMessage"></div>

        <div class="levels-container" id="levelsContainer">
            <table class="levels-table">
                <thead>
                    <tr>
                        <th style="width: 70%;">المستوى الدراسي</th>
                        <th style="width: 30%;">رقم الترتيب</th>
                    </tr>
                </thead>
                <tbody id="levelsTableBody">
                    <tr>
                        <td colspan="2" class="loading">🔄 جاري تحميل المستويات...</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="note">
            <span class="icon">💡</span>
            ملاحظة: القيم الأصغر (1, 2, 3) تظهر أولاً في الترتيب، ثم القيم الأكبر
        </div>

        <div class="buttons">
            <button class="btn btn-save" onclick="saveLevelsOrder()">✅ حفظ الترتيب</button>
            <button class="btn btn-cancel" onclick="cancelChanges()">❌ إلغاء</button>
        </div>
    </div>

    <script>
        let levelsEngine = null;
        let levelsData = [];

        // إعداد قناة التواصل مع Python
        function initializeChannel() {
            if (typeof qt !== 'undefined' && qt.webChannelTransport) {
                new QWebChannel(qt.webChannelTransport, function(channel) {
                    levelsEngine = channel.objects.levelsEngine;
                    console.log('🚀 تم تهيئة نظام ترتيب المستويات');
                    
                    // تحميل البيانات
                    loadLevelsData();
                });
            } else {
                console.log('⚠️ QWebChannel not available, retrying...');
                setTimeout(initializeChannel, 100);
            }
        }

        // تحميل بيانات المستويات
        function loadLevelsData() {
            if (levelsEngine) {
                levelsEngine.getCurrentLevelsOrder(function(result) {
                    try {
                        if (typeof result === 'string') {
                            levelsData = JSON.parse(result);
                        } else {
                            levelsData = result;
                        }
                        
                        displayLevels(levelsData);
                        
                    } catch (error) {
                        console.error('خطأ في تحليل بيانات المستويات:', error);
                        showError('خطأ في تحميل بيانات المستويات');
                    }
                });
            }
        }

        // عرض المستويات في الجدول
        function displayLevels(levels) {
            const tbody = document.getElementById('levelsTableBody');
            
            if (!levels || levels.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="2" class="loading">📚 لا توجد مستويات متاحة</td>
                    </tr>
                `;
                return;
            }

            let html = '';
            levels.forEach((level, index) => {
                html += `
                    <tr>
                        <td>
                            <div class="level-name">${level[0]}</div>
                        </td>
                        <td style="text-align: center;">
                            <input type="number" 
                                   class="level-order" 
                                   value="${level[1]}" 
                                   data-level="${level[0]}"
                                   min="1" 
                                   max="99"
                                   placeholder="ترتيب">
                        </td>
                    </tr>
                `;
            });
            
            tbody.innerHTML = html;
        }

        // حفظ ترتيب المستويات
        function saveLevelsOrder() {
            const orderInputs = document.querySelectorAll('.level-order');
            const updatedLevels = [];
            let hasError = false;

            orderInputs.forEach(input => {
                const level = input.getAttribute('data-level');
                const order = parseInt(input.value);

                if (isNaN(order) || order < 1 || order > 99) {
                    showError(`القيمة للمستوى "${level}" يجب أن تكون رقماً بين 1 و 99`);
                    input.focus();
                    hasError = true;
                    return;
                }

                updatedLevels.push({
                    level: level,
                    order: order
                });
            });

            if (hasError) return;

            // حفظ البيانات
            if (levelsEngine) {
                const dataJson = JSON.stringify(updatedLevels);
                levelsEngine.updateLevelsOrder(dataJson, function(success) {
                    if (success) {
                        showSuccess('✅ تم حفظ ترتيب المستويات بنجاح');
                        setTimeout(() => {
                            if (window.pyqtWindow) {
                                window.pyqtWindow.close();
                            }
                        }, 2000);
                    } else {
                        showError('❌ حدث خطأ أثناء حفظ ترتيب المستويات');
                    }
                });
            }
        }

        // إلغاء التغييرات
        function cancelChanges() {
            if (window.pyqtWindow) {
                window.pyqtWindow.close();
            }
        }

        // عرض رسالة نجاح
        function showSuccess(message) {
            const successMsg = document.getElementById('successMessage');
            const errorMsg = document.getElementById('errorMessage');
            
            errorMsg.style.display = 'none';
            successMsg.textContent = message;
            successMsg.style.display = 'block';
            
            setTimeout(() => {
                successMsg.style.display = 'none';
            }, 3000);
        }

        // عرض رسالة خطأ
        function showError(message) {
            const successMsg = document.getElementById('successMessage');
            const errorMsg = document.getElementById('errorMessage');
            
            successMsg.style.display = 'none';
            errorMsg.textContent = message;
            errorMsg.style.display = 'block';
            
            setTimeout(() => {
                errorMsg.style.display = 'none';
            }, 3000);
        }

        // تهيئة النظام عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            initializeChannel();
            
            // ربط النافذة بالمتغير العام للوصول إليها من JavaScript
            window.pyqtWindow = window;
        });

        // إغلاق النافذة بالضغط على Escape
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                cancelChanges();
            }
        });
    </script>
</body>
</html>"""


class LevelsOrderEngine(QObject):
    """محرك إدارة ترتيب المستويات"""
    
    def __init__(self, parent_engine):
        super().__init__()
        self.parent_engine = parent_engine
        self.db_path = parent_engine.db_path

    @pyqtSlot(result=str)
    def getCurrentLevelsOrder(self):
        """استرجاع ترتيب المستويات الحالي"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # التأكد من وجود العمود ترتيب_المستويات
            try:
                cursor.execute("ALTER TABLE البنية_التربوية ADD COLUMN ترتيب_المستويات INTEGER DEFAULT 99")
                conn.commit()
            except:
                pass

            # جلب المستويات الفريدة مع قيمة الترتيب الخاصة بكل منها
            query = """
                SELECT المستوى, ترتيب_المستويات
                FROM (
                    SELECT المستوى, ترتيب_المستويات, COUNT(*) as ظهور
                    FROM البنية_التربوية
                    GROUP BY المستوى
                )
                ORDER BY ترتيب_المستويات, المستوى
            """
            cursor.execute(query)
            results = cursor.fetchall()

            # تحويل النتائج إلى قائمة
            levels_data = [[level, order] for level, order in results]
            conn.close()
            
            return json.dumps(levels_data, ensure_ascii=False)
            
        except Exception as e:
            print(f"خطأ في استرجاع ترتيب المستويات: {e}")
            # قيم افتراضية
            default_levels = [
                ["جذع مشترك", 1],
                ["أولى بكالوريا", 2],
                ["ثانية بكالوريا", 3],
                ["الأولى", 10],
                ["الثانية", 11],
                ["الثالثة", 12]
            ]
            return json.dumps(default_levels, ensure_ascii=False)

    @pyqtSlot(str, result=bool)
    def updateLevelsOrder(self, levels_order_json):
        """تحديث ترتيب المستويات"""
        try:
            levels_order = json.loads(levels_order_json)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # التأكد من وجود عمود ترتيب_المستويات
            try:
                cursor.execute("ALTER TABLE البنية_التربوية ADD COLUMN ترتيب_المستويات INTEGER DEFAULT 99")
                conn.commit()
            except:
                pass
            
            # تعيين القيمة الافتراضية
            cursor.execute("UPDATE البنية_التربوية SET ترتيب_المستويات = 99")
            
            # تحديث ترتيب المستويات
            for level_data in levels_order:
                level = level_data["level"]
                order = level_data["order"]
                cursor.execute("""
                    UPDATE البنية_التربوية 
                    SET ترتيب_المستويات = ? 
                    WHERE المستوى LIKE ?
                """, (order, f"%{level}%"))
            
            conn.commit()
            conn.close()
            
            # إشعار النافذة الرئيسية بالتحديث
            self.parent_engine.emit_log("✅ تم تحديث ترتيب المستويات بنجاح", "success")
            self.parent_engine.dataUpdated.emit("levels_order_updated")
            
            return True
            
        except Exception as e:
            print(f"خطأ في تحديث ترتيب المستويات: {e}")
            self.parent_engine.emit_log(f"❌ خطأ في تحديث ترتيب المستويات: {str(e)}", "error")
            return False


class SectionsManagementWindow(QMainWindow):
    """نافذة إدارة الأقسام والحراسة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("🏫 إدارة الأقسام والحراسة")
        
        # إعداد النافذة لتفتح في كامل الشاشة مع زر الإغلاق فقط
        self.setWindowFlags(Qt.Window | Qt.WindowCloseButtonHint | Qt.WindowTitleHint)
        
        # فتح النافذة في كامل الشاشة
        self.showMaximized()
        
        # إضافة أيقونة البرنامج
        icon_path = "01.ico"
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))

        # إنشاء محرك إدارة الأقسام
        self.sections_engine = SectionsManagementEngine()

        # إعداد الواجهة
        self.setup_ui()
        self.setup_web_channel()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(0, 0, 0, 0)

        # إنشاء عرض الويب
        self.web_view = QWebEngineView()
        layout.addWidget(self.web_view)

        # تحميل واجهة HTML
        html_content = self.get_complete_html()
        self.web_view.setHtml(html_content)

    def setup_web_channel(self):
        """إعداد قناة التواصل"""
        self.channel = QWebChannel()
        self.channel.registerObject("sectionsEngine", self.sections_engine)
        self.web_view.page().setWebChannel(self.channel)
        self.web_view.loadFinished.connect(self.on_page_loaded)

    def on_page_loaded(self):
        """استدعاء عند انتهاء تحميل الصفحة"""
        self.channel.registerObject("sectionsEngine", self.sections_engine)

    def center_window(self):
        """توسيط النافذة في الشاشة إذا لم تكن في كامل الشاشة"""
        from PyQt5.QtWidgets import QDesktopWidget
        desktop = QDesktopWidget()
        screen_rect = desktop.screenGeometry()
        window_rect = self.geometry()
        x = (screen_rect.width() - window_rect.width()) // 2
        y = (screen_rect.height() - window_rect.height()) // 2
        self.move(x, y)

    def closeEvent(self, event):
        """معالجة إغلاق النافذة"""
        event.accept()

    def get_complete_html(self):
        """HTML كامل مع CSS و JavaScript"""
        return """<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="utf-8">
    <title>إدارة الأقسام والحراسة</title>
    <script src="qrc:///qtwebchannel/qwebchannel.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Calibri', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1250px;
            margin: 0 auto;
            padding: 15px;
            min-height: 100vh;
        }

        /* أدوات التحكم العلوية */
        .controls {
            background: rgba(255,255,255,0.9);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            display: flex;
            gap: 30px;
            align-items: flex-end;
            flex-wrap: wrap;
        }

        .control-group {
            flex: 1;
            min-width: 250px;
        }

        .control-group:last-child {
            margin-bottom: 0;
        }

        .action-buttons {
            display: flex;
            gap: 5px;
            margin-top: 10px;
        }

        .control-group label {
            display: block;
            font-family: 'Calibri', sans-serif;
            font-size: 14pt;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }

        .control-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-family: 'Calibri', sans-serif;
            font-size: 13pt;
            font-weight: bold;
            background: white;
            color: #333;
            transition: border-color 0.3s ease;
            height: 48px;
        }

        .control-group select:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            flex: 1;
            font-family: 'Calibri', sans-serif;
            font-size: 12pt;
            font-weight: bold;
            color: white;
            padding: 0 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(0,0,0,0.2);
            min-width: 140px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(86, 171, 47, 0.4);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12 0%, #f8c471 100%);
        }

        .btn-warning:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(243, 156, 18, 0.4);
        }

        /* تبويبات */
        .tabs {
            display: flex;
            margin-bottom: 20px;
            background: rgba(255,255,255,0.9);
            border-radius: 15px;
            padding: 5px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .tab {
            flex: 1;
            padding: 12px 15px;
            background: transparent;
            border: none;
            cursor: pointer;
            font-family: 'Calibri', sans-serif;
            font-size: 13pt;
            font-weight: bold;
            color: #666;
            transition: all 0.3s ease;
            border-radius: 10px;
            text-align: center;
        }

        .tab.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 3px 10px rgba(102, 126, 234, 0.3);
        }

        .tab:hover {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
        }

        .tab.active:hover {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        /* محتوى التبويبات */
        .tab-content {
            display: none;
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
            min-height: 400px;
        }

        .tab-content.active {
            display: block;
        }

        /* الجداول */
        .table-container {
            background: white;
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow-x: auto;
            max-height: 400px;
            overflow-y: auto;
        }

        .table-container::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        .table-container::-webkit-scrollbar-track {
            background: rgba(0,0,0,0.1);
            border-radius: 4px;
        }

        .table-container::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 4px;
        }

        .table-container::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
        }

        table {
            width: 100%;
            border-collapse: collapse;
            font-family: 'Calibri', sans-serif;
            font-size: 14pt;
        }

        th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 8px;
            text-align: center;
            font-weight: bold;
            border: 2px solid #5a6fd8;
            font-size: 13pt;
        }

        td {
            padding: 10px 8px;
            border: 2px solid #bbb;
            text-align: center;
            cursor: pointer;
            transition: background-color 0.3s ease;
            font-family: 'Calibri', sans-serif;
            font-size: 14pt;
            font-weight: bold;
            color: #000;
        }

        tr:nth-child(even) {
            background: #f9f9f9;
        }

        tr:hover {
            background: #e3f2fd;
        }

        .clickable-row {
            cursor: pointer;
        }

        .clickable-row:hover {
            background: #bbdefb !important;
        }

        /* رسائل الحالة */
        .message-box {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 10px;
            font-family: 'Calibri', sans-serif;
            font-size: 13pt;
            font-weight: bold;
            color: white;
            display: none;
            z-index: 1000;
            box-shadow: 0 5px 20px rgba(0,0,0,0.3);
            backdrop-filter: blur(10px);
            max-width: 300px;
        }

        .message-box.success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }

        .message-box.error {
            background: linear-gradient(135deg, #e74c3c 0%, #f39c12 100%);
        }

        .message-box.warning {
            background: linear-gradient(135deg, #f39c12 0%, #f8c471 100%);
        }

        .message-box.info {
            background: linear-gradient(135deg, #3498db 0%, #85c1e9 100%);
        }

        .message-box.show {
            display: block;
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* تحسينات للشاشات الصغيرة */
        @media (max-width: 700px) {
            .container {
                padding: 10px;
            }
            
            .tabs {
                flex-direction: column;
            }
            
            .tab {
                margin-bottom: 5px;
            }
            
            .action-buttons {
                flex-direction: column;
            }
            
            .btn {
                width: 100%;
                margin-bottom: 10px;
            }
        }

        /* مؤشر التحميل */
        .loading {
            text-align: center;
            padding: 20px;
            font-family: 'Calibri', sans-serif;
            font-size: 14pt;
            color: #666;
        }

        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #999;
            font-family: 'Calibri', sans-serif;
            font-size: 14pt;
        }

        .empty-state .icon {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.5;
        }

        /* نافذة التأكيد المخصصة */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 2000;
            backdrop-filter: blur(5px);
        }

        .modal-overlay.show {
            display: flex;
            animation: fadeIn 0.3s ease-out;
        }

        .confirmation-modal {
            background: white;
            border-radius: 20px;
            padding: 30px;
            max-width: 450px;
            width: 90%;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            text-align: center;
            position: relative;
            transform: scale(0.7);
            animation: modalSlideIn 0.3s ease-out forwards;
        }

        .modal-icon {
            font-size: 64px;
            margin-bottom: 20px;
            color: #667eea;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .modal-title {
            font-family: 'Calibri', sans-serif;
            font-size: 18pt;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            line-height: 1.4;
        }

        .modal-message {
            font-family: 'Calibri', sans-serif;
            font-size: 14pt;
            color: #666;
            margin-bottom: 25px;
            line-height: 1.6;
        }

        .modal-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .modal-btn {
            font-family: 'Calibri', sans-serif;
            font-size: 13pt;
            font-weight: bold;
            padding: 12px 25px;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 100px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .modal-btn-confirm {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
        }

        .modal-btn-confirm:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(86, 171, 47, 0.4);
        }

        .modal-btn-cancel {
            background: linear-gradient(135deg, #e74c3c 0%, #f39c12 100%);
            color: white;
        }

        .modal-btn-cancel:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes modalSlideIn {
            from {
                transform: scale(0.7) translateY(-50px);
                opacity: 0;
            }
            to {
                transform: scale(1) translateY(0);
                opacity: 1;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- أدوات التحكم -->
        <div class="controls">
            <div class="control-group">
                <select id="year-select" onchange="onYearChange()">
                    <option value="">اختر السنة الدراسية</option>
                </select>
            </div>

            <div class="control-group">
                <select id="guard-select" onchange="onGuardChange()">
                    <option value="حراسة رقم 1">حراسة رقم 1</option>
                    <option value="حراسة رقم 2">حراسة رقم 2</option>
                    <option value="حراسة رقم 3">حراسة رقم 3</option>
                    <option value="حراسة رقم 4">حراسة رقم 4</option>
                    <option value="حراسة رقم 5">حراسة رقم 5</option>
                </select>
            </div>

            <div class="action-buttons">
                <button class="btn btn-primary" onclick="refreshAllData()">
                    🔄 تحديث البيانات
                </button>
                <button class="btn btn-success" onclick="assignAllToCurrentGuard()">
                    ✅ تعيين الكل
                </button>
                <button class="btn btn-warning" onclick="showLevelsOrderDialog()">
                    📋 ترتيب المستويات
                </button>
            </div>
        </div>

        <!-- التبويبات -->
        <div class="tabs">
            <button class="tab active" onclick="showTab('levels')">📚 المستويات</button>
            <button class="tab" onclick="showTab('sections')">🏛️ الأقسام</button>
            <button class="tab" onclick="showTab('assigned')">✅ الأقسام المسندة</button>
        </div>

        <!-- محتوى تبويب المستويات -->
        <div id="levels-content" class="tab-content active">
            <div class="table-container">
                <table id="levels-table">
                    <thead>
                        <tr>
                            <th style="width: 40%;">السنة الدراسية</th>
                            <th style="width: 40%;">المستوى</th>
                            <th style="width: 20%;">مجموع التلاميذ</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td colspan="3" class="loading">جاري تحميل المستويات...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- محتوى تبويب الأقسام -->
        <div id="sections-content" class="tab-content">
            <div class="table-container">
                <table id="sections-table">
                    <thead>
                        <tr>
                            <th style="width: 25%;">السنة</th>
                            <th style="width: 35%;">القسم</th>
                            <th style="width: 20%;">التلاميذ</th>
                            <th style="width: 20%;">الحراسة</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td colspan="4" class="empty-state">
                                <div class="icon">📋</div>
                                <div>اختر مستوى من تبويب المستويات أولاً</div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- محتوى تبويب الأقسام المسندة -->
        <div id="assigned-content" class="tab-content">
            <div class="table-container">
                <table id="assigned-table">
                    <thead>
                        <tr>
                            <th style="width: 30%;">السنة الدراسية</th>
                            <th style="width: 40%;">القسم</th>
                            <th style="width: 30%;">مجموع التلاميذ</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td colspan="3" class="loading">جاري تحميل الأقسام المسندة...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- رسائل الحالة -->
        <div class="message-box" id="messageBox"></div>

        <!-- نافذة التأكيد المخصصة -->
        <div class="modal-overlay" id="confirmationModal">
            <div class="confirmation-modal">
                <div class="modal-icon"></div>
                <div class="modal-title" id="modalTitle">تأكيد العملية</div>
                <div class="modal-message" id="modalMessage">هل أنت متأكد من تعيين جميع الأقسام؟</div>
                <div class="modal-buttons">
                    <button class="modal-btn modal-btn-confirm" id="modalConfirm">✅ نعم، تأكيد</button>
                    <button class="modal-btn modal-btn-cancel" id="modalCancel">❌ إلغاء</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let sectionsEngine = null;
        let isChannelReady = false;
        let currentTab = 'levels';
        let currentYear = '';
        let currentGuard = 'حراسة رقم 1';
        let currentLevel = '';

        // إعداد قناة التواصل مع Python
        function initializeChannel() {
            if (typeof qt !== 'undefined' && qt.webChannelTransport) {
                new QWebChannel(qt.webChannelTransport, function(channel) {
                    sectionsEngine = channel.objects.sectionsEngine;
                    isChannelReady = true;

                    console.log('🚀 QWebChannel initialized successfully');

                    // ربط الإشارات
                    if (sectionsEngine) {
                        sectionsEngine.logUpdated.connect(handleLogUpdate);
                        sectionsEngine.dataUpdated.connect(handleDataUpdate);

                        // تحميل البيانات الأولية
                        loadYears();

                        console.log('✅ تم تهيئة نظام إدارة الأقسام بنجاح');
                    }
                });
            } else {
                console.log('⚠️ QWebChannel not available, retrying...');
                setTimeout(initializeChannel, 100);
            }
        }

        // تحميل السنوات الدراسية
        function loadYears() {
            if (sectionsEngine) {
                sectionsEngine.getYears(function(result) {
                    try {
                        let years;
                        if (typeof result === 'string') {
                            years = JSON.parse(result);
                        } else {
                            years = result;
                        }
                        
                        const yearSelect = document.getElementById('year-select');
                        yearSelect.innerHTML = '<option value="">اختر السنة الدراسية</option>';
                        
                        years.forEach(year => {
                            const option = document.createElement('option');
                            option.value = year;
                            option.textContent = year;
                            yearSelect.appendChild(option);
                        });
                        
                        if (years.length > 0) {
                            yearSelect.value = years[0];
                            currentYear = years[0];
                            loadLevels(currentYear);
                            loadAssignedSections();
                        }
                        
                    } catch (error) {
                        console.error('خطأ في تحليل بيانات السنوات:', error);
                    }
                });
            }
        }

        // تحميل المستويات
        function loadLevels(year) {
            if (sectionsEngine && year) {
                sectionsEngine.getLevels(year, function(result) {
                    try {
                        let levels;
                        if (typeof result === 'string') {
                            levels = JSON.parse(result);
                        } else {
                            levels = result;
                        }
                        
                        fillLevelsTable(levels);
                        
                    } catch (error) {
                        console.error('خطأ في تحليل بيانات المستويات:', error);
                    }
                });
            }
        }

        // ملء جدول المستويات
        function fillLevelsTable(levels) {
            const table = document.getElementById('levels-table');
            const tbody = table.querySelector('tbody');
            tbody.innerHTML = '';

            if (levels.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="3" class="empty-state">
                            <div class="icon">📚</div>
                            <div>لا توجد مستويات للسنة المحددة</div>
                        </td>
                    </tr>
                `;
                return;
            }

            levels.forEach(level => {
                const tr = document.createElement('tr');
                tr.className = 'clickable-row';
                tr.onclick = () => onLevelClick(level.المستوى);
                
                tr.innerHTML = `
                    <td>${level.السنة_الدراسية}</td>
                    <td>${level.المستوى}</td>
                    <td>${level.مجموع_التلاميذ}</td>
                `;
                
                tbody.appendChild(tr);
            });
        }

        // عند النقر على مستوى
        function onLevelClick(level) {
            currentLevel = level;
            showMessage(`📚 تم اختيار المستوى: ${level}`, 'info');
            loadSections(currentYear, level);
            showTab('sections');
        }

        // تحميل الأقسام
        function loadSections(year, level) {
            if (sectionsEngine && year && level) {
                sectionsEngine.getSections(year, level, function(result) {
                    try {
                        let sections;
                        if (typeof result === 'string') {
                            sections = JSON.parse(result);
                        } else {
                            sections = result;
                        }
                        
                        fillSectionsTable(sections);
                        
                    } catch (error) {
                        console.error('خطأ في تحليل بيانات الأقسام:', error);
                    }
                });
            }
        }

        // ملء جدول الأقسام
        function fillSectionsTable(sections) {
            const table = document.getElementById('sections-table');
            const tbody = table.querySelector('tbody');
            tbody.innerHTML = '';

            if (sections.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="4" class="empty-state">
                            <div class="icon">🏛️</div>
                            <div>لا توجد أقسام للمستوى المحدد</div>
                        </td>
                    </tr>
                `;
                return;
            }

            sections.forEach(section => {
                const tr = document.createElement('tr');
                tr.className = 'clickable-row';
                tr.onclick = () => onSectionClick(section);
                
                tr.innerHTML = `
                    <td>${section.السنة_الدراسية}</td>
                    <td>${section.القسم}</td>
                    <td>${section.مجموع_التلاميذ}</td>
                    <td>${section.الأقسام_المسندة || 'غير مسند'}</td>
                `;
                
                tbody.appendChild(tr);
            });
        }

        // عند النقر على قسم
        function onSectionClick(section) {
            if (sectionsEngine) {
                sectionsEngine.assignSection(section.السنة_الدراسية, section.القسم, currentGuard);
                // سيتم تحديث البيانات عبر الإشارة
            }
        }

        // تحميل الأقسام المسندة
        function loadAssignedSections() {
            if (sectionsEngine) {
                sectionsEngine.getAssignedSections(currentYear, currentGuard, function(result) {
                    try {
                        let sections;
                        if (typeof result === 'string') {
                            sections = JSON.parse(result);
                        } else {
                            sections = result;
                        }
                        
                        fillAssignedSectionsTable(sections);
                        
                    } catch (error) {
                        console.error('خطأ في تحليل بيانات الأقسام المسندة:', error);
                    }
                });
            }
        }

        // ملء جدول الأقسام المسندة
        function fillAssignedSectionsTable(sections) {
            const table = document.getElementById('assigned-table');
            const tbody = table.querySelector('tbody');
            tbody.innerHTML = '';

            if (sections.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="3" class="empty-state">
                            <div class="icon">✅</div>
                            <div>لا توجد أقسام مسندة لهذه الحراسة</div>
                        </td>
                    </tr>
                `;
                return;
            }

            let totalStudents = 0;
            sections.forEach(section => {
                totalStudents += parseInt(section.مجموع_التلاميذ) || 0;
                
                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>${section.السنة_الدراسية}</td>
                    <td>${section.القسم}</td>
                    <td>${section.مجموع_التلاميذ}</td>
                `;
                
                tbody.appendChild(tr);
            });

            // إضافة صف المجموع
            const totalRow = document.createElement('tr');
            totalRow.style.fontWeight = 'bold';
            totalRow.style.backgroundColor = '#e3f2fd';
            totalRow.innerHTML = `
                <td>المجموع</td>
                <td>${sections.length} قسم</td>
                <td>${totalStudents} تلميذ</td>
            `;
            tbody.appendChild(totalRow);
        }

        // تغيير السنة الدراسية
        function onYearChange() {
            const yearSelect = document.getElementById('year-select');
            currentYear = yearSelect.value;
            
            if (currentYear) {
                loadLevels(currentYear);
                loadAssignedSections();
                // مسح جدول الأقسام
                document.getElementById('sections-table').querySelector('tbody').innerHTML = `
                    <tr>
                        <td colspan="4" class="empty-state">
                            <div class="icon">📋</div>
                            <div>اختر مستوى من تبويب المستويات أولاً</div>
                        </td>
                    </tr>
                `;
            }
        }

        // تغيير الحراسة
        function onGuardChange() {
            const guardSelect = document.getElementById('guard-select');
            currentGuard = guardSelect.value;
            loadAssignedSections();
        }

        // عرض التبويب
        function showTab(tabName) {
            // إخفاء جميع التبويبات
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // إزالة التمييز من جميع أزرار التبويبات
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // إظهار التبويب المحدد
            document.getElementById(tabName + '-content').classList.add('active');
            
            // تمييز زر التبويب المحدد
            event.target.classList.add('active');
            
            currentTab = tabName;
        }

        // تحديث جميع البيانات
        function refreshAllData() {
            if (sectionsEngine) {
                sectionsEngine.refreshData();
                setTimeout(() => {
                    loadYears();
                }, 500);
            } else {
                showMessage('❌ النظام غير جاهز بعد', 'error');
            }
        }

        // تعيين جميع الأقسام للحراسة الحالية
        function assignAllToCurrentGuard() {
            if (!currentYear) {
                showMessage('⚠️ الرجاء اختيار سنة دراسية أولاً', 'warning');
                return;
            }
            
            // عرض نافذة التأكيد المخصصة
            showConfirmationModal(
                '🎯 تعيين جميع الأقسام',
                `هل أنت متأكد من تعيين جميع أقسام سنة <strong>${currentYear}</strong> إلى <strong>${currentGuard}</strong>؟<br><br>📋 سيتم تطبيق هذا التغيير على جميع الأقسام`,
                function() {
                    // تأكيد العملية
                    if (sectionsEngine) {
                        sectionsEngine.assignAllToGuard(currentYear, currentGuard);
                    }
                }
            );
        }

        // عرض نافذة التأكيد المخصصة
        function showConfirmationModal(title, message, onConfirm) {
            const modal = document.getElementById('confirmationModal');
            const modalTitle = document.getElementById('modalTitle');
            const modalMessage = document.getElementById('modalMessage');
            const confirmBtn = document.getElementById('modalConfirm');
            const cancelBtn = document.getElementById('modalCancel');

            modalTitle.textContent = title;
            modalMessage.innerHTML = message;
            
            modal.classList.add('show');

            // معالج زر التأكيد
            confirmBtn.onclick = function() {
                hideConfirmationModal();
                if (onConfirm) onConfirm();
            };

            // معالج زر الإلغاء
            cancelBtn.onclick = function() {
                hideConfirmationModal();
            };

            // إغلاق النافذة عند النقر خارجها
            modal.onclick = function(e) {
                if (e.target === modal) {
                    hideConfirmationModal();
                }
            };
        }

        // إخفاء نافذة التأكيد
        function hideConfirmationModal() {
            const modal = document.getElementById('confirmationModal');
            modal.classList.remove('show');
        }

        // عرض نافذة ترتيب المستويات
        function showLevelsOrderDialog() {
            if (sectionsEngine) {
                sectionsEngine.showLevelsOrderDialog();
            } else {
                showMessage('❌ النظام غير جاهز بعد', 'error');
            }
        }

        // معالجة تحديث السجل
        function handleLogUpdate(message, status, timestamp) {
            showMessage(message, status);
        }

        // معالجة تحديث البيانات
        function handleDataUpdate(action) {
            if (action === 'sections_assigned' || action === 'all_sections_assigned') {
                // تحديث الأقسام والأقسام المسندة
                if (currentLevel) {
                    loadSections(currentYear, currentLevel);
                }
                loadAssignedSections();
            } else if (action === 'levels_order_updated') {
                loadLevels(currentYear);
            } else if (action === 'refresh') {
                loadYears();
            }
        }

        // عرض الرسائل
        function showMessage(message, type) {
            const messageBox = document.getElementById('messageBox');
            messageBox.textContent = message;
            messageBox.className = `message-box ${type} show`;
            
            setTimeout(() => {
                messageBox.classList.remove('show');
            }, 3000);
        }

        // تهيئة النظام عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            initializeChannel();
        });

        // إغلاق النافذة المنبثقة بالضغط على Escape
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const modal = document.getElementById('confirmationModal');
                if (modal.classList.contains('show')) {
                    hideConfirmationModal();
                }
            }
        });
    </script>
</body>
</html>"""


def main():
    """دالة التشغيل الرئيسية"""
    app = QApplication(sys.argv)
    
    # إعداد التطبيق
    app.setApplicationName("إدارة الأقسام والحراسة")
    app.setOrganizationName("نظام إدارة المؤسسة")
    
    # إنشاء وعرض النافذة الرئيسية
    window = SectionsManagementWindow()
    window.show()
    
    # تشغيل التطبيق
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()