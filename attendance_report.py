import os
import datetime
from reportlab.lib import colors
from reportlab.lib.pagesizes import A3
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, PageBreak
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.lib.units import cm

# إضافة هذه المكتبات لدعم العربية
import arabic_reshaper
from bidi.algorithm import get_display

class AttendanceReport:
    """فئة لإنشاء تقارير الغياب الأسبوعية"""

    def __init__(self):
        # تحميل الخطوط العربية مع إعطاء الأولوية لخط Calibri
        self.arabic_font = self._load_arabic_fonts()

    def _load_arabic_fonts(self):
        """تحميل الخطوط العربية المتاحة"""
        try:
            # الخطوط التي تدعم العربية بشكل جيد مع تقديم Calibri
            arabic_fonts = [
                ("Calibri", "c:/windows/fonts/calibri.ttf"),  # وضع Calibri في المقدمة
                ("ARIALUNI", "c:/windows/fonts/ARIALUNI.TTF"),
                ("Arial", "c:/windows/fonts/arial.ttf"),
                ("TraditionalArabic", "c:/windows/fonts/trado.ttf"),
                ("SimplifiedArabic", "c:/windows/fonts/simpo.ttf"),
                ("ArabicTypesetting", "c:/windows/fonts/arabtype.ttf"),
                ("Tahoma", "c:/windows/fonts/tahoma.ttf")
            ]

            # تسجيل أول خط متاح
            for font_name, font_path in arabic_fonts:
                if (os.path.exists(font_path)):
                    pdfmetrics.registerFont(TTFont(font_name, font_path))
                    return font_name

            return 'Helvetica'  # استخدام خط افتراضي إذا لم يتم العثور على خط عربي

        except Exception as e:
            print(f"خطأ في تحميل الخطوط: {str(e)}")
            return 'Helvetica'

    def arabic_text(self, text):
        """معالجة النص العربي لعرضه بشكل صحيح في PDF"""
        # إعادة تشكيل النص العربي وضبط اتجاه الكتابة
        reshaped_text = arabic_reshaper.reshape(text)
        bidi_text = get_display(reshaped_text)
        return bidi_text

    def _is_student_hidden_from_attendance(self, rt_value):
        """
        التحقق الذكي من إخفاء التلميذ من لائحة الغياب
        يتحقق من وجود علامة X في عمود رت
        """
        if not rt_value:
            return False

        rt_value = str(rt_value).strip()

        # التلميذ مخفي إذا كان عمود رت يحتوي على "X" (إما X فقط أو رقم + مسافة + X)
        return rt_value == "X" or rt_value.endswith(" X")

    def generate_pdf(self, file_path, students, class_name, institution_data, start_date, report_type, periods=None):
        """توليد ملف PDF لتقرير الغياب"""
        # فلترة التلاميذ - إزالة التلاميذ الذين لديهم علامة X في عمود رت
        filtered_students = []
        hidden_count = 0

        print(f"=== تشخيص فلترة التلاميذ في {class_name} (attendance_report.py) ===")
        print(f"عدد التلاميذ قبل الفلترة: {len(students)}")

        for i, student in enumerate(students):
            # التحقق من وجود علامة X في عمود رت
            rt_value = student.get('rt', '') or student.get('id', '') or ''
            rt_value = str(rt_value).strip()

            # طباعة تشخيص للتلاميذ الأوائل والأواخر
            if i < 3 or i >= len(students) - 3:
                print(f"التلميذ {i+1}: {student.get('name', 'بدون اسم')}, rt='{rt_value}'")

            # التحقق الذكي: هل التلميذ مخفي من لائحة الغياب؟
            is_hidden = self._is_student_hidden_from_attendance(rt_value)

            if is_hidden:
                hidden_count += 1
                print(f"تم إخفاء التلميذ: {student.get('name', 'بدون اسم')}, rt='{rt_value}'")
            else:
                filtered_students.append(student)

        print(f"عدد التلاميذ المخفيين: {hidden_count}")
        print(f"عدد التلاميذ بعد الفلترة: {len(filtered_students)}")
        print("=" * 50)

        # استخدام القائمة المفلترة بدلاً من القائمة الأصلية
        students = filtered_students

        # تحديد عدد الحصص بناءً على نوع التقرير
        if report_type == 4:  # أربع حصص
            num_periods = 4
            if periods is None or len(periods) < 4:
                periods = [str(i) for i in range(1, 5)]  # القيم الافتراضية من "1" إلى "4"
            else:
                periods = periods[:4]  # أخذ أول 4 حصص فقط
        elif report_type == 5:  # خمس حصص
            num_periods = 5
            if periods is None or len(periods) < 5:
                periods = [str(i) for i in range(1, 6)]  # القيم الافتراضية من "1" إلى "5"
            else:
                periods = periods[:5]  # أخذ أول 5 حصص فقط
        else:  # التقرير العادي (8 حصص)
            num_periods = 8
            if periods is None or len(periods) != 8:
                periods = [str(i) for i in range(1, 9)]  # القيم الافتراضية من "1" إلى "8"

        # إنشاء وثيقة PDF - تعديل الهوامش إلى 0.2 سم على اليمين واليسار لجعل الجدول يأخذ كامل عرض الصفحة
        doc = SimpleDocTemplate(
            file_path,
            pagesize=A3,  # اتجاه عمودي A3
            rightMargin=0.2*cm,  # هامش 0.2 سم من اليمين
            leftMargin=0.2*cm,   # هامش 0.2 سم من اليسار
            topMargin=0.1*cm,
            bottomMargin=0.1*cm
        )

        # قائمة لعناصر PDF
        elements = []

        # تعريف الأنماط مع استخدام خط Calibri حجم مختلف
        styles = getSampleStyleSheet()



        # نمط للسنة الدراسية (تحت المديرية) - Calibri 13 غليظ
        year_style = ParagraphStyle(
            'YearStyle',
            parent=styles['Normal'],
            fontName=self.arabic_font,
            fontSize=13,  # حجم 13
            alignment=2,  # محاذاة لليمين (RTL)
            leading=14,
            rightIndent=0,
            spaceAfter=0,
            spaceBefore=2,  # مسافة صغيرة قبل النص
            textColor=colors.black,
            fontWeight='bold'
        )

        # نمط اسم القسم (تحت المؤسسة) - Calibri 14 غليظ
        class_style = ParagraphStyle(
            'ClassStyle',
            parent=styles['Normal'],
            fontName=self.arabic_font,
            fontSize=14,  # حجم 14
            alignment=1,  # توسيط
            leading=16,
            rightIndent=0,
            spaceAfter=0,
            spaceBefore=4,  # مسافة قبل النص
            textColor=colors.black,
            fontWeight='bold'
        )

        # إضافة الشعار في وسط الصفحة
        if 'ImagePath1' in institution_data and institution_data['ImagePath1'] and os.path.exists(institution_data['ImagePath1']):
            from reportlab.platypus import Image
            print(f"تم العثور على مسار الشعار: {institution_data['ImagePath1']}")
            try:
                logo = Image(institution_data['ImagePath1'])
                logo.drawWidth = 200  # عرض الشعار 200 نقطة
                logo.drawHeight = 70  # ارتفاع الشعار 70 نقطة
                logo.hAlign = 'CENTER'  # محاذاة الشعار في وسط الصفحة
                elements.append(logo)
                print("تم إضافة الشعار بنجاح")
            except Exception as e:
                print(f"خطأ في تحميل الشعار: {str(e)}")
        else:
            print("لم يتم العثور على مسار الشعار أو الملف غير موجود")
            if 'ImagePath1' in institution_data:
                print(f"مسار الشعار: {institution_data['ImagePath1']}")
            else:
                print("مفتاح ImagePath1 غير موجود في بيانات المؤسسة")

        # إضافة مسافة صغيرة بعد الشعار
        elements.append(Spacer(1, 0.3*cm))

        # إنشاء نمط جديد للسنة الدراسية بخط Calibri 17 أزرق غامق
        year_style_blue = ParagraphStyle(
            'YearStyleBlue',
            parent=styles['Normal'],
            fontName=self.arabic_font,
            fontSize=17,  # حجم 17
            alignment=1,  # توسيط
            leading=20,
            rightIndent=0,
            spaceAfter=0,
            spaceBefore=0,
            textColor=colors.navy,  # لون أزرق غامق
            fontWeight='bold'
        )

        # إضافة السنة الدراسية تحت الشعار مباشرة
        year_text = self.arabic_text(f"السنة الدراسية : {institution_data['academic_year']}")
        elements.append(Paragraph(year_text, year_style_blue))
        elements.append(Spacer(1, 0.3*cm))

        # إضافة اسم المؤسسة في الوسط مع حدود بالشكل المطلوب
        # استخدام عمود "المؤسسة" من جدول بيانات_المؤسسة
        institution_name = institution_data.get('institution', 'المؤسسة التعليمية')
        institution_text = self.arabic_text(f"المؤسسة : {institution_name}")

        # إنشاء جدول لعرض المؤسسة بمقاسات محددة - توسيط عمودي وأفقي
        institution_table = Table(
            [[institution_text]],
            colWidths=[300],    # عرض 300 بكسل
            rowHeights=[30]     # ارتفاع 30 بكسل
        )

        institution_table.setStyle(TableStyle([
            ('FONT', (0, 0), (0, 0), self.arabic_font, 16, 1),  # Calibri حجم 16 غليظ
            ('TEXTCOLOR', (0, 0), (0, 0), colors.black),
            ('ALIGN', (0, 0), (0, 0), 'CENTER'),     # توسيط النص أفقيًا
            ('VALIGN', (0, 0), (0, 0), 'MIDDLE'),    # توسيط النص عموديًا بشكل صريح
            ('GRID', (0, 0), (0, 0), 2, colors.black, None, (3, 3)),  # حدود بسمك 2 نقطة ونمط متقطع
            ('LEFTPADDING', (0, 0), (0, 0), 5),      # تقليل التباعد الجانبي
            ('RIGHTPADDING', (0, 0), (0, 0), 5),     # تقليل التباعد الجانبي
            ('TOPPADDING', (0, 0), (0, 0), 0),       # إزالة التباعد العلوي
            ('BOTTOMPADDING', (0, 0), (0, 0), 0),    # إزالة التباعد السفلي
            ('LEADING', (0, 0), (0, 0), 20),         # تقليل المسافة بين السطور
        ]))

        # إنشاء إطار خارجي لجدول المؤسسة لضمان توسيطه في الصفحة
        outer_table = Table(
            [[institution_table]],
            colWidths=[300],
            rowHeights=[30]
        )

        outer_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (0, 0), 'CENTER'),     # توسيط الجدول الداخلي
            ('VALIGN', (0, 0), (0, 0), 'MIDDLE'),    # توسيط الجدول الداخلي
            ('LEFTPADDING', (0, 0), (0, 0), 0),      # إزالة التباعد للإطار الخارجي
            ('RIGHTPADDING', (0, 0), (0, 0), 0),     # إزالة التباعد للإطار الخارجي
            ('TOPPADDING', (0, 0), (0, 0), 0),       # إزالة التباعد للإطار الخارجي
            ('BOTTOMPADDING', (0, 0), (0, 0), 0),    # إزالة التباعد للإطار الخارجي
        ]))

        # إضافة مساحة فارغة متساوية قبل وبعد العنوان للتوسيط في الصفحة
        elements.append(Spacer(1, 0.8*cm))
        elements.append(outer_table)

        # إضافة اسم القسم تحت المؤسسة
        class_paragraph = Paragraph(
            self.arabic_text(f"ورقة الغياب لقسم : {class_name}"), class_style
        )
        elements.append(class_paragraph)
        elements.append(Spacer(1, 0.5*cm))

        # إنشاء جدول الحضور - تغيير ترتيب الأيام والتواريخ من اليمين إلى اليسار مع إضافة يوم السبت
        arabic_days = [self.arabic_text("السبت"), self.arabic_text("الجمعة"), self.arabic_text("الخميس"), self.arabic_text("الأربعاء"),
                self.arabic_text("الثلاثاء"), self.arabic_text("الاثنين")]

        # حساب تواريخ الأسبوع بدءًا من التاريخ المحدد
        week_dates = []
        for i in range(6):
            current_date = start_date + datetime.timedelta(days=5-i)
            formatted_date = current_date.strftime("%Y-%m-%d")
            week_dates.append(self.arabic_text(formatted_date))

        # بيانات الجدول
        data = []

        # صف جديد أعلى الجدول: مقسم حسب الأيام
        new_header_row = []

        # إضافة 6 خلايا منفصلة للأيام الستة
        for day in arabic_days:
            new_header_row.append("")

        # إضافة خلية لكل يوم ممتدة على عدد الحصص المطلوب
        for _ in range((len(arabic_days) * num_periods) - len(arabic_days)):
            new_header_row.append("")  # إضافة الخلايا المتبقية لتغطية كامل الأعمدة

        # إضافة خلية نص الطلاب وخلية رت فارغة
        new_header_row.append(self.arabic_text("تلاميذ يوجهون الى الإدارة"))  # نص فوق الاسم والنسب
        new_header_row.append("")  # خلية فارغة فوق الرت
        data.append(new_header_row)

        # الصف 1: ترويسة معلومات الطالب والأيام
        row1 = []
        # إضافة الأيام من اليمين إلى اليسار
        for day in arabic_days:
            row1.append(day)
            # إضافة خلايا فارغة لإفساح المجال للتواريخ والحصص
            row1.extend([""] * 7)
        # ثم إضافة معلومات الطالب
        row1.append(self.arabic_text("الاسم والنسب"))
        row1.append(self.arabic_text("رت"))
        data.append(row1)

        # الصف 2: التواريخ بدلاً من الفترات
        row2 = []
        for i, date in enumerate(week_dates):
            # إضافة التاريخ مع span على عدد الحصص المطلوب
            row2.append(date)  # إضافة التاريخ
            row2.extend([""] * (num_periods - 1))  # امتداد على عدد الحصص المطلوب
        row2.append("")
        row2.append("")
        data.append(row2)

        # الصف 3: الحصص - استخدام عدد الحصص المحدد لكل يوم
        row3 = []
        for day_index in range(len(arabic_days)):
            # نضيف الحصص المطلوبة لكل يوم
            row3.extend(periods)

        row3.append("")  # خلية فارغة للاسم والنسب
        row3.append("")  # خلية فارغة للرت
        data.append(row3)

        # إضافة صفوف الطلاب مع تأكيد ظهور رقم الترتيب في أول خلية من كل يوم
        for student in students:
            student_row = []
            # الحصول على رقم الترتيب الفعلي من جدول اللوائح
            rt_value = student.get("rt", "") or student.get("id", "")
            # إزالة علامة X إذا كانت موجودة لعرض الرقم الأصلي فقط
            if str(rt_value).endswith(" X"):
                rt_display = str(rt_value)[:-2].strip()
            elif str(rt_value) == "X":
                rt_display = ""
            else:
                rt_display = str(rt_value)

            # إضافة خلايا لعلامات الحضور لكل يوم
            for day_index in range(len(arabic_days)):
                # إضافة رقم الترتيب (رت) في العمود الأول من كل يوم بشكل واضح
                student_row.append(rt_display)  # رقم الترتيب الفعلي من جدول اللوائح

                # إضافة خلايا فارغة للباقي حسب عدد الحصص
                for _ in range(num_periods - 1):
                    student_row.append("")

            # ثم إضافة معلومات الطالب في نهاية الصف
            student_row.append(self.arabic_text(student["name"]))  # الاسم والنسب
            student_row.append(rt_display)  # رقم الترتيب الفعلي في العمود الأخير
            data.append(student_row)

        # إضافة صفوف فارغة (حد أقصى 7 صفوف بشرط عدم تجاوز 50 صفاً إجمالياً)
        current_rows = len(data)  # عدد الصفوف الحالية (رؤوس + طلاب)
        max_total_rows = 50
        max_empty_rows = 7

        # حساب عدد الصفوف الفارغة المطلوبة
        empty_rows_needed = min(max_empty_rows, max_total_rows - current_rows - 1)  # -1 لصف توقيع الأساتذة

        if empty_rows_needed > 0:
            for _ in range(empty_rows_needed):
                empty_row = []
                # إضافة خلايا فارغة لكل يوم
                for day_index in range(len(arabic_days)):
                    empty_row.append("")  # خلية فارغة لرقم الترتيب
                    # إضافة خلايا فارغة للحصص
                    for _ in range(num_periods - 1):
                        empty_row.append("")

                # إضافة خلايا فارغة لمعلومات الطالب
                empty_row.append("")  # الاسم والنسب
                empty_row.append("")  # رت
                data.append(empty_row)

        # إضافة صف جديد أسفل الجدول: توقيع الأساتذة
        footer_row = []
        for i in range(len(arabic_days) * num_periods):
            footer_row.append("")  # خلايا فارغة تحت الأيام مقسمة لكل عمود

        footer_row.append(self.arabic_text("توقيع الأساتذة"))  # نص تحت الاسم والنسب
        footer_row.append("")  # خلية فارغة تحت الرت
        data.append(footer_row)

        # تعديل حساب عرض الأعمدة - الجدول يأخذ كامل عرض الصفحة مع هوامش 0.2 سم
        available_width = A3[0] - 0.4*cm  # A3 width minus margins

        # أعمدة الحضور - 6 أيام × عدد الحصص المطلوب
        attendance_cell_width = (available_width - 6.2*cm) / (len(arabic_days) * num_periods)
        col_widths = [attendance_cell_width] * (len(arabic_days) * num_periods)

        # ثم إضافة أعمدة معلومات الطالب
        col_widths.append(4*cm)  # الاسم والنسب
        col_widths.append(1.2*cm)  # رت

        # إنشاء الجدول مع العرض المحدد للأعمدة والارتفاع المحدد للصفوف
        # تعيين ارتفاع الصفوف مع تعديل ارتفاع الصف العلوي والسفلي إلى 70
        row_heights = [70, 15, 15, 15]  # الصف الأول بارتفاع 70، ثم صفوف الرؤوس بارتفاع 15

        # إضافة ارتفاعات صفوف الطلاب
        for _ in range(len(students)):
            row_heights.append(15)  # ارتفاع صفوف الطلاب 15 بكسل

        # إضافة ارتفاعات الصفوف الفارغة
        if empty_rows_needed > 0:
            for _ in range(empty_rows_needed):
                row_heights.append(15)  # ارتفاع الصفوف الفارغة 15 بكسل

        # إضافة ارتفاع الصف السفلي
        row_heights.append(70)  # الصف الأخير بارتفاع 70

        # إنشاء الجدول مع العرض والارتفاع المحددين
        table = Table(data, colWidths=col_widths, rowHeights=row_heights)

        # تنسيق الجدول
        table_style = TableStyle([
            # تنسيق أساسي
            ('FONT', (0, 0), (-1, -1), self.arabic_font, 12),  # حجم الخط 12 للجدول
            ('GRID', (0, 0), (-1, -1), 1, colors.black),  # سماكة خط 1 نقطة
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),

            # تحسين المحاذاة العمودية للجميع
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),

            # تنسيق الصف العلوي (تلاميذ يوجهون الى الإدارة)
            ('ALIGN', (-2, 0), (-2, 0), 'CENTER'),
            ('VALIGN', (-2, 0), (-2, 0), 'MIDDLE'),
            ('FONT', (-2, 0), (-2, 0), self.arabic_font, 12, 1),  # خط غليظ للنص

            # تنسيق الصف السفلي (توقيع الأساتذة)
            ('ALIGN', (-2, -1), (-2, -1), 'CENTER'),
            ('VALIGN', (-2, -1), (-2, -1), 'MIDDLE'),
            ('VALIGN', (0, -1), (-3, -1), 'MIDDLE'),  # محاذاة عمودية لوسط الخلايا الفارغة
            ('FONT', (-2, -1), (-2, -1), self.arabic_font, 12, 1),  # خط غليظ للنص

            # التنسيقات الأخرى
            ('LEFTPADDING', (0, 1), (-1, 3), 2),
            ('RIGHTPADDING', (0, 1), (-1, 3), 2),
            ('TOPPADDING', (0, 1), (-1, 3), 0),
            ('BOTTOMPADDING', (0, 1), (-1, 3), 2),

            ('LEFTPADDING', (0, 4), (-1, -2), 3),
            ('RIGHTPADDING', (0, 4), (-1, -2), 3),
            ('TOPPADDING', (0, 4), (-1, -2), 1),
            ('BOTTOMPADDING', (0, 4), (-1, -2), 3),

            ('ALIGN', (0, 1), (-1, -2), 'CENTER'),

            # تنسيق رؤوس الأعمدة
            ('BACKGROUND', (0, 1), (-1, 3), colors.lightgrey),
            ('FONTSIZE', (0, 1), (-1, 3), 14),
            ('FONTNAME', (0, 1), (-1, 3), self.arabic_font),
            ('FONT', (0, 1), (-1, 3), self.arabic_font, 12, 1),

            # تطبيق محاذاة عمودية خاصة لرؤوس الأعمدة
            ('VALIGN', (0, 1), (-1, 5), 'TOP'),

            # تنسيق أعمدة رت والاسم والنسب
            ('SPAN', (-1, 1), (-1, 3)),  # رت
            ('SPAN', (-2, 1), (-2, 3)),  # الاسم والنسب
        ])

        # إضافة امتداد (SPAN) للأيام والتواريخ
        for i in range(6):  # 6 أيام
            col_start = i * num_periods
            col_end = col_start + (num_periods - 1)
            table_style.add('SPAN', (col_start, 1), (col_end, 1))  # span للأيام
            table_style.add('SPAN', (col_start, 2), (col_end, 2))  # span للتواريخ

        # تنسيق خاص للتواريخ
        for i in range(6):
            col_start = i * num_periods
            col_end = col_start + (num_periods - 1)
            table_style.add('TOPPADDING', (col_start, 2), (col_end, 2), 1)
            table_style.add('BOTTOMPADDING', (col_start, 2), (col_end, 2), 0)
            table_style.add('VALIGN', (col_start, 2), (col_end, 2), 'TOP')

        # تنسيق إضافي للحصص - تطبيق على كل خلية حصة بشكل فردي
        for i in range(6):
            for j in range(num_periods):
                cell_col = i * num_periods + j
                table_style.add('VALIGN', (cell_col, 3), (cell_col, 3), 'TOP')
                table_style.add('TOPPADDING', (cell_col, 3), (cell_col, 3), 0)
                table_style.add('BOTTOMPADDING', (cell_col, 3), (cell_col, 3), 0)

        # إضافة امتداد (SPAN) لخلايا الصف العلوي لكل يوم على حدة
        for i in range(6):  # 6 أيام
            col_start = i * num_periods  # بداية أعمدة اليوم
            col_end = col_start + (num_periods - 1)  # نهاية أعمدة اليوم
            # جعل كل خلية من الصف العلوي ممتدة على عدد الحصص المطلوب (يوم كامل)
            table_style.add('SPAN', (col_start, 0), (col_end, 0))
            table_style.add('VALIGN', (col_start, 0), (col_end, 0), 'MIDDLE')

        # إضافة تنسيق خاص لقيم "رت" في العمود الأول من كل يوم لكل طالب
        for row_index in range(4, 4 + len(students)):  # صفوف الطلاب تبدأ من الصف الرابع
            for day_index in range(6):  # 6 أيام
                col_index = day_index * num_periods  # العمود الأول من كل يوم
                # تنسيق خاص لإبراز رقم الترتيب في العمود الأول من كل يوم
                table_style.add('FONT', (col_index, row_index), (col_index, row_index), self.arabic_font, 8)  # خط Calibri حجم 8
                table_style.add('ALIGN', (col_index, row_index), (col_index, row_index), 'CENTER')  # توسيط النص
                table_style.add('TEXTCOLOR', (col_index, row_index), (col_index, row_index), colors.black)  # لون أسود
                table_style.add('BACKGROUND', (col_index, row_index), (col_index, row_index), colors.lightgrey)  # خلفية رمادية فاتحة للتمييز

        table.setStyle(table_style)
        elements.append(table)

        # إضافة ملاحظة تحت الجدول
        note_style = ParagraphStyle(
            'NoteStyle',
            parent=styles['Normal'],
            fontName=self.arabic_font,
            fontSize=13,  # حجم 13
            alignment=1,  # توسيط
            leading=16,
            spaceAfter=0,
            spaceBefore=15,  # مسافة قبل النص
            textColor=colors.black,
            fontWeight='bold'
        )

        # إضافة العبارة المطلوبة أسفل الجدول
        note_text = self.arabic_text("المرجو من السادة الأساتذة عدم قبول أي تلميذ(ة) غير مسجل(ة) في اللائحة ولا يتوفر على ورقة السماح بالدخول")
        note_paragraph = Paragraph(note_text, note_style)
        elements.append(note_paragraph)

        # بناء ملف PDF
        doc.build(elements)

        return True

    def generate_multi_class_pdf(self, file_path, class_data_list, institution_data, start_date, report_type, periods=None):
        """
        توليد ملف PDF يحتوي على عدة أقسام، كل قسم في صفحة مستقلة

        Args:
            file_path: مسار حفظ الملف
            class_data_list: قائمة من القواميس، كل قاموس يحتوي على اسم القسم وطلابه
            institution_data: بيانات المؤسسة
            start_date: تاريخ بداية الأسبوع
            report_type: نوع التقرير
            periods: قيم الحصص المستخدمة في التقرير (تم إزالة استخدامها)
        """
        # التأكد من وجود قيم الحصص - فقط 8 قيم
        if periods is None or len(periods) != 8:
            periods = [str(i) for i in range(1, 9)]  # القيم الافتراضية من "1" إلى "8"

        # إنشاء وثيقة PDF
        doc = SimpleDocTemplate(
            file_path,
            pagesize=A3,
            rightMargin=0.2*cm,
            leftMargin=0.2*cm,
            topMargin=0.1*cm,
            bottomMargin=0.1*cm
        )

        # قائمة لعناصر PDF
        elements = []

        # معالجة كل قسم على حدة وإضافته كصفحة منفصلة
        for i, class_data in enumerate(class_data_list):
            class_name = class_data['class_name']
            students = class_data['students']

            # نفس الكود الموجود في generate_pdf ولكن بدون بناء الملف
            # تعريف الأنماط مع استخدام خط Calibri حجم مختلف
            styles = getSampleStyleSheet()

            # تعريف الأنماط المطلوبة فقط

            # نمط اسم القسم (تحت المؤسسة) - Calibri 14 غليظ
            class_style = ParagraphStyle(
                'ClassStyle',
                parent=styles['Normal'],
                fontName=self.arabic_font,
                fontSize=14,  # حجم 14
                alignment=1,  # توسيط
                leading=16,
                rightIndent=0,
                spaceAfter=0,
                spaceBefore=4,  # مسافة قبل النص
                textColor=colors.black,
                fontWeight='bold'
            )

            # إضافة شعار المؤسسة في أعلى الصفحة
            if 'ImagePath1' in institution_data and institution_data['ImagePath1'] and os.path.exists(institution_data['ImagePath1']):
                from reportlab.platypus import Image
                print(f"تم العثور على مسار الشعار: {institution_data['ImagePath1']}")
                try:
                    logo = Image(institution_data['ImagePath1'])
                    logo.drawWidth = 200  # عرض الشعار 200 نقطة
                    logo.drawHeight = 70  # ارتفاع الشعار 70 نقطة
                    logo.hAlign = 'CENTER'  # محاذاة الشعار في وسط الصفحة
                    elements.append(logo)
                    elements.append(Spacer(1, 0.3*cm))
                    print("تم إضافة الشعار بنجاح")
                except Exception as e:
                    print(f"خطأ في تحميل الشعار: {str(e)}")
            else:
                print("لم يتم العثور على مسار الشعار أو الملف غير موجود")
                if 'ImagePath1' in institution_data:
                    print(f"مسار الشعار: {institution_data['ImagePath1']}")
                else:
                    print("مفتاح ImagePath1 غير موجود في بيانات المؤسسة")

            # إنشاء نمط جديد للسنة الدراسية بخط Calibri 17 أزرق غامق
            year_style_blue = ParagraphStyle(
                'YearStyleBlue',
                parent=styles['Normal'],
                fontName=self.arabic_font,
                fontSize=17,  # حجم 17
                alignment=1,  # توسيط
                leading=20,
                rightIndent=0,
                spaceAfter=0,
                spaceBefore=0,
                textColor=colors.navy,  # لون أزرق غامق
                fontWeight='bold'
            )

            # إضافة السنة الدراسية تحت الشعار مباشرة
            year_text = self.arabic_text(f"السنة الدراسية : {institution_data['academic_year']}")
            elements.append(Paragraph(year_text, year_style_blue))
            elements.append(Spacer(1, 0.3*cm))

            # إضافة اسم المؤسسة في الوسط مع حدود بالشكل المطلوب
            # استخدام عمود "المؤسسة" من جدول بيانات_المؤسسة
            institution_name = institution_data.get('institution', 'المؤسسة التعليمية')
            institution_text = self.arabic_text(f"المؤسسة : {institution_name}")

            # إنشاء جدول لعرض المؤسسة
            institution_table = Table(
                [[institution_text]],
                colWidths=[300],
                rowHeights=[30]
            )

            institution_table.setStyle(TableStyle([
                ('FONT', (0, 0), (0, 0), self.arabic_font, 16, 1),
                ('TEXTCOLOR', (0, 0), (0, 0), colors.black),
                ('ALIGN', (0, 0), (0, 0), 'CENTER'),
                ('VALIGN', (0, 0), (0, 0), 'MIDDLE'),
                ('GRID', (0, 0), (0, 0), 2, colors.black, None, (3, 3)),
                ('LEFTPADDING', (0, 0), (0, 0), 5),
                ('RIGHTPADDING', (0, 0), (0, 0), 5),
                ('TOPPADDING', (0, 0), (0, 0), 0),
                ('BOTTOMPADDING', (0, 0), (0, 0), 0),
                ('LEADING', (0, 0), (0, 0), 20),
            ]))

            outer_table = Table(
                [[institution_table]],
                colWidths=[300],
                rowHeights=[30]
            )

            outer_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (0, 0), 'CENTER'),
                ('VALIGN', (0, 0), (0, 0), 'MIDDLE'),
                ('LEFTPADDING', (0, 0), (0, 0), 0),
                ('RIGHTPADDING', (0, 0), (0, 0), 0),
                ('TOPPADDING', (0, 0), (0, 0), 0),
                ('BOTTOMPADDING', (0, 0), (0, 0), 0),
            ]))

            elements.append(Spacer(1, 0.8*cm))
            elements.append(outer_table)

            # إضافة اسم القسم تحت المؤسسة
            class_paragraph = Paragraph(
                self.arabic_text(f"ورقة الغياب لقسم : {class_name}"), class_style
            )
            elements.append(class_paragraph)
            elements.append(Spacer(1, 0.5*cm))

            # إنشاء جدول الحضور - تغيير ترتيب الأيام والتواريخ من اليمين إلى اليسار مع إضافة يوم السبت
            arabic_days = [self.arabic_text("السبت"), self.arabic_text("الجمعة"), self.arabic_text("الخميس"), self.arabic_text("الأربعاء"),
                    self.arabic_text("الثلاثاء"), self.arabic_text("الاثنين")]

            # حساب تواريخ الأسبوع بدءًا من التاريخ المحدد
            week_dates = []
            for j in range(6):
                current_date = start_date + datetime.timedelta(days=5-j)
                formatted_date = current_date.strftime("%Y-%m-%d")
                week_dates.append(self.arabic_text(formatted_date))

            # بيانات الجدول
            data = []

            # صف جديد أعلى الجدول: مقسم حسب الأيام
            new_header_row = []

            # إضافة 6 خلايا منفصلة للأيام الستة
            for day in arabic_days:
                new_header_row.append("")

            # إضافة خلية لكل يوم ممتدة على 8 أعمدة
            for _ in range((len(arabic_days) * 8) - len(arabic_days)):
                new_header_row.append("")  # إضافة الخلايا المتبقية لتغطية 48 عمود

            # إضافة خلية نص الطلاب وخلية رت فارغة
            new_header_row.append(self.arabic_text("تلاميذ يوجهون الى الإدارة"))  # نص فوق الاسم والنسب
            new_header_row.append("")  # خلية فارغة فوق الرت
            data.append(new_header_row)

            # الصف 1: ترويسة معلومات الطالب والأيام
            row1 = []
            # إضافة الأيام من اليمين إلى اليسار
            for day in arabic_days:
                row1.append(day)
                # إضافة خلايا فارغة لإفساح المجال للتواريخ والحصص
                row1.extend([""] * 7)
            # ثم إضافة معلومات الطالب
            row1.append(self.arabic_text("الاسم والنسب"))
            row1.append(self.arabic_text("رت"))
            data.append(row1)

            # الصف 2: التواريخ بدلاً من الفترات
            row2 = []
            for j, date in enumerate(week_dates):
                # إضافة التاريخ مع span على 8 أعمدة
                row2.append(date)  # إضافة التاريخ
                row2.extend([""] * 7)  # امتداد على 8 أعمدة
            row2.append("")
            row2.append("")
            data.append(row2)

            # الصف 3: الحصص - استخدام نفس 8 حصص لكل يوم
            row3 = []
            for day_index in range(len(arabic_days)):
                # نضيف نفس الحصص لكل يوم
                row3.extend(periods)

            row3.append("")  # خلية فارغة للاسم والنسب
            row3.append("")  # خلية فارغة للرت
            data.append(row3)

            # إضافة صفوف الطلاب مع تأكيد ظهور رقم الترتيب في أول خلية من كل يوم
            for student in students:
                student_row = []
                # الحصول على رقم الترتيب الفعلي من جدول اللوائح
                rt_value = student.get("rt", "") or student.get("id", "")
                # إزالة علامة X إذا كانت موجودة لعرض الرقم الأصلي فقط
                if str(rt_value).endswith(" X"):
                    rt_display = str(rt_value)[:-2].strip()
                elif str(rt_value) == "X":
                    rt_display = ""
                else:
                    rt_display = str(rt_value)

                # إضافة خلايا لعلامات الحضور لكل يوم
                for day_index in range(len(arabic_days)):
                    # إضافة رقم الترتيب (رت) في العمود الأول من كل يوم بشكل واضح
                    student_row.append(rt_display)  # رقم الترتيب الفعلي من جدول اللوائح

                    # إضافة 7 خلايا فارغة للباقي
                    for _ in range(7):
                        student_row.append("")

                # ثم إضافة معلومات الطالب في نهاية الصف
                student_row.append(self.arabic_text(student["name"]))  # الاسم والنسب
                student_row.append(rt_display)  # رقم الترتيب الفعلي في العمود الأخير
                data.append(student_row)

            # إضافة صفوف فارغة (حد أقصى 7 صفوف بشرط عدم تجاوز 50 صفاً إجمالياً)
            current_rows = len(data)  # عدد الصفوف الحالية (رؤوس + طلاب)
            max_total_rows = 50
            max_empty_rows = 7

            # حساب عدد الصفوف الفارغة المطلوبة
            empty_rows_needed = min(max_empty_rows, max_total_rows - current_rows - 1)  # -1 لصف توقيع الأساتذة

            if empty_rows_needed > 0:
                for _ in range(empty_rows_needed):
                    empty_row = []
                    # إضافة خلايا فارغة لكل يوم
                    for day_index in range(len(arabic_days)):
                        empty_row.append("")  # خلية فارغة لرقم الترتيب
                        # إضافة 7 خلايا فارغة للحصص
                        for _ in range(7):
                            empty_row.append("")

                    # إضافة خلايا فارغة لمعلومات الطالب
                    empty_row.append("")  # الاسم والنسب
                    empty_row.append("")  # رت
                    data.append(empty_row)

            # إضافة صف جديد أسفل الجدول: توقيع الأساتذة
            footer_row = []
            for _ in range(len(arabic_days) * 8):
                footer_row.append("")  # خلايا فارغة تحت الأيام مقسمة لكل عمود

            footer_row.append(self.arabic_text("توقيع الأساتذة"))  # نص تحت الاسم والنسب
            footer_row.append("")  # خلية فارغة تحت الرت
            data.append(footer_row)

            # تعديل حساب عرض الأعمدة - الجدول يأخذ كامل عرض الصفحة مع هوامش 0.2 سم
            available_width = A3[0] - 0.4*cm  # A3 width minus margins

            # أعمدة الحضور - 6 أيام × 8 حصص = 48 عمود
            attendance_cell_width = (available_width - 6.2*cm) / (len(arabic_days) * 8)
            col_widths = [attendance_cell_width] * (len(arabic_days) * 8)

            # ثم إضافة أعمدة معلومات الطالب
            col_widths.append(4*cm)  # الاسم والنسب
            col_widths.append(1.2*cm)  # رت

            # إنشاء الجدول مع العرض المحدد للأعمدة والارتفاع المحدد للصفوف
            # تعيين ارتفاع الصفوف مع تعديل ارتفاع الصف العلوي والسفلي إلى 70
            row_heights = [70, 15, 15, 15]  # الصف الأول بارتفاع 70، ثم صفوف الرؤوس بارتفاع 15

            # إضافة ارتفاعات صفوف الطلاب
            for _ in range(len(students)):
                row_heights.append(15)  # ارتفاع صفوف الطلاب 15 بكسل

            # إضافة ارتفاعات الصفوف الفارغة
            if empty_rows_needed > 0:
                for _ in range(empty_rows_needed):
                    row_heights.append(15)  # ارتفاع الصفوف الفارغة 15 بكسل

            # إضافة ارتفاع الصف السفلي
            row_heights.append(70)  # الصف الأخير بارتفاع 70

            # إنشاء الجدول مع العرض والارتفاع المحددين
            table = Table(data, colWidths=col_widths, rowHeights=row_heights)

            # تنسيق الجدول
            table_style = TableStyle([
                # تنسيق أساسي
                ('FONT', (0, 0), (-1, -1), self.arabic_font, 12),  # حجم الخط 12 للجدول
                ('GRID', (0, 0), (-1, -1), 1, colors.black),  # سماكة خط 1 نقطة
                ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),

                # تحسين المحاذاة العمودية للجميع
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),

                # تنسيق الصف العلوي (تلاميذ يوجهون الى الإدارة)
                ('ALIGN', (-2, 0), (-2, 0), 'CENTER'),
                ('VALIGN', (-2, 0), (-2, 0), 'MIDDLE'),
                ('FONT', (-2, 0), (-2, 0), self.arabic_font, 12, 1),  # خط غليظ للنص

                # تنسيق الصف السفلي (توقيع الأساتذة)
                ('ALIGN', (-2, -1), (-2, -1), 'CENTER'),
                ('VALIGN', (-2, -1), (-2, -1), 'MIDDLE'),
                ('VALIGN', (0, -1), (-3, -1), 'MIDDLE'),  # محاذاة عمودية لوسط الخلايا الفارغة
                ('FONT', (-2, -1), (-2, -1), self.arabic_font, 12, 1),  # خط غليظ للنص

                # التنسيقات الأخرى
                ('LEFTPADDING', (0, 1), (-1, 3), 2),
                ('RIGHTPADDING', (0, 1), (-1, 3), 2),
                ('TOPPADDING', (0, 1), (-1, 3), 0),
                ('BOTTOMPADDING', (0, 1), (-1, 3), 2),

                ('LEFTPADDING', (0, 4), (-1, -2), 3),
                ('RIGHTPADDING', (0, 4), (-1, -2), 3),
                ('TOPPADDING', (0, 4), (-1, -2), 1),
                ('BOTTOMPADDING', (0, 4), (-1, -2), 3),

                ('ALIGN', (0, 1), (-1, -2), 'CENTER'),

                # تنسيق رؤوس الأعمدة
                ('BACKGROUND', (0, 1), (-1, 3), colors.lightgrey),
                ('FONTSIZE', (0, 1), (-1, 3), 14),
                ('FONTNAME', (0, 1), (-1, 3), self.arabic_font),
                ('FONT', (0, 1), (-1, 3), self.arabic_font, 12, 1),

                # تطبيق محاذاة عمودية خاصة لرؤوس الأعمدة
                ('VALIGN', (0, 1), (-1, 5), 'TOP'),

                # تنسيق أعمدة رت والاسم والنسب
                ('SPAN', (-1, 1), (-1, 3)),  # رت
                ('SPAN', (-2, 1), (-2, 3)),  # الاسم والنسب
            ])

            # إضافة امتداد (SPAN) للأيام والتواريخ
            for j in range(6):  # 6 أيام
                col_start = j * 8
                col_end = col_start + 7
                table_style.add('SPAN', (col_start, 1), (col_end, 1))  # span للأيام
                table_style.add('SPAN', (col_start, 2), (col_end, 2))  # span للتواريخ

            # تنسيق خاص للتواريخ
            for j in range(6):
                col_start = j * 8
                col_end = col_start + 7
                table_style.add('TOPPADDING', (col_start, 2), (col_end, 2), 1)
                table_style.add('BOTTOMPADDING', (col_start, 2), (col_end, 2), 0)
                table_style.add('VALIGN', (col_start, 2), (col_end, 2), 'TOP')

            # تنسيق إضافي للحصص - تطبيق على كل خلية حصة بشكل فردي
            for j in range(6):
                for k in range(8):
                    cell_col = j * 8 + k
                    table_style.add('VALIGN', (cell_col, 3), (cell_col, 3), 'TOP')
                    table_style.add('TOPPADDING', (cell_col, 3), (cell_col, 3), 0)
                    table_style.add('BOTTOMPADDING', (cell_col, 3), (cell_col, 3), 0)

            # إضافة امتداد (SPAN) لخلايا الصف العلوي لكل يوم على حدة
            for j in range(6):  # 6 أيام
                col_start = j * 8  # بداية أعمدة اليوم
                col_end = col_start + 7  # نهاية أعمدة اليوم
                # جعل كل خلية من الصف العلوي ممتدة على 8 أعمدة (يوم كامل)
                table_style.add('SPAN', (col_start, 0), (col_end, 0))
                table_style.add('VALIGN', (col_start, 0), (col_end, 0), 'MIDDLE')

            # إضافة تنسيق خاص لقيم "رت" في العمود الأول من كل يوم لكل طالب
            for row_index in range(4, 4 + len(students)):  # صفوف الطلاب تبدأ من الصف الرابع
                for day_index in range(6):  # 6 أيام
                    col_index = day_index * 8  # العمود الأول من كل يوم
                    # تنسيق خاص لإبراز رقم الترتيب في العمود الأول من كل يوم
                    table_style.add('FONT', (col_index, row_index), (col_index, row_index), self.arabic_font, 8)  # خط Calibri حجم 8
                    table_style.add('ALIGN', (col_index, row_index), (col_index, row_index), 'CENTER')  # توسيط النص
                    table_style.add('TEXTCOLOR', (col_index, row_index), (col_index, row_index), colors.black)  # لون أسود
                    table_style.add('BACKGROUND', (col_index, row_index), (col_index, row_index), colors.lightgrey)  # خلفية رمادية فاتحة للتمييز

            table.setStyle(table_style)
            elements.append(table)

            # إضافة ملاحظة تحت الجدول
            note_style = ParagraphStyle(
                'NoteStyle',
                parent=styles['Normal'],
                fontName=self.arabic_font,
                fontSize=13,  # حجم 13
                alignment=1,  # توسيط
                leading=16,
                spaceAfter=0,
                spaceBefore=15,  # مسافة قبل النص
                textColor=colors.black,
                fontWeight='bold'
            )

            # إضافة العبارة المطلوبة أسفل الجدول
            note_text = self.arabic_text("المرجو من السادة الأساتذة عدم قبول أي تلميذ(ة) غير مسجل(ة) في اللائحة ولا يتوفر على ورقة السماح بالدخول")
            note_paragraph = Paragraph(note_text, note_style)
            elements.append(note_paragraph)

            # إضافة فاصل صفحات إلا إذا كان هذا القسم الأخير
            if i < len(class_data_list) - 1:
                elements.append(PageBreak())

        # بناء ملف PDF
        doc.build(elements)

        return True

    def generate_attendance_report(self, file_path, students, institution_data, class_name, start_date, report_type, periods=None):
        """دالة مساعدة للتوافق مع استدعاءات sub20_window.py"""
        return self.generate_pdf(file_path, students, class_name, institution_data, start_date, report_type, periods)
