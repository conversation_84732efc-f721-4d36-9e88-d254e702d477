import sys
import os
import sqlite3
import traceback
import shutil
from datetime import datetime
from database_config import get_database_path, get_database_connection
from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QFrame,
    QPushButton, QLabel, QMessageBox, QApplication,
    QComboBox, QDateEdit, QGroupBox,
    QTableWidget, QTableWidgetItem, QHeaderView, QAbstractItemView, QCheckBox,
    QDialog, QTextEdit, QRadioButton, QLineEdit, QProgressDialog, QStyle
)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont, QIcon, QPixmap, QIntValidator

# استيراد المكتبات اللازمة لإنشاء ملفات PDF
try:
    from reportlab.lib.pagesizes import A4
    from reportlab.pdfgen import canvas
    from reportlab.pdfbase.ttfonts import TTFont
    from reportlab.pdfbase import pdfmetrics
    from reportlab.lib.units import cm
    from reportlab.lib import colors
    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle

    # استيراد دعم العربية
    import arabic_reshaper
    from bidi.algorithm import get_display

    # تسجيل الخطوط العربية
    REPORTLAB_AVAILABLE = True

    # محاولة تسجيل خط عربي
    try:
        # البحث عن ملفات الخطوط في مجلد الخطوط
        fonts_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "fonts")

        # إذا لم يكن مجلد الخطوط موجودًا، قم بإنشائه
        if not os.path.exists(fonts_dir):
            os.makedirs(fonts_dir)

        # قائمة بالخطوط العربية المحتملة
        arabic_fonts = [
            "Arial.ttf",
            "arialbd.ttf",
            "Calibri.ttf",
            "calibrib.ttf",
            "Tahoma.ttf",
            "tahomabd.ttf",
            "Amiri-Regular.ttf",
            "Amiri-Bold.ttf",
            "Amiri-BoldItalic.ttf",
            "Amiri-Italic.ttf"
        ]

        # البحث عن الخطوط في مجلد النظام
        system_fonts_dir = os.path.join(os.environ.get('WINDIR', ''), 'Fonts')

        # تسجيل أول خط عربي متاح
        arabic_font_registered = False

        for font_name in arabic_fonts:
            # البحث في مجلد الخطوط المحلي
            local_font_path = os.path.join(fonts_dir, font_name)

            # البحث في مجلد خطوط النظام
            system_font_path = os.path.join(system_fonts_dir, font_name)

            if os.path.exists(local_font_path):
                # تسجيل الخط من المجلد المحلي
                pdfmetrics.registerFont(TTFont('Arabic', local_font_path))
                arabic_font_registered = True
                print(f"تم تسجيل الخط العربي: {font_name} من المجلد المحلي")
                break
            elif os.path.exists(system_font_path):
                # نسخ الخط من مجلد النظام إلى المجلد المحلي
                import shutil
                shutil.copy2(system_font_path, local_font_path)

                # تسجيل الخط
                pdfmetrics.registerFont(TTFont('Arabic', local_font_path))
                arabic_font_registered = True
                print(f"تم تسجيل الخط العربي: {font_name} من مجلد النظام")
                break

        # إذا لم يتم تسجيل أي خط عربي، استخدم الخط الافتراضي
        if not arabic_font_registered:
            print("لم يتم العثور على خط عربي، سيتم استخدام الخط الافتراضي")
    except Exception as e:
        print(f"خطأ في تسجيل الخط العربي: {e}")

except ImportError as e:
    print(f"خطأ في استيراد مكتبات إنشاء التقارير: {e}")
    REPORTLAB_AVAILABLE = False

def reshape_ar(text):
    """إعادة تشكيل النص العربي للعرض الصحيح في PDF"""
    try:
        # تحويل النص إلى سلسلة نصية إذا لم يكن كذلك
        text = str(text)
        # إعادة تشكيل النص العربي
        reshaped_text = arabic_reshaper.reshape(text)
        # تطبيق خوارزمية BIDI لعرض النص من اليمين إلى اليسار
        bidi_text = get_display(reshaped_text)
        return bidi_text
    except Exception as e:
        print(f"خطأ في إعادة تشكيل النص العربي: {e}")
        return text



class CreateAbsenceTableWindow(QMainWindow):
    """نافذة مسك أوراق الفروض"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("مسك أوراق الفروض")
        self.setMinimumSize(1000, 700)
        self.setObjectName("CreateAbsenceTableWindow")

        # تعيين نمط النافذة
        self.setStyleSheet("""
            QMainWindow { background-color: #F0F2F5; }
            QPushButton {
                background-color: #1565c0;
                color: white;
                border-radius: 5px;
                padding: 8px 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0D47A1;
            }
            QLabel {
                color: #333;
                font-size: 16px;
            }
            QComboBox, QDateEdit, QLineEdit, QTextEdit {
                border: 1px solid #1565c0;
                border-radius: 4px;
                padding: 5px;
                background-color: white;
                min-height: 30px;
                font-size: 16px;
            }
            QRadioButton {
                color: #333;
                font-size: 16px;
                font-weight: bold;
            }
            QRadioButton::indicator {
                width: 20px;
                height: 20px;
            }
            QRadioButton::indicator:checked {
                background-color: #1565c0;
                border: 2px solid #1565c0;
            }
            QGroupBox {
                font-weight: bold;
                border: 1px solid #1565c0;
                border-radius: 5px;
                margin-top: 15px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 5px;
                color: #1565c0;
                font-size: 16px;
            }
        """)

        # إنشاء الخطوط
        self.font_calibri_14_bold = QFont("Calibri", 16, QFont.Bold)
        self.font_calibri_15_bold_blue = QFont("Calibri", 16, QFont.Bold)
        self.font_calibri_12_normal = QFont("Calibri", 16)
        self.font_calibri_13_bold = QFont("Calibri", 16, QFont.Bold)

        # إنشاء الإطار الرئيسي
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)

        # قائمة لتخزين مربعات اختيار الأقسام
        self.section_checkboxes = []

        # قائمة لتخزين مربعات اختيار الفروض
        self.exam_checkboxes = []

        # إنشاء عناصر واجهة المستخدم

        # تم حذف تاريخ المسك بناءً على طلب المستخدم

        # إنشاء مجموعة المادة والأستاذ
        subject_teacher_group = QGroupBox("المادة والأستاذ", self.central_widget)
        subject_teacher_group.setFont(QFont("Calibri", 16, QFont.Bold))
        subject_teacher_group.setStyleSheet("QGroupBox { color: #1565c0; border: 2px solid #1565c0; border-radius: 5px; margin-top: 15px; padding-top: 15px; }")
        subject_teacher_group.setGeometry(550, 50, 300, 120)

        # المادة الدراسية
        subject_label = QLabel("المادة الدراسية:", subject_teacher_group)
        subject_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        subject_label.setFont(QFont("Calibri", 16, QFont.Bold))
        subject_label.setStyleSheet("color: #1565c0; border: 2px solid #1565c0; padding: 2px;")
        subject_label.setGeometry(180, 30, 100, 30)

        self.subject_combo = QComboBox(subject_teacher_group)
        self.subject_combo.setFont(QFont("Calibri", 16, QFont.Bold))
        self.subject_combo.setStyleSheet("border: 2px solid #1565c0; padding: 2px;")
        self.subject_combo.setGeometry(20, 30, 150, 30)
        # اسم الأستاذ
        teacher_label = QLabel("اسم الأستاذ(ة):", subject_teacher_group)
        teacher_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        teacher_label.setFont(QFont("Calibri", 14, QFont.Bold))
        teacher_label.setStyleSheet("color: #1565c0; border: 2px solid #1565c0; padding: 2px;")
        teacher_label.setGeometry(180, 70, 100, 30)

        self.teacher_combo = QComboBox(subject_teacher_group)
        self.teacher_combo.setFont(QFont("Calibri", 14, QFont.Bold))
        self.teacher_combo.setStyleSheet("border: 2px solid #1565c0; padding: 2px;")
        self.teacher_combo.setGeometry(20, 70, 150, 30)

        # قسم نماذج ورقة التنقيط
        template_group = QGroupBox("نماذج ورقة التنقيط", self.central_widget)
        template_group.setFont(QFont("Calibri", 16, QFont.Bold))
        template_group.setStyleSheet("QGroupBox { color: #1565c0; border: 2px solid #1565c0; border-radius: 5px; margin-top: 15px; padding-top: 15px; }")
        template_group.setGeometry(550, 180, 300, 300)  # زيادة ارتفاع المجموعة أكثر لاستيعاب مربعات النص الجديدة

        # إنشاء خيارات النماذج
        options = [
            "1 - نموذج فرضان وأنشطة",
            "2 - نموذج ثلاث فروض وأنشطة",
            "3 - نموذج أربع فرض وأنشطة",
            "4 - نموذج فرضان من غير أنشطة",
            "5 - نموذج ثلاث فرض من غير أنشطة"
        ]

        # إنشاء زر اختيار لكل خيار
        self.template_radio_buttons = []
        for i, option in enumerate(options):
            radio = QRadioButton(option, template_group)
            radio.setFont(QFont("Calibri", 14, QFont.Bold))
            radio.setGeometry(20, 30 + i*30, 260, 30)
            if i == 0:  # تحديد الخيار الأول افتراضيًا
                radio.setChecked(True)
            self.template_radio_buttons.append(radio)

        # إضافة مربعات نص للتحكم في ارتفاع صفوف الجدول
        row_height_label = QLabel("ارتفاع صفوف الجدول:", template_group)
        row_height_label.setFont(QFont("Calibri", 14, QFont.Bold))
        row_height_label.setStyleSheet("color: #1565c0; font-weight: bold;")
        row_height_label.setGeometry(20, 190, 260, 25)

        # مربع نص لارتفاع رؤوس الأعمدة
        header_height_label = QLabel("ارتفاع العناوين:", template_group)
        header_height_label.setFont(QFont("Calibri", 14, QFont.Bold))
        header_height_label.setStyleSheet("color: #1565c0; border: 1px solid #1565c0; padding: 2px;")
        header_height_label.setGeometry(20, 220, 120, 30)

        self.header_height_input = QLineEdit(template_group)
        self.header_height_input.setFont(QFont("Calibri", 14, QFont.Bold))
        self.header_height_input.setStyleSheet("border: 1px solid #1565c0; padding: 2px;")
        self.header_height_input.setGeometry(150, 220, 50, 30)
        self.header_height_input.setText("16")  # القيمة الافتراضية
        self.header_height_input.setAlignment(Qt.AlignCenter)
        # التحقق من أن المدخلات أرقام فقط
        self.header_height_input.setValidator(QIntValidator(10, 50))

        # مربع نص لارتفاع باقي الصفوف
        body_height_label = QLabel("ارتفاع الصفوف:", template_group)
        body_height_label.setFont(QFont("Calibri", 14, QFont.Bold))
        body_height_label.setStyleSheet("color: #1565c0; border: 1px solid #1565c0; padding: 2px;")
        body_height_label.setGeometry(20, 260, 120, 30)

        self.body_height_input = QLineEdit(template_group)
        self.body_height_input.setFont(QFont("Calibri", 14, QFont.Bold))
        self.body_height_input.setStyleSheet("border: 1px solid #1565c0; padding: 2px;")
        self.body_height_input.setGeometry(150, 260, 50, 30)
        self.body_height_input.setText("14")  # القيمة الافتراضية
        self.body_height_input.setAlignment(Qt.AlignCenter)
        # التحقق من أن المدخلات أرقام فقط
        self.body_height_input.setValidator(QIntValidator(10, 50))

        # المستويات والأقسام
        levels_group = QGroupBox("المستويات والأقسام", self.central_widget)
        levels_group.setFont(QFont("Calibri", 16, QFont.Bold))
        levels_group.setStyleSheet("QGroupBox { color: #1565c0; border: 2px solid #1565c0; border-radius: 5px; margin-top: 15px; padding-top: 15px; }")
        levels_group.setGeometry(50, 50, 470, 500)

        # عنوان توضيحي
        instruction_label = QLabel("الرجاء قم بتحديد المستوى ثم اختر الأقسام المطلوبة", levels_group)
        instruction_label.setFont(QFont("Calibri", 14, QFont.Bold))
        instruction_label.setAlignment(Qt.AlignCenter)
        instruction_label.setStyleSheet("color: #1565c0;")
        instruction_label.setGeometry(50, 30, 350, 30)

        # المستوى الدراسي
        levels_label = QLabel("المستوى الدراسي:", levels_group)
        levels_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        levels_label.setFont(QFont("Calibri", 14, QFont.Bold))
        levels_label.setStyleSheet("color: #1565c0; border: 2px solid #1565c0; padding: 2px;")
        levels_label.setGeometry(350, 70, 110, 35)

        self.levels_combo = QComboBox(levels_group)
        self.levels_combo.setFont(QFont("Calibri", 14, QFont.Bold))
        self.levels_combo.setStyleSheet("border: 2px solid #1565c0; padding: 2px;")
        self.levels_combo.setGeometry(50, 70, 300, 30)
        self.levels_combo.currentTextChanged.connect(self.update_sections_table_from_combo)

        # جدول الأقسام
        sections_group = QGroupBox("الأقسام", levels_group)
        sections_group.setFont(QFont("Calibri", 16, QFont.Bold))
        sections_group.setStyleSheet("QGroupBox { color: #1565c0; border: 2px solid #1565c0; border-radius: 5px; margin-top: 15px; padding-top: 15px; }")
        sections_group.setGeometry(20, 110, 410, 350)

        # زر إلغاء التحديد
        self.clear_sections_button = QPushButton("إلغاء التحديد", sections_group)
        self.clear_sections_button.setFont(QFont("Calibri", 14, QFont.Bold))
        self.clear_sections_button.setGeometry(10, 30, 120, 30)
        self.clear_sections_button.setStyleSheet("""
            background-color: #1565c0;
            color: white;
            padding: 5px;
            border-radius: 5px;
        """)
        self.clear_sections_button.setCursor(Qt.PointingHandCursor)
        self.clear_sections_button.clicked.connect(self.clear_sections_selection)

        self.sections_table = QTableWidget(sections_group)
        self.sections_table.setColumnCount(2)
        self.sections_table.setHorizontalHeaderLabels(["تحديد", "القسم"])
        self.sections_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.sections_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.sections_table.verticalHeader().setVisible(False)
        self.sections_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.sections_table.setSelectionMode(QAbstractItemView.NoSelection)
        self.sections_table.setGeometry(10, 70, 390, 260)
        self.sections_table.setFont(QFont("Calibri", 14, QFont.Bold))
        self.sections_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #1565c0;
                border-radius: 5px;
            }
            QHeaderView::section {
                background-color: #1565c0;
                color: white;
                padding: 5px;
                border: 1px solid #1565c0;
                font-weight: bold;
                font-size: 16px;
            }
        """)

        # إضافة الأزرار

        # تم حذف جميع الأزرار بناءً على طلب المستخدم

        # زر ورقة التنقيط
        self.scoring_sheet_button = QPushButton("ورقة التنقيط", self.central_widget)
        self.scoring_sheet_button.setFont(QFont("Calibri", 16, QFont.Bold))
        self.scoring_sheet_button.setGeometry(550, 490, 300, 40)  # تعديل موضع الزر ليكون أسفل مربعات النص
        self.scoring_sheet_button.setStyleSheet("""
            background-color: #1565c0;
            color: white;
            padding: 5px;
            border-radius: 5px;
        """)
        # تحويل مؤشر الماوس إلى يد عند المرور على الزر
        self.scoring_sheet_button.setCursor(Qt.PointingHandCursor)
        self.scoring_sheet_button.clicked.connect(self.show_scoring_sheet)

        # التأكد من وجود الجدول
        self.ensure_table_exists()

        # تحميل البيانات
        self.load_data()

        # ربط الإشارات
        self.connect_signals()

    def ensure_table_exists(self):
        """التأكد من وجود جدول مسك_أوراق_الفروض في قاعدة البيانات"""
        try:
            # الاتصال بقاعدة البيانات
            conn = sqlite3.connect(get_database_path())
            cursor = conn.cursor()

            # إنشاء جدول مسك_أوراق_الفروض إذا لم يكن موجودًا
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS مسك_أوراق_الفروض (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    التاريخ TEXT,
                    المادة TEXT,
                    الأستاذ TEXT,
                    المستوى TEXT,
                    القسم TEXT,
                    نوع_الفرض TEXT,
                    المتغيبون TEXT,
                    تاريخ_التسجيل TEXT,
                    السنة_الدراسية TEXT,
                    الأسدس TEXT
                )
            """)

            # حفظ التغييرات وإغلاق الاتصال
            conn.commit()
            conn.close()

        except Exception as e:
            # عرض رسالة خطأ
            error_message = f"حدث خطأ أثناء التحقق من وجود الجدول:\n{str(e)}"
            QMessageBox.critical(
                self,
                "خطأ",
                error_message,
                QMessageBox.Ok
            )
            traceback.print_exc()

    def load_data(self):
        """تحميل البيانات من قاعدة البيانات"""
        try:
            # الاتصال بقاعدة البيانات
            conn = sqlite3.connect(get_database_path())
            cursor = conn.cursor()

            # تحميل المواد والأساتذة من جدول الأساتذة
            cursor.execute("""
                SELECT DISTINCT المادة FROM الأساتذة
                WHERE المادة IS NOT NULL AND المادة != ''
                ORDER BY المادة
            """)
            subjects = [row[0] for row in cursor.fetchall()]
            self.subject_combo.clear()
            # إضافة عنصر فارغ في بداية القائمة
            self.subject_combo.addItem("")
            self.subject_combo.addItems(subjects)
            # تعيين العنصر الفارغ كعنصر افتراضي
            self.subject_combo.setCurrentIndex(0)

            # تحميل المستويات من جدول البنية_التربوية
            cursor.execute("""
                SELECT DISTINCT المستوى FROM البنية_التربوية
                WHERE المستوى IS NOT NULL AND المستوى != ''
                ORDER BY ترتيب_المستويات, المستوى
            """)
            levels = [row[0] for row in cursor.fetchall()]

            # تحميل المستويات في مربع التحرير والسرد
            self.levels_combo.clear()
            # إضافة عنصر فارغ في بداية القائمة
            self.levels_combo.addItem("")
            self.levels_combo.addItems(levels)
            # تعيين العنصر الفارغ كعنصر افتراضي
            self.levels_combo.setCurrentIndex(0)

            # تحميل السنة الدراسية والأسدس من جدول بيانات_المؤسسة
            cursor.execute("""
                SELECT السنة_الدراسية, الأسدس FROM بيانات_المؤسسة
                LIMIT 1
            """)
            result = cursor.fetchone()
            if result:
                self.current_year = result[0]
                self.current_semester = result[1]
            else:
                self.current_year = ""
                self.current_semester = ""

            # إغلاق الاتصال
            conn.close()



        except Exception as e:
            # عرض رسالة خطأ
            error_message = f"حدث خطأ أثناء تحميل البيانات:\n{str(e)}"
            QMessageBox.critical(
                self,
                "خطأ",
                error_message,
                QMessageBox.Ok
            )
            traceback.print_exc()

    def connect_signals(self):
        """ربط إشارات العناصر"""
        # ربط تغيير المادة بتحديث قائمة الأساتذة
        self.subject_combo.currentTextChanged.connect(self.update_teachers)

    def update_teachers(self, subject):
        """تحديث قائمة الأساتذة بناءً على المادة المختارة"""
        try:
            # إذا كانت المادة فارغة، قم بتفريغ قائمة الأساتذة
            if not subject:
                self.teacher_combo.clear()
                self.teacher_combo.addItem("")
                return

            # الاتصال بقاعدة البيانات
            conn = sqlite3.connect(get_database_path())
            cursor = conn.cursor()

            # تحميل الأساتذة للمادة المحددة
            cursor.execute("""
                SELECT اسم_الأستاذ FROM الأساتذة
                WHERE المادة = ? AND اسم_الأستاذ IS NOT NULL AND اسم_الأستاذ != ''
                ORDER BY اسم_الأستاذ
            """, (subject,))
            teachers = [row[0] for row in cursor.fetchall()]

            # تحديث قائمة الأساتذة
            self.teacher_combo.clear()
            # إضافة عنصر فارغ في بداية القائمة
            self.teacher_combo.addItem("")
            self.teacher_combo.addItems(teachers)
            # تعيين العنصر الفارغ كعنصر افتراضي
            self.teacher_combo.setCurrentIndex(0)

            # إغلاق الاتصال
            conn.close()

        except Exception as e:
            print(f"خطأ في تحديث قائمة الأساتذة: {e}")

    # تم حذف دالة update_sections واستبدالها بـ update_sections_table

    def update_sections_table_from_combo(self, level):
        """تحديث جدول الأقسام بناءً على المستوى المحدد من مربع التحرير والسرد"""
        try:
            # إذا كان المستوى فارغًا، قم بتفريغ جدول الأقسام
            if not level:
                self.sections_table.setRowCount(0)
                self.section_checkboxes = []
                return

            # الاتصال بقاعدة البيانات
            conn = sqlite3.connect(get_database_path())
            cursor = conn.cursor()

            # تحميل الأقسام للمستوى المحدد
            cursor.execute("""
                SELECT DISTINCT القسم FROM البنية_التربوية
                WHERE المستوى = ? AND القسم IS NOT NULL AND القسم != ''
            """, (level,))
            sections = [row[0] for row in cursor.fetchall()]

            # ترتيب الأقسام حسب رقم القسم
            def extract_section_number(section):
                # استخراج رقم القسم من النص (مثال: "1/1" أو "2/3" إلخ)
                import re
                match = re.search(r'(\d+)[/\-]?(\d*)', section)
                if match:
                    # إذا كان القسم بصيغة "رقم/رقم"
                    if match.group(2):
                        return int(match.group(1)) * 100 + int(match.group(2))
                    # إذا كان القسم برقم فقط
                    return int(match.group(1)) * 100
                # إذا لم يتم العثور على رقم، نعيد قيمة كبيرة لوضع القسم في النهاية
                return 9999

            # ترتيب الأقسام حسب الرقم
            sections.sort(key=extract_section_number)

            # تحديث جدول الأقسام
            self.sections_table.setRowCount(len(sections))
            self.section_checkboxes = []

            for i, section in enumerate(sections):
                # إنشاء مربع اختيار
                checkbox_widget = QWidget()
                checkbox = QCheckBox(checkbox_widget)
                checkbox.setObjectName(f"section_checkbox_{i}")
                checkbox.setGeometry(10, 10, 25, 25)
                checkbox.setCursor(Qt.PointingHandCursor)
                checkbox.setStyleSheet("QCheckBox { cursor: pointer; }")
                self.section_checkboxes.append(checkbox)

                self.sections_table.setCellWidget(i, 0, checkbox_widget)

                # إضافة اسم القسم
                item = QTableWidgetItem(section)
                item.setFont(QFont("Calibri", 16, QFont.Bold))
                self.sections_table.setItem(i, 1, item)

            # ضبط ارتفاع الصفوف
            for i in range(len(sections)):
                self.sections_table.setRowHeight(i, 40)

            # إغلاق الاتصال
            conn.close()

        except Exception as e:
            print(f"خطأ في تحديث جدول الأقسام: {e}")

    def update_sections_table(self, item=None):
        """تم استبدالها بدالة update_sections_table_from_combo"""
        if item and hasattr(item, 'text'):
            self.update_sections_table_from_combo(item.text())

    def save_all_exams(self):
        """حفظ جميع الفروض في قاعدة البيانات"""
        try:
            # التحقق من إدخال البيانات الإلزامية
            if not self.subject_combo.currentText():
                QMessageBox.warning(self, "تنبيه", "يرجى اختيار المادة")
                return

            if not self.teacher_combo.currentText():
                QMessageBox.warning(self, "تنبيه", "يرجى اختيار الأستاذ")
                return

            # التحقق من اختيار قسم واحد على الأقل
            selected_sections = []
            for i, checkbox in enumerate(self.section_checkboxes):
                if checkbox.isChecked():
                    section_item = self.sections_table.item(i, 1)
                    if section_item:
                        selected_sections.append(section_item.text())

            if not selected_sections:
                QMessageBox.warning(self, "تنبيه", "يرجى اختيار قسم واحد على الأقل")
                return

            # تم حذف التحقق من اختيار نوع فرض بناءً على طلب المستخدم
            # نستخدم قيمة افتراضية لنوع الفرض
            selected_exam_types = ["الفرض 1"]

            # الحصول على البيانات المشتركة من النموذج
            # استخدام تاريخ اليوم بدلاً من تاريخ المسك المحذوف
            date = QDate.currentDate().toString("yyyy-MM-dd")
            subject = self.subject_combo.currentText()
            teacher = self.teacher_combo.currentText()
            level = ""  # سيتم تحديده من الأقسام المحددة
            absent_students = ""  # تم إزالة حقل المتغيبين

            # الاتصال بقاعدة البيانات
            conn = sqlite3.connect(get_database_path())
            cursor = conn.cursor()

            # عداد للفروض المضافة
            exams_added = 0

            # إدخال البيانات في جدول مسك_أوراق_الفروض
            # لكل قسم ولكل نوع فرض، إنشاء سجل منفصل
            for section in selected_sections:
                # الحصول على المستوى من القسم
                cursor.execute("""
                    SELECT المستوى FROM البنية_التربوية
                    WHERE القسم = ? LIMIT 1
                """, (section,))
                level_result = cursor.fetchone()
                if level_result:
                    level = level_result[0]

                for exam_type in selected_exam_types:
                    # التحقق مما إذا كان هناك سجل موجود بالفعل لنفس الأستاذ والقسم ونوع الفرض في نفس الأسدس
                    cursor.execute("""
                        SELECT id FROM مسك_أوراق_الفروض
                        WHERE الأستاذ = ? AND القسم = ? AND نوع_الفرض = ? AND الأسدس = ? AND السنة_الدراسية = ?
                    """, (teacher, section, exam_type, self.current_semester, self.current_year))

                    existing_record = cursor.fetchone()

                    if existing_record:
                        # تحديث السجل الموجود
                        cursor.execute("""
                            UPDATE مسك_أوراق_الفروض
                            SET التاريخ = ?, المادة = ?, المستوى = ?, المتغيبون = ?, تاريخ_التسجيل = ?
                            WHERE id = ?
                        """, (
                            date, subject, level, absent_students,
                            datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                            existing_record[0]
                        ))
                        exams_added += 1
                    else:
                        # إضافة سجل جديد
                        cursor.execute("""
                            INSERT INTO مسك_أوراق_الفروض (
                                التاريخ, المادة, الأستاذ, المستوى, القسم, نوع_الفرض, المتغيبون,
                                تاريخ_التسجيل, السنة_الدراسية, الأسدس
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """, (
                            date, subject, teacher, level, section, exam_type, absent_students,
                            datetime.now().strftime("%Y-%m-%d %H:%M:%S"), self.current_year, self.current_semester
                        ))
                        exams_added += 1

            # حفظ التغييرات وإغلاق الاتصال
            conn.commit()
            conn.close()

            # عرض رسالة نجاح
            QMessageBox.information(
                self,
                "نجاح",
                f"تم حفظ {exams_added} فرض بنجاح!",
                QMessageBox.Ok
            )

            # مسح النموذج
            self.clear_form()

        except Exception as e:
            # عرض رسالة خطأ
            error_message = f"حدث خطأ أثناء حفظ البيانات:\n{str(e)}"
            QMessageBox.critical(
                self,
                "خطأ",
                error_message,
                QMessageBox.Ok
            )
            traceback.print_exc()

    def add_exam_to_table(self):
        """تم استبدالها بدالة save_all_exams"""
        pass

    def clear_form(self):
        """مسح النموذج"""
        self.subject_combo.setCurrentIndex(0 if self.subject_combo.count() > 0 else -1)
        self.teacher_combo.setCurrentIndex(0 if self.teacher_combo.count() > 0 else -1)

        # تحديد الخيار الأول في نماذج ورقة التنقيط
        if self.template_radio_buttons and len(self.template_radio_buttons) > 0:
            self.template_radio_buttons[0].setChecked(True)

        # إلغاء تحديد جميع مربعات اختيار
        self.clear_all_checkboxes()

    def clear_all_checkboxes(self):
        """إزالة جميع علامات التحديد من مربعات اختيار الفروض فقط"""
        # إلغاء تحديد جميع مربعات اختيار الفروض
        for checkbox in self.exam_checkboxes:
            checkbox.setChecked(False)

        # لا نقوم بإزالة التحديد من الأقسام لأنها مطلوبة للعمل

    def clear_sections_selection(self):
        """إزالة جميع علامات التحديد من مربعات اختيار الأقسام"""
        # إلغاء تحديد جميع مربعات اختيار الأقسام
        for checkbox in self.section_checkboxes:
            checkbox.setChecked(False)

        # عرض رسالة تأكيد
        self.show_custom_message("تم", "تم إلغاء تحديد جميع الأقسام")

    def get_template_selection(self):
        """الحصول على اختيار نموذج الفرض من النافذة الرئيسية وإعدادات ارتفاع الصفوف"""
        template = 1
        for i, radio in enumerate(self.template_radio_buttons):
            if radio.isChecked():
                template = i + 1
                break

        # تم إزالة حقل الملاحظات، لذلك نستخدم قيمة فارغة
        note = ""

        # الحصول على قيم ارتفاع الصفوف
        try:
            header_height = int(self.header_height_input.text())
            if header_height < 10 or header_height > 50:
                header_height = 16  # القيمة الافتراضية إذا كانت القيمة خارج النطاق
        except (ValueError, AttributeError):
            header_height = 16  # القيمة الافتراضية في حالة حدوث خطأ

        try:
            body_height = int(self.body_height_input.text())
            if body_height < 10 or body_height > 50:
                body_height = 14  # القيمة الافتراضية إذا كانت القيمة خارج النطاق
        except (ValueError, AttributeError):
            body_height = 14  # القيمة الافتراضية في حالة حدوث خطأ

        return {
            "template": template,
            "note": note,
            "header_height": header_height,
            "body_height": body_height
        }

    def show_custom_message(self, title, message):
        """عرض رسالة مخصصة بنمط مميز"""
        message_dialog = QDialog(self)
        message_dialog.setWindowTitle(title)
        message_dialog.setFixedSize(450, 250)
        message_dialog.setLayoutDirection(Qt.RightToLeft)

        # إضافة أيقونة البرنامج
        try:
            app_icon = QIcon("01.ico")
            message_dialog.setWindowIcon(app_icon)
        except Exception as e:
            print(f"خطأ في تحميل أيقونة البرنامج: {e}")

        # تنسيق النافذة
        message_dialog.setStyleSheet("""
            QDialog {
                background-color: #f0f8ff;
                border: 2px solid #1565c0;
                border-radius: 10px;
            }
            QLabel {
                color: #333333;
                font-weight: bold;
            }
            QLabel#icon_label {
                padding: 10px;
            }
            QLabel#message_label {
                background-color: #e3f2fd;
                border: 1px solid #1565c0;
                border-radius: 5px;
                padding: 15px;
                font-size: 14pt;
            }
            QPushButton {
                background-color: #1565c0;
                color: white;
                border-radius: 5px;
                padding: 8px 15px;
                font-weight: bold;
                min-height: 35px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #0D47A1;
                border: 2px solid #1565c0;
            }
        """)

        # إنشاء تخطيط النافذة
        layout = QVBoxLayout(message_dialog)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # إضافة أيقونة وعنوان
        header_layout = QHBoxLayout()

        # محاولة إضافة أيقونة البرنامج
        icon_label = QLabel()
        icon_label.setObjectName("icon_label")
        icon_label.setAlignment(Qt.AlignCenter)
        try:
            # استخدام أيقونة قياسية
            info_icon = QStyle.standardPixmap(QStyle.SP_MessageBoxInformation, QStyle.StyleOptionButton(), self)
            info_icon = QPixmap(info_icon).scaled(48, 48, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            icon_label.setPixmap(info_icon)
        except Exception:
            # استخدام نص بديل في حالة الفشل
            icon_label.setText("i")
            icon_label.setFont(QFont("Arial", 24))
            icon_label.setStyleSheet("color: #1565c0;")

        header_layout.addWidget(icon_label)

        # إضافة عنوان النافذة
        title_label = QLabel(title)
        title_label.setFont(QFont("Calibri", 16, QFont.Bold))
        title_label.setStyleSheet("color: #1565c0;")
        title_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title_label, 1)

        layout.addLayout(header_layout)

        # إضافة رسالة
        message_label = QLabel(message)
        message_label.setObjectName("message_label")
        message_label.setFont(QFont("Calibri", 13))
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setWordWrap(True)
        layout.addWidget(message_label)

        # إضافة زر الموافقة
        button_layout = QHBoxLayout()
        ok_button = QPushButton("موافق")
        ok_button.setFont(QFont("Calibri", 12, QFont.Bold))
        ok_button.setCursor(Qt.PointingHandCursor)
        ok_button.clicked.connect(message_dialog.accept)
        button_layout.addWidget(ok_button)

        layout.addLayout(button_layout)

        # عرض النافذة
        message_dialog.exec_()
        return True

    def show_scoring_sheet(self):
        """عرض ورقة التنقيط مباشرة من نافذة مسك أوراق الفروض"""
        try:
            # تعطيل زر ورقة التنقيط أثناء المعالجة
            self.scoring_sheet_button.setEnabled(False)
            self.scoring_sheet_button.setText("جاري إنشاء ورقة التنقيط...")

            # إنشاء شريط التحميل المحسن
            progress = QProgressDialog("جاري إنشاء ورقة التنقيط...", "إلغاء", 0, 100, self)
            progress.setWindowTitle("إنشاء ورقة التنقيط")
            progress.setWindowModality(Qt.WindowModal)
            progress.setMinimumDuration(0)
            progress.setMinimumWidth(400)
            progress.setStyleSheet("""
                QProgressDialog {
                    background-color: #f0f8ff;
                    border: 2px solid #1565c0;
                    border-radius: 10px;
                }
                QProgressBar {
                    border: 1px solid #1565c0;
                    border-radius: 5px;
                    text-align: center;
                    background-color: #e3f2fd;
                    height: 25px;
                }
                QProgressBar::chunk {
                    background-color: #1565c0;
                    width: 10px;
                    margin: 0.5px;
                    border-radius: 3px;
                }
                QLabel {
                    color: #1565c0;
                    font-weight: bold;
                    font-size: 14px;
                }
                QPushButton {
                    background-color: #1565c0;
                    color: white;
                    border-radius: 5px;
                    padding: 5px 10px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #0D47A1;
                }
            """)

            # تعيين أيقونة البرنامج للنافذة
            try:
                app_icon = QIcon("01.ico")
                progress.setWindowIcon(app_icon)
            except Exception as e:
                print(f"خطأ في تحميل أيقونة البرنامج: {e}")

            progress.setValue(10)
            QApplication.processEvents()

            # التحقق من اختيار المستوى
            level = self.levels_combo.currentText()
            if not level:
                self.show_custom_message("تنبيه", "يرجى اختيار المستوى أولاً")
                self.scoring_sheet_button.setEnabled(True)
                self.scoring_sheet_button.setText("ورقة التنقيط")
                progress.close()
                return

            progress.setValue(20)
            QApplication.processEvents()

            # التحقق من اختيار قسم واحد على الأقل
            selected_sections = []
            for i, checkbox in enumerate(self.section_checkboxes):
                if checkbox.isChecked():
                    section_item = self.sections_table.item(i, 1)
                    if section_item:
                        selected_sections.append(section_item.text())

            if not selected_sections:
                self.show_custom_message("تنبيه", "يرجى اختيار قسم واحد على الأقل")
                self.scoring_sheet_button.setEnabled(True)
                self.scoring_sheet_button.setText("ورقة التنقيط")
                progress.close()
                return

            progress.setValue(30)
            QApplication.processEvents()

            # الحصول على المادة (اختيارية)
            subject = self.subject_combo.currentText()

            progress.setValue(40)
            QApplication.processEvents()

            # الحصول على الأستاذ (اختياري)
            teacher = self.teacher_combo.currentText()

            # الحصول على اختيار نموذج الفرض من النافذة الرئيسية
            template_result = self.get_template_selection()

            # تحديث شريط التقدم
            progress.setValue(60)
            QApplication.processEvents()

            # الاتصال بقاعدة البيانات
            conn = sqlite3.connect(get_database_path())
            cursor = conn.cursor()

            # استعلام لجلب معلومات المؤسسة
            cursor.execute("""
                SELECT المؤسسة, السنة_الدراسية, الأسدس, ImagePath1
                FROM بيانات_المؤسسة
                LIMIT 1
            """)

            institution_data = cursor.fetchone()
            if institution_data:
                school_name = institution_data[0] or "المؤسسة"
                school_year = institution_data[1] or ""
                semester = institution_data[2] or ""
                logo_path = institution_data[3] or ""
            else:
                school_name = "المؤسسة"
                school_year = ""
                semester = ""
                logo_path = ""

            # تحديث شريط التقدم
            progress.setValue(70)
            QApplication.processEvents()

            # إنشاء تقرير ورقة التنقيط لكل قسم محدد
            for section in selected_sections:
                # استعلام لجلب قائمة الطلاب في القسم
                cursor.execute("""
                    SELECT l.رت, s.الاسم_والنسب, l.الرمز
                    FROM اللوائح l
                    JOIN السجل_العام s ON l.الرمز = s.الرمز
                    WHERE l.القسم = ? AND l.السنة_الدراسية = ?
                    ORDER BY l.رت
                """, (section, school_year))

                students = cursor.fetchall()

                if not students:
                    if len(selected_sections) == 1:
                        self.show_custom_message("معلومات", f"لا يوجد طلاب في القسم {section}")
                        conn.close()
                        self.scoring_sheet_button.setEnabled(True)
                        self.scoring_sheet_button.setText("ورقة التنقيط")
                        progress.close()
                        return
                    continue  # تخطي هذا القسم والانتقال إلى القسم التالي

                # تحديث شريط التقدم
                progress.setValue(80)
                QApplication.processEvents()

                # إنشاء تقرير ورقة التنقيط للقسم الحالي
                self.create_scoring_sheet_report(
                    section,
                    level,
                    subject,
                    teacher,
                    school_name,
                    school_year,
                    semester,
                    logo_path,
                    students,
                    template_result["template"],
                    template_result["note"],
                    template_result["header_height"],
                    template_result["body_height"]
                )

            conn.close()

            # إعادة تمكين زر ورقة التنقيط
            self.scoring_sheet_button.setEnabled(True)
            self.scoring_sheet_button.setText("ورقة التنقيط")

            # إزالة جميع علامات التحديد من مربعات اختيار الفروض فقط
            self.clear_all_checkboxes()

            # إغلاق شريط التقدم
            progress.close()

        except Exception as e:
            # إعادة تمكين زر ورقة التنقيط في حالة الخطأ
            self.scoring_sheet_button.setEnabled(True)
            self.scoring_sheet_button.setText("ورقة التنقيط")

            self.show_custom_message(
                "خطأ",
                f"حدث خطأ أثناء إنشاء ورقة التنقيط:\n{str(e)}"
            )
            traceback.print_exc()

    def show_delete_exam_dialog(self):
        """عرض نافذة حذف بطاقة فرض"""
        try:
            # إنشاء نافذة حوار جديدة
            delete_dialog = QDialog(self)
            delete_dialog.setWindowTitle("حذف بطاقة فرض")
            delete_dialog.setMinimumWidth(500)
            delete_dialog.setMinimumHeight(400)
            delete_dialog.setLayoutDirection(Qt.RightToLeft)
            delete_dialog.setWindowFlags(Qt.Dialog | Qt.WindowCloseButtonHint)

            # إضافة عنوان
            title_label = QLabel("حذف بطاقة فرض", delete_dialog)
            title_label.setFont(QFont("Calibri", 16, QFont.Bold))
            title_label.setStyleSheet("color: #e53935;")
            title_label.setAlignment(Qt.AlignCenter)
            title_label.setGeometry(50, 20, 400, 30)

            # إضافة وصف
            description_label = QLabel("يرجى اختيار الأستاذ والقسم ونوع الفرض لحذف البطاقة", delete_dialog)
            description_label.setFont(self.font_calibri_12_normal)
            description_label.setAlignment(Qt.AlignCenter)
            description_label.setGeometry(50, 60, 400, 30)

            # إضافة مربع تحرير وسرد للأستاذ
            teacher_label = QLabel("الأستاذ:", delete_dialog)
            teacher_label.setFont(self.font_calibri_13_bold)
            teacher_label.setGeometry(400, 100, 100, 30)

            teacher_combo = QComboBox(delete_dialog)
            teacher_combo.setFont(self.font_calibri_13_bold)
            teacher_combo.setGeometry(100, 100, 300, 30)
            teacher_combo.setCursor(Qt.PointingHandCursor)

            # إضافة مربع تحرير وسرد للقسم
            section_label = QLabel("القسم:", delete_dialog)
            section_label.setFont(self.font_calibri_13_bold)
            section_label.setGeometry(400, 150, 100, 30)

            section_combo = QComboBox(delete_dialog)
            section_combo.setFont(self.font_calibri_13_bold)
            section_combo.setGeometry(100, 150, 300, 30)
            section_combo.setCursor(Qt.PointingHandCursor)

            # إضافة مربع تحرير وسرد لنوع الفرض
            exam_type_label = QLabel("نوع الفرض:", delete_dialog)
            exam_type_label.setFont(self.font_calibri_13_bold)
            exam_type_label.setGeometry(400, 200, 100, 30)

            exam_type_combo = QComboBox(delete_dialog)
            exam_type_combo.setFont(self.font_calibri_13_bold)
            exam_type_combo.setGeometry(100, 200, 300, 30)
            exam_type_combo.setCursor(Qt.PointingHandCursor)

            # إضافة أزرار الحذف والإلغاء
            cancel_button = QPushButton("إلغاء", delete_dialog)
            cancel_button.setFont(self.font_calibri_13_bold)
            cancel_button.setGeometry(150, 300, 120, 40)
            cancel_button.setStyleSheet("""
                background-color: #9e9e9e;
                color: white;
                padding: 5px;
                cursor: pointer;
            """)
            cancel_button.setCursor(Qt.PointingHandCursor)
            cancel_button.clicked.connect(delete_dialog.reject)

            delete_button = QPushButton("حذف", delete_dialog)
            delete_button.setFont(self.font_calibri_13_bold)
            delete_button.setGeometry(280, 300, 120, 40)
            delete_button.setStyleSheet("""
                background-color: #e53935;
                color: white;
                padding: 5px;
                cursor: pointer;
            """)
            delete_button.setCursor(Qt.PointingHandCursor)

            # تحميل قائمة الأساتذة
            conn = sqlite3.connect(get_database_path())
            cursor = conn.cursor()

            cursor.execute("""
                SELECT DISTINCT الأستاذ FROM مسك_أوراق_الفروض
                WHERE الأستاذ IS NOT NULL AND الأستاذ != ''
                ORDER BY الأستاذ
            """)
            teachers = [row[0] for row in cursor.fetchall()]
            teacher_combo.clear()
            teacher_combo.addItem("")
            teacher_combo.addItems(teachers)

            # دالة لتحديث قائمة الأقسام بناءً على الأستاذ المحدد
            def update_sections():
                teacher = teacher_combo.currentText()
                if not teacher:
                    section_combo.clear()
                    section_combo.addItem("")
                    exam_type_combo.clear()
                    exam_type_combo.addItem("")
                    return

                cursor.execute("""
                    SELECT DISTINCT القسم FROM مسك_أوراق_الفروض
                    WHERE الأستاذ = ? AND القسم IS NOT NULL AND القسم != ''
                """, (teacher,))
                sections = [row[0] for row in cursor.fetchall()]

                # ترتيب الأقسام حسب رقم القسم
                def extract_section_number(section):
                    # استخراج رقم القسم من النص (مثال: "1/1" أو "2/3" إلخ)
                    import re
                    match = re.search(r'(\d+)[/\-]?(\d*)', section)
                    if match:
                        # إذا كان القسم بصيغة "رقم/رقم"
                        if match.group(2):
                            return int(match.group(1)) * 100 + int(match.group(2))
                        # إذا كان القسم برقم فقط
                        return int(match.group(1)) * 100
                    # إذا لم يتم العثور على رقم، نعيد قيمة كبيرة لوضع القسم في النهاية
                    return 9999

                # ترتيب الأقسام حسب الرقم
                sections.sort(key=extract_section_number)
                section_combo.clear()
                section_combo.addItem("")
                section_combo.addItems(sections)

            # دالة لتحديث قائمة أنواع الفروض بناءً على الأستاذ والقسم المحددين
            def update_exam_types():
                teacher = teacher_combo.currentText()
                section = section_combo.currentText()
                if not teacher or not section:
                    exam_type_combo.clear()
                    exam_type_combo.addItem("")
                    return

                cursor.execute("""
                    SELECT DISTINCT نوع_الفرض FROM مسك_أوراق_الفروض
                    WHERE الأستاذ = ? AND القسم = ? AND نوع_الفرض IS NOT NULL AND نوع_الفرض != ''
                    ORDER BY نوع_الفرض
                """, (teacher, section))
                exam_types = [row[0] for row in cursor.fetchall()]
                exam_type_combo.clear()
                exam_type_combo.addItem("")
                exam_type_combo.addItems(exam_types)

            # ربط إشارات تغيير القيمة بالدوال المناسبة
            teacher_combo.currentIndexChanged.connect(update_sections)
            section_combo.currentIndexChanged.connect(update_exam_types)

            # دالة لحذف بطاقة الفرض
            def delete_exam():
                teacher = teacher_combo.currentText()
                section = section_combo.currentText()
                exam_type = exam_type_combo.currentText()

                if not teacher or not section or not exam_type:
                    QMessageBox.warning(delete_dialog, "تنبيه", "يرجى اختيار الأستاذ والقسم ونوع الفرض")
                    return

                # التأكيد قبل الحذف
                reply = QMessageBox.question(
                    delete_dialog,
                    "تأكيد الحذف",
                    f"هل أنت متأكد من حذف بطاقة الفرض التالية؟\n\nالأستاذ: {teacher}\nالقسم: {section}\nنوع الفرض: {exam_type}",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )

                if reply == QMessageBox.Yes:
                    try:
                        # حذف بطاقة الفرض
                        cursor.execute("""
                            DELETE FROM مسك_أوراق_الفروض
                            WHERE الأستاذ = ? AND القسم = ? AND نوع_الفرض = ?
                        """, (teacher, section, exam_type))

                        conn.commit()

                        # عرض رسالة نجاح
                        QMessageBox.information(
                            delete_dialog,
                            "نجاح",
                            "تم حذف بطاقة الفرض بنجاح!",
                            QMessageBox.Ok
                        )

                        # تحديث القوائم
                        update_sections()
                        update_exam_types()

                    except Exception as e:
                        # عرض رسالة خطأ
                        error_message = f"حدث خطأ أثناء حذف بطاقة الفرض:\n{str(e)}"
                        QMessageBox.critical(
                            delete_dialog,
                            "خطأ",
                            error_message,
                            QMessageBox.Ok
                        )
                        traceback.print_exc()

            # ربط زر الحذف بدالة الحذف
            delete_button.clicked.connect(delete_exam)

            # عرض النافذة
            delete_dialog.exec_()

            # إغلاق الاتصال بقاعدة البيانات
            conn.close()

        except Exception as e:
            # عرض رسالة خطأ
            error_message = f"حدث خطأ أثناء فتح نافذة حذف بطاقة الفرض:\n{str(e)}"
            QMessageBox.critical(
                self,
                "خطأ",
                error_message,
                QMessageBox.Ok
            )
            traceback.print_exc()

    def show_section_exams_report(self):
        """عرض تقرير الفروض التي تم مسكها حسب القسم"""
        try:
            # التحقق من اختيار قسم
            # نحتاج أولاً التحقق من اختيار مستوى
            level = self.levels_combo.currentText()
            if not level:
                QMessageBox.warning(self, "تنبيه", "يرجى اختيار المستوى أولاً")
                return

            # التحقق من اختيار قسم واحد على الأقل
            selected_sections = []
            for i, checkbox in enumerate(self.section_checkboxes):
                if checkbox.isChecked():
                    section_item = self.sections_table.item(i, 1)
                    if section_item:
                        selected_sections.append(section_item.text())

            if not selected_sections:
                QMessageBox.warning(self, "تنبيه", "يرجى اختيار قسم واحد على الأقل")
                return

            # الاتصال بقاعدة البيانات
            conn = sqlite3.connect(get_database_path())
            cursor = conn.cursor()

            # استعلام لجلب معلومات المؤسسة
            cursor.execute("""
                SELECT المؤسسة, السنة_الدراسية, الأسدس, ImagePath1
                FROM بيانات_المؤسسة
                LIMIT 1
            """)

            institution_data = cursor.fetchone()
            if institution_data:
                school_name = institution_data[0] or "المؤسسة"
                school_year = institution_data[1] or ""
                semester = institution_data[2] or ""
                logo_path = institution_data[3] or ""
            else:
                school_name = "المؤسسة"
                school_year = ""
                semester = ""
                logo_path = ""

            # إذا تم اختيار قسم واحد فقط
            if len(selected_sections) == 1:
                section = selected_sections[0]

                # استعلام لجلب الفروض المسجلة للقسم المحدد، مرتبة حسب الأستاذ ونوع الفرض
                cursor.execute("""
                    SELECT الأستاذ, نوع_الفرض, التاريخ, المادة
                    FROM مسك_أوراق_الفروض
                    WHERE القسم = ?
                    ORDER BY الأستاذ, نوع_الفرض, التاريخ
                """, (section,))

                exams = cursor.fetchall()

                if not exams:
                    QMessageBox.information(self, "معلومات", f"لا توجد فروض مسجلة للقسم {section}")
                    conn.close()
                    return

                # إنشاء التقرير لقسم واحد
                conn.close()
                self.create_section_report(section, level, exams, school_name, school_year, semester, logo_path)

            # إذا تم اختيار أكثر من قسم
            else:
                # إنشاء قائمة بأسماء الأقسام المحددة للعنوان
                sections_str = " و ".join(selected_sections)

                # استعلام لجلب الفروض المسجلة لجميع الأقسام المحددة
                placeholders = ','.join(['?'] * len(selected_sections))
                query = f"""
                    SELECT القسم, الأستاذ, نوع_الفرض, التاريخ, المادة
                    FROM مسك_أوراق_الفروض
                    WHERE القسم IN ({placeholders})
                    ORDER BY القسم, الأستاذ, نوع_الفرض, التاريخ
                """

                cursor.execute(query, selected_sections)
                all_exams = cursor.fetchall()
                conn.close()

                if not all_exams:
                    QMessageBox.information(self, "معلومات", "لا توجد فروض مسجلة للأقسام المحددة")
                    return

                # إنشاء التقرير لعدة أقسام
                self.create_multiple_sections_report(selected_sections, level, all_exams, school_name, school_year, semester, logo_path)

        except Exception as e:
            # عرض رسالة خطأ
            error_message = f"حدث خطأ أثناء إنشاء التقرير:\n{str(e)}"
            QMessageBox.critical(
                self,
                "خطأ",
                error_message,
                QMessageBox.Ok
            )
            traceback.print_exc()

    def create_scoring_sheet_report(self, section, level, subject, teacher, school_name, school_year, semester, logo_path, students, template_type=1, note="", header_height=16, body_height=14):
        """إنشاء تقرير ورقة التنقيط للقسم المحدد"""
        try:
            # التعامل مع المادة والأستاذ الفارغين
            subject_display = subject if subject and subject.strip() else ""
            teacher_display = teacher if teacher and teacher.strip() else "____________"
            
            # إنشاء مجلد التقارير على سطح المكتب
            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
            reports_dir = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة", "أوراق التنقيط")
            if not os.path.exists(reports_dir):
                os.makedirs(reports_dir)

            # نسخ الشعار إلى مجلد التقارير لضمان الوصول إليه
            logo_html = ""
            if logo_path and os.path.exists(logo_path):
                # استخراج اسم الملف من المسار الكامل
                logo_filename = os.path.basename(logo_path)
                logo_destination = os.path.join(reports_dir, logo_filename)

                # نسخ ملف الشعار إلى مجلد التقارير
                import shutil
                shutil.copy2(logo_path, logo_destination)

                # استخدام مسار الشعار المحلي مع تحديد الحجم المطلوب
                logo_html = f'<img src="{logo_filename}" style="width: 100px; height: 50px; margin-top: 2px; object-fit: contain;">'

            # إنشاء محتوى HTML للتقرير
            html_content = f"""<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ورقة التنقيط {section}{' ' + subject_display if subject_display.strip() else ''}</title>
    <style>
        @page {{
            size: A4 portrait;
            margin: 0.2cm 0.2cm 0.2cm 0.2cm;
        }}
        body {{
            font-family: 'Calibri', Arial, sans-serif;
            margin: 0;
            padding: 0;
            direction: rtl;
        }}
        .container {{
            width: 100%;
            max-width: 21cm;
            margin: 0 auto;
            padding: 0.2cm;
            box-sizing: border-box;
        }}
        .header {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;
        }}
        .header-right, .header-left {{
            width: 33%;
        }}
        .header-center {{
            width: 34%;
            text-align: center;
        }}
        .title {{
            text-align: center;
            font-weight: bold;
            font-size: 20px;
            margin: 5px 0;
            border: 1px solid #000;
            padding: 3px;
        }}
        .info-container {{
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }}
        .info-right, .info-left {{
            width: 48%;
        }}
        .info-item {{
            margin-bottom: 2px;
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            margin-top: 5px;
        }}
        th, td {{
            border: 1px solid #000;
            padding: 2px 4px;
            text-align: center;
            height: 16px;
            font-size: 13px;
        }}
        th {{
            background-color: #f2f2f2;
            font-weight: bold;
            height: 20px;
        }}
        tr {{
            height: 14px;
        }}
        .print-button {{
            background-color: #1976d2;
            color: white;
            border: none;
            padding: 10px 20px;
            font-size: 16px;
            cursor: pointer;
            border-radius: 5px;
            margin: 10px auto;
            display: block;
        }}
        @media print {{
            .print-button {{
                display: none;
            }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-right">
                <div>{school_name}</div>
                <div>ورقة التنقيط</div>
                <div>المادة: {subject_display}</div>
            </div>
            <div class="header-center">
                {logo_html}
            </div>
            <div class="header-left">
                <div>{school_year}</div>
                <div>{semester}</div>
                <div>الأستاذ(ة): {teacher_display}</div>
            </div>
        </div>

        <div class="title">ورقة التنقيط {section}{' ' + subject_display if subject_display.strip() else ''}</div>

        <table>
            <thead>
                <tr>
                    <th width="60px">الملاحظة</th>
                    <th width="60px">الفرض الثاني</th>
                    <th width="60px">الفرض الأول</th>
                    <th width="120px">رمز التلميذ</th>
                    <th width="150px">الاسم والنسب</th>
                    <th width="30px">ر.ت</th>
                </tr>
            </thead>
            <tbody>
"""

            # إضافة صفوف التلاميذ
            for student in students:
                student_number, student_name, student_code = student
                html_content += f"""
                <tr style="height: 14px;">
                    <td style="text-align: center;"></td>
                    <td style="text-align: center;"></td>
                    <td style="text-align: center;"></td>
                    <td style="text-align: center;">{student_code or ""}</td>
                    <td style="text-align: right;">{student_name or ""}</td>
                    <td style="text-align: center;">{student_number}</td>
                </tr>
"""

            # إكمال محتوى HTML
            html_content += """
            </tbody>
        </table>

        <div style="margin-top: 20px; display: flex; justify-content: space-between;">
            <div style="width: 30%; text-align: center;">
                <p style="margin-bottom: 50px;">توقيع الأستاذ(ة)</p>
                <div style="border-top: 1px solid #000; width: 100%;"></div>
            </div>
            <div style="width: 30%; text-align: center;">
                <p style="margin-bottom: 50px;">توقيع الحارسة العامة</p>
                <div style="border-top: 1px solid #000; width: 100%;"></div>
            </div>
        </div>
    </div>

    <div style="text-align: center; margin-top: 20px;">
        <button class="print-button" onclick="window.print()">طباعة التقرير</button>
    </div>
</body>
</html>
"""

            # إنشاء اسم ملف فريد باستخدام الطابع الزمني
            import time
            timestamp = int(time.time())
            html_filename = f"ورقة_التنقيط_{section}_{timestamp}.html"
            pdf_filename = f"ورقة_التنقيط_{section}_{timestamp}.pdf"
            html_path = os.path.join(reports_dir, html_filename)
            pdf_path = os.path.join(reports_dir, pdf_filename)

            # حفظ ملف HTML
            with open(html_path, 'w', encoding='utf-8') as f:
                f.write(html_content)

            # إنشاء ملف PDF مباشرة باستخدام ReportLab
            if not REPORTLAB_AVAILABLE:
                self.show_custom_message(
                    "تنبيه",
                    "المكتبات اللازمة لإنشاء التقارير غير متوفرة.\n"
                    "سيتم فتح التقرير بصيغة HTML بدلاً من ذلك."
                )
                import webbrowser
                webbrowser.open('file://' + os.path.abspath(html_path))
                return

            try:
                # إنشاء ملف PDF
                pdf_path = os.path.splitext(html_path)[0] + '.pdf'
                # تعديل الهوامش لتكون 0.2 سم من جميع الجوانب
                doc = SimpleDocTemplate(pdf_path, pagesize=A4, rightMargin=0.2*cm, leftMargin=0.2*cm, topMargin=0.2*cm, bottomMargin=0.2*cm)
                elements = []

                # استخدام الخط العربي إذا كان متاحًا
                header_font = 'Arabic' if 'Arabic' in pdfmetrics.getRegisteredFontNames() else 'Helvetica-Bold'
                title_font = 'Arabic' if 'Arabic' in pdfmetrics.getRegisteredFontNames() else 'Helvetica-Bold'

                # أنماط الفقرة
                left_style = ParagraphStyle("left", fontName=header_font, fontSize=12, alignment=0)  # 0=LEFT
                right_style = ParagraphStyle("right", fontName=header_font, fontSize=12, alignment=2)  # 2=RIGHT
                center_style = ParagraphStyle("center", fontName=title_font, fontSize=14, alignment=1)  # 1=CENTER

                # إعداد محتويات الترويسة
                # إذا كانت المادة فارغة، نضع خطوط فارغة
                if subject and subject.strip():
                    subject_text = Paragraph(reshape_ar(f"المادة: {subject}"), left_style)
                else:
                    subject_text = Paragraph(reshape_ar("المادة: ____________"), left_style)

                # إذا كان اسم الأستاذ فارغًا، نضع خطوط فارغة
                if teacher and teacher.strip():
                    teacher_text = Paragraph(reshape_ar(f"الأستاذ(ة): {teacher}"), left_style)
                else:
                    teacher_text = Paragraph(reshape_ar("الأستاذ(ة): ____________"), left_style)

                school_name_text = Paragraph(reshape_ar(school_name), right_style)
                academic_year_text = Paragraph(reshape_ar(f"السنة الدراسية: {school_year}"), right_style)
                semester_text = Paragraph(reshape_ar(f"الأسدس: {semester}"), right_style)

                # إعداد الشعار
                logo_img = None
                if logo_path and os.path.exists(logo_path):
                    logo_img = Image(logo_path)
                    logo_img.drawWidth = 100  # تقليل حجم الشعار إلى 100 بكسل عرض
                    logo_img.drawHeight = 50  # تقليل ارتفاع الشعار إلى 50 بكسل

                # إنشاء جدول الترويسة
                left_column = [subject_text, Spacer(1, 0.1*cm), teacher_text]
                right_column = [school_name_text, Spacer(1, 0.1*cm), academic_year_text, Spacer(1, 0.1*cm), semester_text]

                # إذا كان الشعار متاحًا، نضيفه للعمود الأوسط، وإلا نضع مساحة فارغة
                middle_column = logo_img if logo_img else Spacer(1, 50)

                header_table = Table(
                    [[left_column, middle_column, right_column]],
                    colWidths=[6*cm, 4*cm, 6*cm]  # ضبط العرض حسب الحاجة
                )

                # تنسيق جدول الترويسة
                header_table.setStyle(TableStyle([
                    ('VALIGN',     (0,0), (-1,-1), 'MIDDLE'),
                    ('ALIGN',      (0,0), (0,0),   'LEFT'),
                    ('ALIGN',      (1,0), (1,0),   'CENTER'),
                    ('ALIGN',      (2,0), (2,0),   'RIGHT'),
                ]))

                elements.append(header_table)
                elements.append(Spacer(1, 0.1*cm))

                # عنوان التقرير
                title = f"ورقة التنقيط {section}{' ' + subject_display if subject_display.strip() else ''}"
                elements.append(Paragraph(reshape_ar(title), center_style))
                elements.append(Spacer(1, 0.2*cm))

                # إنشاء جدول البيانات للطلاب بالترتيب الصحيح من اليمين إلى اليسار
                # تحديد العناوين حسب نوع النموذج المختار
                if template_type == 1:  # نموذج فرضان وأنشطة
                    headers = [
                        reshape_ar("الملاحظات"),
                        reshape_ar("الأنشطة"),
                        reshape_ar("الفرض 2"),
                        reshape_ar("الفرض 1"),
                        reshape_ar("رمز التلميذ"),
                        reshape_ar("اسم التلميذ"),
                        reshape_ar(" ر.ت")
                    ]
                elif template_type == 2:  # نموذج ثلاث فروض وأنشطة
                    headers = [
                        reshape_ar("الملاحظات"),
                        reshape_ar("الأنشطة"),
                        reshape_ar("الفرض 3"),
                        reshape_ar("الفرض 2"),
                        reshape_ar("الفرض 1"),
                        reshape_ar("رمز التلميذ"),
                        reshape_ar("اسم التلميذ"),
                        reshape_ar(" ر.ت")
                    ]
                elif template_type == 3:  # نموذج أربع فرض وأنشطة
                    headers = [
                        reshape_ar("الملاحظات"),
                        reshape_ar("الأنشطة"),
                        reshape_ar("الفرض 4"),
                        reshape_ar("الفرض 3"),
                        reshape_ar("الفرض 2"),
                        reshape_ar("الفرض 1"),
                        reshape_ar("رمز التلميذ"),
                        reshape_ar("اسم التلميذ"),
                        reshape_ar(" ر.ت")
                    ]
                elif template_type == 4:  # نموذج فرضان من غير أنشطة
                    headers = [
                        reshape_ar("الملاحظات"),
                        reshape_ar("الفرض 2"),
                        reshape_ar("الفرض 1"),
                        reshape_ar("رمز التلميذ"),
                        reshape_ar("اسم التلميذ"),
                        reshape_ar(" ر.ت")
                    ]
                elif template_type == 5:  # نموذج ثلاث فرض من غير أنشطة
                    headers = [
                        reshape_ar("الملاحظات"),
                        reshape_ar("الفرض 3"),
                        reshape_ar("الفرض 2"),
                        reshape_ar("الفرض 1"),
                        reshape_ar("رمز التلميذ"),
                        reshape_ar("اسم التلميذ"),
                        reshape_ar(" ر.ت")
                    ]
                else:  # النموذج الافتراضي
                    headers = [
                        reshape_ar("الملاحظة"),
                        reshape_ar("الفرض الثاني"),
                        reshape_ar("الفرض الأول"),
                        reshape_ar("رمز التلميذ"),
                        reshape_ar("اسم التلميذ"),
                        reshape_ar(" ر.ت")
                    ]

                # إضافة الملاحظة إذا كانت موجودة
                if note:
                    elements.append(Paragraph(reshape_ar(f"ملاحظة: {note}"), ParagraphStyle("note", fontName=header_font, fontSize=10, alignment=2)))
                    elements.append(Spacer(1, 0.2*cm))

                data = [headers]

                # إضافة بيانات الطلاب بالترتيب الصحيح من اليمين إلى اليسار
                for student in students:
                    student_number, student_name, student_code = student

                    # إنشاء صف البيانات حسب نوع النموذج
                    if template_type == 1:  # نموذج فرضان وأنشطة
                        data_row = [
                            "",   # الملاحظات
                            "",   # الأنشطة
                            "",   # الفرض 2
                            "",   # الفرض 1
                            reshape_ar(student_code or ""),
                            reshape_ar(student_name or ""),
                            reshape_ar(str(student_number))
                        ]
                    elif template_type == 2:  # نموذج ثلاث فروض وأنشطة
                        data_row = [
                            "",   # الملاحظات
                            "",   # الأنشطة
                            "",   # الفرض 3
                            "",   # الفرض 2
                            "",   # الفرض 1
                            reshape_ar(student_code or ""),
                            reshape_ar(student_name or ""),
                            reshape_ar(str(student_number))
                        ]
                    elif template_type == 3:  # نموذج أربع فرض وأنشطة
                        data_row = [
                            "",   # الملاحظات
                            "",   # الأنشطة
                            "",   # الفرض 4
                            "",   # الفرض 3
                            "",   # الفرض 2
                            "",   # الفرض 1
                            reshape_ar(student_code or ""),
                            reshape_ar(student_name or ""),
                            reshape_ar(str(student_number))
                        ]
                    elif template_type == 4:  # نموذج فرضان من غير أنشطة
                        data_row = [
                            "",   # الملاحظات
                            "",   # الفرض 2
                            "",   # الفرض 1
                            reshape_ar(student_code or ""),
                            reshape_ar(student_name or ""),
                            reshape_ar(str(student_number))
                        ]
                    elif template_type == 5:  # نموذج ثلاث فرض من غير أنشطة
                        data_row = [
                            "",   # الملاحظات
                            "",   # الفرض 3
                            "",   # الفرض 2
                            "",   # الفرض 1
                            reshape_ar(student_code or ""),
                            reshape_ar(student_name or ""),
                            reshape_ar(str(student_number))
                        ]
                    else:  # النموذج الافتراضي
                        data_row = [
                            "",   # الملاحظة
                            "",   # الفرض الثاني
                            "",   # الفرض الأول
                            reshape_ar(student_code or ""),
                            reshape_ar(student_name or ""),
                            reshape_ar(str(student_number))
                        ]

                    data.append(data_row)

                # لا نعكس ترتيب البيانات لأن الترتيب يجب أن يكون: رت، الاسم والنسب، الرمز، الفرض الأول، الفرض الثاني، الملاحظة
                # data = [list(reversed(row)) for row in data]

                # تعيين أبعاد الأعمدة حسب المطلوب الجديد بالترتيب الصحيح من اليمين إلى اليسار
                # توحيد عرض الأعمدة في جميع النماذج
                if template_type == 1:  # نموذج فرضان وأنشطة
                    col_widths = [60, 60, 60, 60, 80, 100, 30]  # الملاحظات، الأنشطة، الفرض 2، الفرض 1، الرمز، الاسم، رت
                elif template_type == 2:  # نموذج ثلاث فروض وأنشطة
                    col_widths = [60, 60, 60, 60, 60, 80, 100, 30]  # الملاحظات، الأنشطة، الفرض 3، الفرض 2، الفرض 1، الرمز، الاسم، رت
                elif template_type == 3:  # نموذج أربع فرض وأنشطة
                    col_widths = [60, 60, 60, 60, 60, 60, 80, 100, 30]  # الملاحظات، الأنشطة، الفرض 4، الفرض 3، الفرض 2، الفرض 1، الرمز، الاسم، رت
                elif template_type == 4:  # نموذج فرضان من غير أنشطة
                    col_widths = [60, 60, 60, 80, 100, 30]  # الملاحظات، الفرض 2، الفرض 1، الرمز، الاسم، رت
                elif template_type == 5:  # نموذج ثلاث فرض من غير أنشطة
                    col_widths = [60, 60, 60, 60, 80, 100, 30]  # الملاحظات، الفرض 3، الفرض 2، الفرض 1، الرمز، الاسم، رت
                else:  # النموذج الافتراضي
                    col_widths = [60, 60, 60, 80, 100, 30]  # الملاحظة، الفرض الثاني، الفرض الأول، الرمز، الاسم، رت

                # استخدام الخط العربي إذا كان متاحًا
                table_font = 'Arabic' if 'Arabic' in pdfmetrics.getRegisteredFontNames() else 'Helvetica'

                # استخدام قيم ارتفاع الصفوف المحددة من المعلمات
                row_heights = [header_height] + [body_height] * (len(data) - 1)

                # إنشاء الجدول مع rowHeights
                table = Table(
                    data,
                    colWidths=col_widths,
                    rowHeights=row_heights,
                    repeatRows=1
                )

                # تطبيق التنسيقات بدون ROWHEIGHT
                table.setStyle(TableStyle([
                    ('GRID', (0,0), (-1,-1), 0.5, colors.black),
                    ('FONTNAME', (0,0), (-1,-1), table_font),
                    ('FONTSIZE', (0,0), (-1,-1), 10),
                    ('BACKGROUND', (0,0), (-1,0), colors.lightgrey),
                    # محاذاة جميع الأعمدة ما عدا عمود الاسم للوسط
                    ('ALIGN', (0,0), (-2,-1), 'CENTER'),
                    # محاذاة عمود اسم التلميذ إلى اليمين (قبل عمود واحد من النهاية)
                    ('ALIGN', (-2,0), (-2,-1), 'RIGHT'),
                    # محاذاة عمود رقم الترتيب في المنتصف (آخر عمود)
                    ('ALIGN', (-1,0), (-1,-1), 'CENTER'),
                    ('VALIGN', (0,0), (-1,-1), 'MIDDLE')
                ]))

                elements.append(table)

                # إضافة توقيعات الأستاذ(ة) والحارسة العامة
                elements.append(Spacer(1, 1*cm))

                # إنشاء جدول للتوقيعات
                signature_data = [
                    [reshape_ar("توقيع الأستاذ(ة)"), "", reshape_ar("توقيع الحارسة العامة")],
                    ["", "", ""]
                ]

                # تحديد عرض الأعمدة وارتفاع الصفوف
                col_widths = [200, 50, 200]
                row_heights = [16, 15]  # 16 للنص (أو الهيدر) و30 للسطر الفارغ تحته

                # إنشاء الجدول مع تحديد rowHeights
                signature_table = Table(
                    signature_data,
                    colWidths=col_widths,
                    rowHeights=row_heights,
                )

                # استخدام الخط العربي إذا كان متاحًا
                signature_font = 'Arabic' if 'Arabic' in pdfmetrics.getRegisteredFontNames() else 'Helvetica-Bold'

                # تطبيق الأسلوب
                signature_table.setStyle(TableStyle([
                    ('ALIGN',        (0,0), (-1,-1), 'CENTER'),
                    ('VALIGN',       (0,0), (-1,-1), 'MIDDLE'),
                    ('FONTNAME',     (0,0), (-1,-1), signature_font),
                    ('FONTSIZE',     (0,0), (-1,-1), 12),
                    ('LINEBELOW',    (0,1), (0,1),   1, colors.black),
                    ('LINEBELOW',    (2,1), (2,1),   1, colors.black),
                    ('TOPPADDING',   (0,1), (-1,1),  10),
                    ('BOTTOMPADDING',(0,1), (-1,1),  0),
                ]))

                elements.append(signature_table)

                # بناء ملف PDF
                doc.build(elements)

                # فتح الملف
                os.startfile(pdf_path)

            except Exception as e:
                QMessageBox.critical(
                    self,
                    "خطأ",
                    f"حدث خطأ أثناء إنشاء ملف PDF:\n{str(e)}",
                    QMessageBox.Ok
                )
                # في حالة فشل إنشاء PDF، فتح ملف HTML في المتصفح
                import webbrowser
                webbrowser.open('file://' + os.path.abspath(html_path))



        except Exception as e:
            # عرض رسالة خطأ مخصصة
            self.show_custom_message(
                "خطأ",
                f"حدث خطأ أثناء إنشاء تقرير ورقة التنقيط:\n{str(e)}"
            )
            traceback.print_exc()

            # إعادة تمكين زر ورقة التنقيط
            self.scoring_sheet_button.setEnabled(True)
            self.scoring_sheet_button.setText("ورقة التنقيط")


    def create_multiple_sections_report(self, sections, level, exams, school_name, school_year, semester, logo_path):
        """إنشاء تقرير الفروض لعدة أقسام"""
        try:
            # إنشاء مجلد التقارير إذا لم يكن موجودًا
            reports_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "reports")
            if not os.path.exists(reports_dir):
                os.makedirs(reports_dir)

            # إنشاء نص للأقسام المحددة
            sections_str = " و ".join(sections)

            # إنشاء اسم ملف فريد باستخدام الطابع الزمني
            import time
            timestamp = int(time.time())
            pdf_filename = f"multiple_sections_report_{timestamp}.pdf"
            pdf_path = os.path.join(reports_dir, pdf_filename)

            # التحقق من توفر مكتبات إنشاء PDF
            if not REPORTLAB_AVAILABLE:
                QMessageBox.warning(
                    self,
                    "تنبيه",
                    "المكتبات اللازمة لإنشاء التقارير غير متوفرة.",
                    QMessageBox.Ok
                )
                return

            try:
                # إنشاء ملف PDF
                doc = SimpleDocTemplate(pdf_path, pagesize=A4, rightMargin=0.2*cm, leftMargin=0.2*cm, topMargin=0.2*cm, bottomMargin=0.2*cm)
                elements = []

                # استخدام الخط العربي إذا كان متاحًا
                header_font = 'Arabic' if 'Arabic' in pdfmetrics.getRegisteredFontNames() else 'Helvetica-Bold'
                title_font = 'Arabic' if 'Arabic' in pdfmetrics.getRegisteredFontNames() else 'Helvetica-Bold'
                normal_font = 'Arabic' if 'Arabic' in pdfmetrics.getRegisteredFontNames() else 'Helvetica'

                # أنماط الفقرة
                title_style = ParagraphStyle("title", fontName=title_font, fontSize=16, alignment=1, leading=20)  # 1=CENTER
                header_style = ParagraphStyle("header", fontName=header_font, fontSize=14, alignment=1, leading=18)  # 1=CENTER
                subheader_style = ParagraphStyle("subheader", fontName=header_font, fontSize=12, alignment=1, leading=16)  # 1=CENTER
                section_style = ParagraphStyle("section", fontName=header_font, fontSize=12, alignment=2, leading=16)  # 2=RIGHT
                teacher_style = ParagraphStyle("teacher", fontName=header_font, fontSize=11, alignment=2, leading=14)  # 2=RIGHT
                normal_style = ParagraphStyle("normal", fontName=normal_font, fontSize=10, alignment=2, leading=12)  # 2=RIGHT

                # إضافة شعار المؤسسة
                if logo_path and os.path.exists(logo_path):
                    logo_img = Image(logo_path)
                    logo_img.drawWidth = 100  # تقليل حجم الشعار إلى 100 بكسل عرض
                    logo_img.drawHeight = 50  # تقليل ارتفاع الشعار إلى 50 بكسل
                    elements.append(logo_img)

                # إضافة عنوان التقرير
                elements.append(Paragraph(reshape_ar(school_name), title_style))
                elements.append(Spacer(1, 0.1*cm))
                elements.append(Paragraph(reshape_ar(f"السنة الدراسية: {school_year}"), header_style))
                elements.append(Spacer(1, 0.1*cm))
                elements.append(Paragraph(reshape_ar(f"الأسدس: {semester}"), header_style))
                elements.append(Spacer(1, 0.1*cm))
                elements.append(Paragraph(reshape_ar("تقرير أوراق الفروض الممسوكة"), title_style))
                elements.append(Spacer(1, 0.1*cm))
                elements.append(Paragraph(reshape_ar(f"المستوى: {level}"), subheader_style))
                elements.append(Spacer(1, 0.1*cm))
                elements.append(Paragraph(reshape_ar(f"الأقسام: {sections_str}"), subheader_style))
                elements.append(Spacer(1, 0.3*cm))

                # تنظيم البيانات حسب القسم ثم الأستاذ ثم نوع الفرض
                # تجميع الفروض حسب القسم
                exams_by_section = {}
                for exam in exams:
                    section, teacher, exam_type, date, subject = exam
                    if section not in exams_by_section:
                        exams_by_section[section] = {}

                    if teacher not in exams_by_section[section]:
                        exams_by_section[section][teacher] = []

                    exams_by_section[section][teacher].append((exam_type, date, subject))

                # إضافة الفروض للتقرير مرتبة حسب القسم ثم الأستاذ
                for section in sections:  # استخدام ترتيب الأقسام كما تم اختيارها
                    if section in exams_by_section:
                        elements.append(Paragraph(reshape_ar(f"القسم: {section}"), section_style))
                        elements.append(Spacer(1, 0.2*cm))

                        # ترتيب الأساتذة أبجديًا
                        for teacher in sorted(exams_by_section[section].keys()):
                            elements.append(Paragraph(reshape_ar(f"الأستاذ(ة): {teacher}"), teacher_style))
                            elements.append(Spacer(1, 0.1*cm))

                            # ترتيب الفروض حسب نوع الفرض
                            teacher_exams = exams_by_section[section][teacher]
                            teacher_exams.sort(key=lambda x: x[0])

                            # إنشاء جدول للفروض
                            data = []
                            for exam_type, date, subject in teacher_exams:
                                data.append([
                                    reshape_ar(subject),
                                    reshape_ar(date),
                                    reshape_ar(exam_type)
                                ])

                            if data:
                                # إضافة رؤوس الأعمدة
                                headers = [
                                    reshape_ar("المادة"),
                                    reshape_ar("تاريخ المسك"),
                                    reshape_ar("نوع الفرض")
                                ]
                                data.insert(0, headers)

                                # إنشاء الجدول
                                col_widths = [5*cm, 3*cm, 3*cm]  # عرض الأعمدة
                                table = Table(data, colWidths=col_widths)

                                # تنسيق الجدول
                                table.setStyle(TableStyle([
                                    ('GRID', (0,0), (-1,-1), 0.5, colors.black),
                                    ('FONTNAME', (0,0), (-1,0), header_font),  # خط العنوان
                                    ('FONTNAME', (0,1), (-1,-1), normal_font),  # خط البيانات
                                    ('FONTSIZE', (0,0), (-1,0), 10),  # حجم خط العنوان
                                    ('FONTSIZE', (0,1), (-1,-1), 9),  # حجم خط البيانات
                                    ('BACKGROUND', (0,0), (-1,0), colors.lightgrey),  # لون خلفية العنوان
                                    ('ALIGN', (0,0), (-1,-1), 'CENTER'),  # محاذاة النص في الوسط
                                    ('VALIGN', (0,0), (-1,-1), 'MIDDLE'),  # محاذاة النص عموديًا في الوسط
                                ]))

                                elements.append(table)
                                elements.append(Spacer(1, 0.3*cm))
                            else:
                                elements.append(Paragraph(reshape_ar("لا توجد فروض مسجلة"), normal_style))
                                elements.append(Spacer(1, 0.2*cm))

                # إضافة توقيع الحارسة العامة
                elements.append(Spacer(1, 1*cm))
                signature_data = [
                    [reshape_ar("توقيع الحارسة العامة")],
                    [""]
                ]
                signature_table = Table(signature_data, colWidths=[200])
                signature_table.setStyle(TableStyle([
                    ('ALIGN', (0,0), (-1,-1), 'CENTER'),
                    ('VALIGN', (0,0), (-1,-1), 'MIDDLE'),
                    ('FONTNAME', (0,0), (-1,-1), header_font),
                    ('FONTSIZE', (0,0), (-1,-1), 12),
                    ('LINEBELOW', (0,1), (0,1), 1, colors.black),
                    ('TOPPADDING', (0,1), (-1,1), 40)
                ]))
                elements.append(signature_table)

                # بناء ملف PDF
                doc.build(elements)

                # فتح الملف
                os.startfile(pdf_path)

            except Exception as e:
                QMessageBox.critical(
                    self,
                    "خطأ",
                    f"حدث خطأ أثناء إنشاء ملف PDF:\n{str(e)}",
                    QMessageBox.Ok
                )
                traceback.print_exc()

        except Exception as e:
            # عرض رسالة خطأ
            error_message = f"حدث خطأ أثناء إنشاء التقرير:\n{str(e)}"
            QMessageBox.critical(
                self,
                "خطأ",
                error_message,
                QMessageBox.Ok
            )
            traceback.print_exc()

    def create_section_report(self, section, level, exams, school_name, school_year, semester, logo_path):
        """إنشاء تقرير الفروض حسب القسم"""
        try:
            # إنشاء مجلد التقارير إذا لم يكن موجودًا
            reports_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "reports")
            if not os.path.exists(reports_dir):
                os.makedirs(reports_dir)

            # إنشاء اسم ملف فريد باستخدام الطابع الزمني
            import time
            timestamp = int(time.time())
            pdf_filename = f"section_exams_report_{section.replace(' ', '_')}_{timestamp}.pdf"
            pdf_path = os.path.join(reports_dir, pdf_filename)

            # التحقق من توفر مكتبات إنشاء PDF
            if not REPORTLAB_AVAILABLE:
                QMessageBox.warning(
                    self,
                    "تنبيه",
                    "المكتبات اللازمة لإنشاء التقارير غير متوفرة.",
                    QMessageBox.Ok
                )
                return

            try:
                # إنشاء ملف PDF
                doc = SimpleDocTemplate(pdf_path, pagesize=A4, rightMargin=0.2*cm, leftMargin=0.2*cm, topMargin=0.2*cm, bottomMargin=0.2*cm)
                elements = []

                # استخدام الخط العربي إذا كان متاحًا
                header_font = 'Arabic' if 'Arabic' in pdfmetrics.getRegisteredFontNames() else 'Helvetica-Bold'
                title_font = 'Arabic' if 'Arabic' in pdfmetrics.getRegisteredFontNames() else 'Helvetica-Bold'
                normal_font = 'Arabic' if 'Arabic' in pdfmetrics.getRegisteredFontNames() else 'Helvetica'

                # أنماط الفقرة
                title_style = ParagraphStyle("title", fontName=title_font, fontSize=16, alignment=1, leading=20)  # 1=CENTER
                header_style = ParagraphStyle("header", fontName=header_font, fontSize=14, alignment=1, leading=18)  # 1=CENTER
                subheader_style = ParagraphStyle("subheader", fontName=header_font, fontSize=12, alignment=1, leading=16)  # 1=CENTER
                section_style = ParagraphStyle("section", fontName=header_font, fontSize=12, alignment=2, leading=16)  # 2=RIGHT
                teacher_style = ParagraphStyle("teacher", fontName=header_font, fontSize=11, alignment=2, leading=14)  # 2=RIGHT
                normal_style = ParagraphStyle("normal", fontName=normal_font, fontSize=10, alignment=2, leading=12)  # 2=RIGHT

                # إضافة شعار المؤسسة
                if logo_path and os.path.exists(logo_path):
                    logo_img = Image(logo_path)
                    logo_img.drawWidth = 100  # تقليل حجم الشعار إلى 100 بكسل عرض
                    logo_img.drawHeight = 50  # تقليل ارتفاع الشعار إلى 50 بكسل
                    elements.append(logo_img)

                # إضافة عنوان التقرير
                elements.append(Paragraph(reshape_ar(school_name), title_style))
                elements.append(Spacer(1, 0.1*cm))
                elements.append(Paragraph(reshape_ar(f"السنة الدراسية: {school_year}"), header_style))
                elements.append(Spacer(1, 0.1*cm))
                elements.append(Paragraph(reshape_ar(f"الأسدس: {semester}"), header_style))
                elements.append(Spacer(1, 0.1*cm))
                elements.append(Paragraph(reshape_ar("تقرير أوراق الفروض الممسوكة"), title_style))
                elements.append(Spacer(1, 0.1*cm))
                elements.append(Paragraph(reshape_ar(f"المستوى: {level}"), subheader_style))
                elements.append(Spacer(1, 0.1*cm))
                elements.append(Paragraph(reshape_ar(f"القسم: {section}"), subheader_style))
                elements.append(Spacer(1, 0.3*cm))

                # تنظيم البيانات حسب الأستاذ ونوع الفرض
                # تجميع الفروض حسب الأستاذ
                exams_by_teacher = {}
                for exam in exams:
                    teacher, exam_type, date, subject = exam
                    if teacher not in exams_by_teacher:
                        exams_by_teacher[teacher] = []
                    exams_by_teacher[teacher].append((exam_type, date, subject))

                # إضافة الفروض للتقرير مرتبة حسب الأستاذ
                for teacher, teacher_exams in exams_by_teacher.items():
                    elements.append(Paragraph(reshape_ar(f"الأستاذ(ة): {teacher}"), teacher_style))
                    elements.append(Spacer(1, 0.1*cm))

                    # ترتيب الفروض حسب نوع الفرض
                    teacher_exams.sort(key=lambda x: x[0])

                    # إنشاء جدول للفروض
                    data = []
                    for exam_type, date, subject in teacher_exams:
                        data.append([
                            reshape_ar(subject),
                            reshape_ar(date),
                            reshape_ar(exam_type)
                        ])

                    if data:
                        # إضافة رؤوس الأعمدة
                        headers = [
                            reshape_ar("المادة"),
                            reshape_ar("تاريخ المسك"),
                            reshape_ar("نوع الفرض")
                        ]
                        data.insert(0, headers)

                        # إنشاء الجدول
                        col_widths = [5*cm, 3*cm, 3*cm]  # عرض الأعمدة
                        table = Table(data, colWidths=col_widths)

                        # تنسيق الجدول
                        table.setStyle(TableStyle([
                            ('GRID', (0,0), (-1,-1), 0.5, colors.black),
                            ('FONTNAME', (0,0), (-1,0), header_font),  # خط العنوان
                            ('FONTNAME', (0,1), (-1,-1), normal_font),  # خط البيانات
                            ('FONTSIZE', (0,0), (-1,0), 10),  # حجم خط العنوان
                            ('FONTSIZE', (0,1), (-1,-1), 9),  # حجم خط البيانات
                            ('BACKGROUND', (0,0), (-1,0), colors.lightgrey),  # لون خلفية العنوان
                            ('ALIGN', (0,0), (-1,-1), 'CENTER'),  # محاذاة النص في الوسط
                            ('VALIGN', (0,0), (-1,-1), 'MIDDLE'),  # محاذاة النص عموديًا في الوسط
                        ]))

                        elements.append(table)
                        elements.append(Spacer(1, 0.3*cm))
                    else:
                        elements.append(Paragraph(reshape_ar("لا توجد فروض مسجلة"), normal_style))
                        elements.append(Spacer(1, 0.2*cm))

                # إضافة توقيع الحارسة العامة
                elements.append(Spacer(1, 1*cm))
                signature_data = [
                    [reshape_ar("توقيع الحارسة العامة")],
                    [""]
                ]
                signature_table = Table(signature_data, colWidths=[200])
                signature_table.setStyle(TableStyle([
                    ('ALIGN', (0,0), (-1,-1), 'CENTER'),
                    ('VALIGN', (0,0), (-1,-1), 'MIDDLE'),
                    ('FONTNAME', (0,0), (-1,-1), header_font),
                    ('FONTSIZE', (0,0), (-1,-1), 12),
                    ('LINEBELOW', (0,1), (0,1), 1, colors.black),
                    ('TOPPADDING', (0,1), (-1,1), 40)
                ]))
                elements.append(signature_table)

                # بناء ملف PDF
                doc.build(elements)

                # فتح الملف
                os.startfile(pdf_path)

            except Exception as e:
                QMessageBox.critical(
                    self,
                    "خطأ",
                    f"حدث خطأ أثناء إنشاء ملف PDF:\n{str(e)}",
                    QMessageBox.Ok
                )
                traceback.print_exc()

        except Exception as e:
            # عرض رسالة خطأ
            error_message = f"حدث خطأ أثناء إنشاء التقرير:\n{str(e)}"
            QMessageBox.critical(
                self,
                "خطأ",
                error_message,
                QMessageBox.Ok
            )
            traceback.print_exc()

    def show_teacher_exams_report(self):
        """عرض تقرير الفروض التي تم مسكها حسب الأستاذ"""
        try:
            # التحقق من اختيار أستاذ
            teacher = self.teacher_combo.currentText()
            subject = self.subject_combo.currentText()

            if not teacher:
                self.show_custom_message("تنبيه", "يرجى اختيار الأستاذ أولاً")
                return

            # الاتصال بقاعدة البيانات
            conn = sqlite3.connect(get_database_path())
            cursor = conn.cursor()

            # استعلام لجلب معلومات المؤسسة
            cursor.execute("""
                SELECT المؤسسة, السنة_الدراسية, الأسدس, ImagePath1
                FROM بيانات_المؤسسة
                LIMIT 1
            """)

            institution_data = cursor.fetchone()
            if institution_data:
                school_name = institution_data[0] or "المؤسسة"
                school_year = institution_data[1] or ""
                semester = institution_data[2] or ""
                logo_path = institution_data[3] or ""
            else:
                school_name = "المؤسسة"
                school_year = ""
                semester = ""
                logo_path = ""

            # استعلام لجلب الفروض المسجلة للأستاذ المحدد، مرتبة حسب المستوى والقسم ونوع الفرض
            cursor.execute("""
                SELECT المستوى, القسم, نوع_الفرض, التاريخ, المادة
                FROM مسك_أوراق_الفروض
                WHERE الأستاذ = ?
                ORDER BY المستوى, القسم, نوع_الفرض, التاريخ
            """, (teacher,))

            exams = cursor.fetchall()
            conn.close()

            if not exams:
                self.show_custom_message("معلومات", f"لا توجد فروض مسجلة للأستاذ {teacher}")
                return

            # إنشاء مجلد التقارير على سطح المكتب
            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
            reports_dir = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة", "أوراق التنقيط")
            if not os.path.exists(reports_dir):
                os.makedirs(reports_dir)

            # إنشاء اسم ملف فريد باستخدام الطابع الزمني
            import time
            timestamp = int(time.time())
            pdf_filename = f"تقرير_الفروض_{teacher.replace(' ', '_')}_{timestamp}.pdf"
            pdf_path = os.path.join(reports_dir, pdf_filename)

            # التحقق من توفر مكتبات إنشاء PDF
            if not REPORTLAB_AVAILABLE:
                QMessageBox.warning(
                    self,
                    "تنبيه",
                    "المكتبات اللازمة لإنشاء التقارير غير متوفرة.",
                    QMessageBox.Ok
                )
                return

            try:
                # إنشاء ملف PDF
                doc = SimpleDocTemplate(pdf_path, pagesize=A4, rightMargin=0.2*cm, leftMargin=0.2*cm, topMargin=0.2*cm, bottomMargin=0.2*cm)
                elements = []

                # استخدام الخط العربي إذا كان متاحًا
                header_font = 'Arabic' if 'Arabic' in pdfmetrics.getRegisteredFontNames() else 'Helvetica-Bold'
                title_font = 'Arabic' if 'Arabic' in pdfmetrics.getRegisteredFontNames() else 'Helvetica-Bold'
                normal_font = 'Arabic' if 'Arabic' in pdfmetrics.getRegisteredFontNames() else 'Helvetica'

                # أنماط الفقرة
                title_style = ParagraphStyle("title", fontName=title_font, fontSize=16, alignment=1, leading=20)  # 1=CENTER
                header_style = ParagraphStyle("header", fontName=header_font, fontSize=14, alignment=1, leading=18)  # 1=CENTER
                subheader_style = ParagraphStyle("subheader", fontName=header_font, fontSize=12, alignment=1, leading=16)  # 1=CENTER
                section_style = ParagraphStyle("section", fontName=header_font, fontSize=12, alignment=2, leading=16)  # 2=RIGHT
                normal_style = ParagraphStyle("normal", fontName=normal_font, fontSize=10, alignment=2, leading=12)  # 2=RIGHT

                # إضافة شعار المؤسسة
                if logo_path and os.path.exists(logo_path):
                    logo_img = Image(logo_path)
                    logo_img.drawWidth = 100  # تقليل حجم الشعار إلى 100 بكسل عرض
                    logo_img.drawHeight = 50  # تقليل ارتفاع الشعار إلى 50 بكسل
                    elements.append(logo_img)

                # إضافة عنوان التقرير
                elements.append(Paragraph(reshape_ar(school_name), title_style))
                elements.append(Spacer(1, 0.1*cm))
                elements.append(Paragraph(reshape_ar(f"السنة الدراسية: {school_year}"), header_style))
                elements.append(Spacer(1, 0.1*cm))
                elements.append(Paragraph(reshape_ar(f"الأسدس: {semester}"), header_style))
                elements.append(Spacer(1, 0.1*cm))
                elements.append(Paragraph(reshape_ar("تقرير أوراق الفروض الممسوكة"), title_style))
                elements.append(Spacer(1, 0.1*cm))
                elements.append(Paragraph(reshape_ar(f"المادة: {subject}"), subheader_style))
                elements.append(Spacer(1, 0.1*cm))
                elements.append(Paragraph(reshape_ar(f"الأستاذ(ة): {teacher}"), subheader_style))
                elements.append(Spacer(1, 0.3*cm))

                # تجميع جميع الفروض في قائمة واحدة مع إضافة القسم
                all_exams_data = []
                for exam in exams:
                    _, section, exam_type, date, subject = exam  # استخدام _ للمتغير غير المستخدم (المستوى)
                    all_exams_data.append((section, exam_type, date, subject))

                # ترتيب الفروض حسب القسم ثم نوع الفرض
                all_exams_data.sort(key=lambda x: (x[0], x[1]))

                # إنشاء جدول واحد لجميع الفروض
                data = []
                for section, exam_type, date, subject in all_exams_data:
                    data.append([
                        reshape_ar(section),  # إضافة القسم كعمود في الجدول
                        reshape_ar(subject),
                        reshape_ar(date),
                        reshape_ar(exam_type)
                    ])

                if data:
                    # إضافة رؤوس الأعمدة
                    headers = [
                        reshape_ar("القسم"),
                        reshape_ar("المادة"),
                        reshape_ar("تاريخ المسك"),
                        reshape_ar("نوع الفرض")
                    ]
                    data.insert(0, headers)

                    # إنشاء الجدول
                    col_widths = [2.5*cm, 4*cm, 3*cm, 2.5*cm]  # عرض الأعمدة
                    table = Table(data, colWidths=col_widths)

                    # تنسيق الجدول
                    table.setStyle(TableStyle([
                        ('GRID', (0,0), (-1,-1), 0.5, colors.black),
                        ('FONTNAME', (0,0), (-1,0), header_font),  # خط العنوان
                        ('FONTNAME', (0,1), (-1,-1), normal_font),  # خط البيانات
                        ('FONTSIZE', (0,0), (-1,0), 10),  # حجم خط العنوان
                        ('FONTSIZE', (0,1), (-1,-1), 9),  # حجم خط البيانات
                        ('BACKGROUND', (0,0), (-1,0), colors.lightgrey),  # لون خلفية العنوان
                        ('ALIGN', (0,0), (-1,-1), 'CENTER'),  # محاذاة النص في الوسط
                        ('VALIGN', (0,0), (-1,-1), 'MIDDLE'),  # محاذاة النص عموديًا في الوسط
                    ]))

                    elements.append(table)
                    elements.append(Spacer(1, 0.3*cm))
                else:
                    elements.append(Paragraph(reshape_ar("لا توجد فروض مسجلة"), normal_style))
                    elements.append(Spacer(1, 0.2*cm))

                # إضافة توقيعات الأستاذ(ة) والحارسة العامة
                elements.append(Spacer(1, 1*cm))
                signature_data = [
                    [reshape_ar("و(ة)"), "", reshape_ar("توقيع الحارسة العامة")],
                    ["", "", ""]
                ]
                signature_table = Table(signature_data, colWidths=[200, 100, 200])
                signature_table.setStyle(TableStyle([
                    ('ALIGN', (0,0), (-1,-1), 'CENTER'),
                    ('VALIGN', (0,0), (-1,-1), 'MIDDLE'),
                    ('FONTNAME', (0,0), (-1,-1), header_font),
                    ('FONTSIZE', (0,0), (-1,-1), 12),
                    ('LINEBELOW', (0,1), (0,1), 1, colors.black),
                    ('LINEBELOW', (2,1), (2,1), 1, colors.black),
                    ('TOPPADDING', (0,1), (-1,1), 40)
                ]))
                elements.append(signature_table)

                # بناء ملف PDF
                doc.build(elements)

                # فتح الملف
                os.startfile(pdf_path)

            except Exception as e:
                QMessageBox.critical(
                    self,
                    "خطأ",
                    f"حدث خطأ أثناء إنشاء ملف PDF:\n{str(e)}",
                    QMessageBox.Ok
                )
                traceback.print_exc()

        except Exception as e:
            # عرض رسالة خطأ
            error_message = f"حدث خطأ أثناء إنشاء التقرير:\n{str(e)}"
            QMessageBox.critical(
                self,
                "خطأ",
                error_message,
                QMessageBox.Ok
            )
            traceback.print_exc()





# تشغيل التطبيق كنافذة مستقلة للاختبار
if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)  # تعيين اتجاه التخطيط من اليمين إلى اليسار
    window = CreateAbsenceTableWindow()
    window.show()
    sys.exit(app.exec_())
