import sys
import os
import sqlite3
from datetime import datetime
from database_config import get_database_path, get_database_connection
from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QPushButton, QVBoxLayout, QHBoxLayout, QFrame, QSpacerItem, QSizePolicy, QBoxLayout, QMessageBox
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QColor, QPalette

# استيراد المكتبات اللازمة لإنشاء ملفات PDF
try:
    from reportlab.lib.pagesizes import A4
    from reportlab.pdfgen import canvas
    from reportlab.pdfbase.ttfonts import TTFont
    from reportlab.pdfbase import pdfmetrics
    from reportlab.lib.units import cm
    from reportlab.lib import colors
    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle

    # استيراد دعم العربية
    import arabic_reshaper
    from bidi.algorithm import get_display

    REPORTLAB_AVAILABLE = True
except ImportError as e:
    print(f"خطأ في استيراد مكتبات إنشاء التقارير: {e}")
    REPORTLAB_AVAILABLE = False

def reshape_ar(text):
    """إعادة تشكيل النص العربي للعرض الصحيح في PDF"""
    try:
        return get_display(arabic_reshaper.reshape(str(text)))
    except:
        return text

def print_absence_report(parent, section, month, model=None):
    """
    طباعة تقرير الغياب الشهري للقسم والشهر المحددين

    المعلمات:
    - parent: النافذة الأم (للعرض رسائل الخطأ)
    - section: القسم المحدد
    - month: الشهر المحدد
    - model: نموذج البيانات (اختياري)

    العائد:
    - True إذا تم إنشاء التقرير بنجاح، False خلاف ذلك
    """
    if not REPORTLAB_AVAILABLE:
        parent.show_message("خطأ", "المكتبات اللازمة لإنشاء التقارير غير متوفرة.", QMessageBox.Critical)
        return False

    if not section or not month:
        parent.show_message("تنبيه", "الرجاء اختيار القسم والشهر أولاً.", QMessageBox.Warning)
        return False

    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(get_database_path())
        cur = conn.cursor()

        # الحصول على بيانات المؤسسة
        cur.execute("SELECT المؤسسة, ImagePath1, السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
        inst = cur.fetchone()
        inst_name, logo_path, school_year = inst if inst else ("المؤسسة", None, "")

        # الحصول على بيانات الغياب للقسم والشهر المحددين
        # طباعة قيم المعلمات للتشخيص
        print(f"معلمات الاستعلام: الشهر={month}, القسم={section}, السنة الدراسية={school_year}")

        # تشخيص: التحقق من وجود بيانات في جدول مسك_الغياب_الأسبوعي
        cur.execute("SELECT COUNT(*) FROM مسك_الغياب_الأسبوعي WHERE الشهر = ? AND السنة_الدراسية = ?", (month, school_year))
        count = cur.fetchone()[0]
        print(f"عدد سجلات الغياب في جدول مسك_الغياب_الأسبوعي للشهر {month} والسنة الدراسية {school_year}: {count}")

        # تشخيص: التحقق من وجود بيانات في جدول اللوائح
        cur.execute("SELECT COUNT(*) FROM اللوائح WHERE القسم = ? AND السنة_الدراسية = ?", (section, school_year))
        count = cur.fetchone()[0]
        print(f"عدد سجلات الطلاب في جدول اللوائح للقسم {section} والسنة الدراسية {school_year}: {count}")

        # تشخيص: التحقق من بيانات الطالب المحدد H142106728
        student_code = "H142106728"
        print(f"\n=== تشخيص بيانات الطالب {student_code} ===")

        # التحقق من وجود الطالب في جدول اللوائح
        cur.execute("""
            SELECT رت, الرمز, القسم, السنة_الدراسية
            FROM اللوائح
            WHERE الرمز = ?
        """, (student_code,))
        student_in_lists = cur.fetchall()
        print(f"عدد سجلات الطالب {student_code} في جدول اللوائح: {len(student_in_lists)}")
        if student_in_lists:
            for record in student_in_lists:
                print(f"  سجل في اللوائح: {record}")

        # التحقق من وجود الطالب في جدول اللوائح للقسم والسنة الدراسية المحددين
        cur.execute("""
            SELECT رت, الرمز, القسم, السنة_الدراسية
            FROM اللوائح
            WHERE الرمز = ? AND القسم = ? AND السنة_الدراسية = ?
        """, (student_code, section, school_year))
        student_in_section = cur.fetchall()
        print(f"عدد سجلات الطالب {student_code} في جدول اللوائح للقسم {section} والسنة الدراسية {school_year}: {len(student_in_section)}")
        if student_in_section:
            for record in student_in_section:
                print(f"  سجل في القسم: {record}")

        # التحقق من وجود بيانات الغياب للطالب في جدول مسك_الغياب_الأسبوعي
        cur.execute("""
            SELECT id, السنة_الدراسية, الشهر, رمز_التلميذ, "1", "2", "3", "4", "5", مجموع_شهري
            FROM مسك_الغياب_الأسبوعي
            WHERE رمز_التلميذ = ?
        """, (student_code,))
        student_absence = cur.fetchall()
        print(f"عدد سجلات الغياب للطالب {student_code} في جدول مسك_الغياب_الأسبوعي: {len(student_absence)}")
        if student_absence:
            for record in student_absence:
                print(f"  سجل غياب: {record}")

        # التحقق من وجود بيانات الغياب للطالب في جدول مسك_الغياب_الأسبوعي للشهر والسنة الدراسية المحددين
        cur.execute("""
            SELECT id, السنة_الدراسية, الشهر, رمز_التلميذ, "1", "2", "3", "4", "5", مجموع_شهري
            FROM مسك_الغياب_الأسبوعي
            WHERE رمز_التلميذ = ? AND الشهر = ? AND السنة_الدراسية = ?
        """, (student_code, month, school_year))
        student_absence_month = cur.fetchall()
        print(f"عدد سجلات الغياب للطالب {student_code} في جدول مسك_الغياب_الأسبوعي للشهر {month} والسنة الدراسية {school_year}: {len(student_absence_month)}")
        if student_absence_month:
            for record in student_absence_month:
                print(f"  سجل غياب للشهر: {record}")

        # تجربة استعلام مباشر للتحقق من نتائج الانضمام
        print("\n=== تجربة استعلام الانضمام للطالب المحدد ===")
        test_query = """
            SELECT l.رت, sg.الاسم_والنسب, l.الرمز,
                   mag."1", mag."2", mag."3", mag."4", mag."5",
                   mag.مجموع_شهري, mag.الغياب_المبرر, mag.ملاحظات, mag.بداية_الشهر
            FROM اللوائح l
            LEFT JOIN مسك_الغياب_الأسبوعي mag ON l.الرمز = mag.رمز_التلميذ
                AND mag.الشهر = ?
                AND mag.السنة_الدراسية = ?
            LEFT JOIN السجل_العام sg ON l.الرمز = sg.الرمز
            JOIN بيانات_المؤسسة inst ON l.السنة_الدراسية = inst.السنة_الدراسية
            WHERE l.الرمز = ? AND l.السنة_الدراسية = ?
        """
        cur.execute(test_query, (month, school_year, student_code, school_year))
        join_result = cur.fetchall()
        print(f"نتائج استعلام الانضمام للطالب {student_code}: {len(join_result)}")
        if join_result:
            for idx, record in enumerate(join_result):
                print(f"  نتيجة {idx+1}:")
                print(f"    رت: {record[0]}")
                print(f"    الاسم والنسب: {record[1]}")
                print(f"    الرمز: {record[2]}")
                print(f"    الأسبوع 1: {record[3]}, النوع: {type(record[3])}")
                print(f"    الأسبوع 2: {record[4]}, النوع: {type(record[4])}")
                print(f"    الأسبوع 3: {record[5]}, النوع: {type(record[5])}")
                print(f"    الأسبوع 4: {record[6]}, النوع: {type(record[6])}")
                print(f"    الأسبوع 5: {record[7]}, النوع: {type(record[7])}")
                print(f"    المجموع الشهري: {record[8]}, النوع: {type(record[8])}")
                print(f"    الغياب المبرر: {record[9]}, النوع: {type(record[9])}")
                print(f"    ملاحظات: {record[10]}, النوع: {type(record[10])}")
                print(f"    بداية الشهر: {record[11]}, النوع: {type(record[11])}")

        # تصفية جدول اللوائح حسب السنة الدراسية والقسم
        # التأكد من أن السنة الدراسية في جدول اللوائح تتطابق مع السنة الدراسية في جدول بيانات_المؤسسة
        # وأيضًا تصفية جدول مسك_الغياب_الأسبوعي حسب السنة الدراسية
        query = """
            SELECT l.رت, sg.الاسم_والنسب, l.الرمز,
                   mag."1", mag."2", mag."3", mag."4", mag."5",
                   mag.مجموع_شهري, mag.الغياب_المبرر, mag.ملاحظات, mag.بداية_الشهر
            FROM اللوائح l
            LEFT JOIN مسك_الغياب_الأسبوعي mag ON l.الرمز = mag.رمز_التلميذ
                AND mag.الشهر = ?
                AND mag.السنة_الدراسية = ?
            LEFT JOIN السجل_العام sg ON l.الرمز = sg.الرمز
            JOIN بيانات_المؤسسة inst ON l.السنة_الدراسية = inst.السنة_الدراسية
            WHERE l.القسم = ? AND l.السنة_الدراسية = ?
            ORDER BY CAST(l.رت AS INTEGER)
        """
        print(f"الاستعلام المستخدم: {query}")
        print(f"المعاملات: month={month}, school_year={school_year}, section={section}")

        cur.execute(query, (month, school_year, section, school_year))

        rows = cur.fetchall()
        print(f"عدد الصفوف المسترجعة من الاستعلام: {len(rows)}")

        # تشخيص: عرض عينة من البيانات المسترجعة
        if rows:
            print(f"عينة من البيانات المسترجعة (أول 3 صفوف):")
            for i, row in enumerate(rows[:3]):
                print(f"الصف {i+1}: {row}")
                print(f"  رت: {row[0]}")
                print(f"  الاسم والنسب: {row[1]}")
                print(f"  الرمز: {row[2]}")
                print(f"  الأسبوع 1: {row[3]}")
                print(f"  الأسبوع 2: {row[4]}")
                print(f"  الأسبوع 3: {row[5]}")
                print(f"  الأسبوع 4: {row[6]}")
                print(f"  الأسبوع 5: {row[7]}")
                print(f"  المجموع الشهري: {row[8]}")
                print(f"  الغياب المبرر: {row[9]}")
                print(f"  ملاحظات: {row[10]}")
                print(f"  بداية الشهر: {row[11]}")

        if not rows:
            parent.show_message("تنبيه", f"لا توجد بيانات غياب للقسم {section} في شهر {month}.", QMessageBox.Warning)
            conn.close()
            return False

        # إنشاء ملف PDF
        file_path = f"تقرير_الغياب_{section}_{month}.pdf"
        doc = SimpleDocTemplate(file_path, pagesize=A4, rightMargin=0.2*cm, leftMargin=0.2*cm, topMargin=0.5*cm, bottomMargin=0.5*cm)
        elements = []

        # إضافة شعار المؤسسة والعنوان
        if logo_path and os.path.exists(logo_path):
            img = Image(logo_path)
            img.drawWidth = 250
            img.drawHeight = 80
            elements.append(img)

        elements.append(Spacer(1, 0.3*cm))
        elements.append(Paragraph(reshape_ar(inst_name), ParagraphStyle('header', fontName='Helvetica-Bold', fontSize=15, alignment=1)))
        elements.append(Spacer(1, 0.3*cm))

        # عنوان التقرير
        title = f"تقرير الغياب الشهري - قسم {section} - شهر {month} - السنة الدراسية {school_year}"
        elements.append(Paragraph(reshape_ar(title), ParagraphStyle('title', fontName='Helvetica-Bold', fontSize=14, alignment=1)))
        elements.append(Spacer(1, 0.5*cm))

        # الحصول على تاريخ بداية الشهر وحساب تواريخ بداية الأسابيع
        month_start_date = rows[0][11] if rows[0][11] else ""
        print(f"تاريخ بداية الشهر: {month_start_date}")
        week_dates = []

        if month_start_date:
            try:
                # تشخيص: محاولة استيراد دالة calc_week_dates من sub9_window
                print(f"محاولة استيراد دالة calc_week_dates من sub9_window...")
                try:
                    from sub9_window import calc_week_dates
                    print(f"تم استيراد دالة calc_week_dates من sub9_window بنجاح")
                except ImportError as e:
                    print(f"فشل استيراد دالة calc_week_dates من sub9_window: {e}")
                    # إذا فشل الاستيراد، نستخدم دالة بديلة
                    def calc_week_dates(start_date_str):
                        """دالة بديلة لحساب تواريخ الأسابيع"""
                        return ["" for _ in range(5)]
                    print(f"تم استخدام دالة calc_week_dates البديلة")

                # تشخيص: حساب تواريخ بداية الأسابيع
                print(f"حساب تواريخ بداية الأسابيع باستخدام تاريخ البداية: {month_start_date}")
                week_dates = calc_week_dates(month_start_date)
                print(f"تواريخ بداية الأسابيع: {week_dates}")
            except Exception as e:
                print(f"خطأ في حساب تواريخ الأسابيع: {e}")
                import traceback
                traceback.print_exc()
                week_dates = ["" for _ in range(5)]
        else:
            print(f"تاريخ بداية الشهر غير موجود، استخدام قيم فارغة لتواريخ الأسابيع")
            week_dates = ["" for _ in range(5)]

        # إنشاء عناوين الأسابيع مع التواريخ
        week_headers = []
        for i in range(5):
            date_str = week_dates[i] if i < len(week_dates) else ""
            if date_str:
                week_headers.append(f"الأسبوع {i+1}\n{date_str}")
            else:
                week_headers.append(f"الأسبوع {i+1}")

        print(f"عناوين الأسابيع: {week_headers}")

        # إنشاء جدول البيانات
        headers = [
            reshape_ar("رت"),
            reshape_ar("الاسم والنسب"),
            reshape_ar("رمز التلميذ"),
            reshape_ar(week_headers[0]),
            reshape_ar(week_headers[1]),
            reshape_ar(week_headers[2]),
            reshape_ar(week_headers[3]),
            reshape_ar(week_headers[4]),
            reshape_ar("المجموع"),
            reshape_ar("الغياب المبرر"),
            reshape_ar("ملاحظات")
        ]

        data = [headers]
        print(f"تم إنشاء رؤوس الجدول: {headers}")

        # تشخيص: معالجة البيانات وإضافتها إلى التقرير
        print(f"بدء معالجة البيانات وإضافتها إلى التقرير...")
        for idx, row in enumerate(rows):
            # تشخيص: عرض قيم الأسابيع قبل المعالجة
            if idx < 3:  # عرض أول 3 صفوف فقط للتشخيص
                print(f"معالجة الصف {idx+1}:")
                print(f"  الأسبوع 1 (قبل المعالجة): '{row[3]}', النوع: {type(row[3])}")
                print(f"  الأسبوع 2 (قبل المعالجة): '{row[4]}', النوع: {type(row[4])}")
                print(f"  الأسبوع 3 (قبل المعالجة): '{row[5]}', النوع: {type(row[5])}")
                print(f"  الأسبوع 4 (قبل المعالجة): '{row[6]}', النوع: {type(row[6])}")
                print(f"  الأسبوع 5 (قبل المعالجة): '{row[7]}', النوع: {type(row[7])}")
                print(f"  المجموع (قبل المعالجة): '{row[8]}', النوع: {type(row[8])}")

            # تشخيص إضافي للطالب H142106728
            if row[2] == "H142106728":
                print(f"\n=== تشخيص معالجة بيانات الطالب H142106728 ===")
                print(f"  الأسبوع 1 (قبل المعالجة): '{row[3]}', النوع: {type(row[3])}")
                print(f"  الأسبوع 2 (قبل المعالجة): '{row[4]}', النوع: {type(row[4])}")
                print(f"  الأسبوع 3 (قبل المعالجة): '{row[5]}', النوع: {type(row[5])}")
                print(f"  الأسبوع 4 (قبل المعالجة): '{row[6]}', النوع: {type(row[6])}")
                print(f"  الأسبوع 5 (قبل المعالجة): '{row[7]}', النوع: {type(row[7])}")
                print(f"  المجموع (قبل المعالجة): '{row[8]}', النوع: {type(row[8])}")

            # تحسين معالجة القيم لتجنب استبدال القيم الصفرية
            # استخدام is not None بدلاً من or لتجنب استبدال القيم الصفرية
            week1_val = str(row[3] if row[3] is not None else 0)
            week2_val = str(row[4] if row[4] is not None else 0)
            week3_val = str(row[5] if row[5] is not None else 0)
            week4_val = str(row[6] if row[6] is not None else 0)
            week5_val = str(row[7] if row[7] is not None else 0)
            total_val = str(row[8] if row[8] is not None else 0)
            justified_val = str(row[9] if row[9] is not None else 0)

            # تشخيص: عرض القيم بعد المعالجة
            if idx < 3:  # عرض أول 3 صفوف فقط للتشخيص
                print(f"  الأسبوع 1 (بعد المعالجة): '{week1_val}'")
                print(f"  الأسبوع 2 (بعد المعالجة): '{week2_val}'")
                print(f"  الأسبوع 3 (بعد المعالجة): '{week3_val}'")
                print(f"  الأسبوع 4 (بعد المعالجة): '{week4_val}'")
                print(f"  الأسبوع 5 (بعد المعالجة): '{week5_val}'")
                print(f"  المجموع (بعد المعالجة): '{total_val}'")

            # تشخيص إضافي للطالب H142106728
            if row[2] == "H142106728":
                print(f"  الأسبوع 1 (بعد المعالجة): '{week1_val}'")
                print(f"  الأسبوع 2 (بعد المعالجة): '{week2_val}'")
                print(f"  الأسبوع 3 (بعد المعالجة): '{week3_val}'")
                print(f"  الأسبوع 4 (بعد المعالجة): '{week4_val}'")
                print(f"  الأسبوع 5 (بعد المعالجة): '{week5_val}'")
                print(f"  المجموع (بعد المعالجة): '{total_val}'")

            data_row = [
                reshape_ar(str(row[0])),  # رت
                reshape_ar(str(row[1])),  # الاسم والنسب
                reshape_ar(str(row[2])),  # الرمز
                reshape_ar(week1_val),  # الأسبوع 1
                reshape_ar(week2_val),  # الأسبوع 2
                reshape_ar(week3_val),  # الأسبوع 3
                reshape_ar(week4_val),  # الأسبوع 4
                reshape_ar(week5_val),  # الأسبوع 5
                reshape_ar(total_val),  # المجموع
                reshape_ar(justified_val),  # الغياب المبرر
                reshape_ar(str(row[10] or ""))  # ملاحظات
            ]
            data.append(data_row)

        # عكس ترتيب البيانات للعرض من اليمين إلى اليسار
        data = [list(reversed(row)) for row in data]
        print(f"تم عكس ترتيب البيانات للعرض من اليمين إلى اليسار")

        # تعيين أبعاد الأعمدة
        col_widths = [80, 60, 60, 40, 40, 40, 40, 40, 60, 120, 40]  # عرض كل عمود
        col_widths = list(reversed(col_widths))  # عكس ترتيب الأعمدة
        print(f"أبعاد الأعمدة بعد العكس: {col_widths}")

        # إنشاء الجدول
        print(f"إنشاء جدول التقرير...")
        table = Table(data, colWidths=col_widths, repeatRows=1)
        table.setStyle(TableStyle([
            ('GRID', (0,0), (-1,-1), 0.5, colors.black),
            ('FONTNAME', (0,0), (-1,-1), 'Helvetica'),
            ('FONTSIZE', (0,0), (-1,-1), 10),
            ('BACKGROUND', (0,0), (-1,0), colors.lightgrey),
            ('ALIGN', (0,0), (-1,-1), 'RIGHT'),
            ('VALIGN', (0,0), (-1,-1), 'MIDDLE')
        ]))
        print(f"تم تطبيق التنسيق على الجدول")

        elements.append(table)
        print(f"تم إضافة الجدول إلى عناصر التقرير")

        # إضافة تاريخ إنشاء التقرير
        elements.append(Spacer(1, 0.5*cm))
        current_date = datetime.now().strftime("%Y-%m-%d %H:%M")
        date_text = f"تاريخ إنشاء التقرير: {current_date}"
        elements.append(Paragraph(reshape_ar(date_text), ParagraphStyle('date', fontName='Helvetica', fontSize=10, alignment=1)))
        print(f"تم إضافة تاريخ إنشاء التقرير: {current_date}")

        # بناء ملف PDF
        print(f"بدء بناء ملف PDF: {file_path}")
        doc.build(elements)
        print(f"تم بناء ملف PDF بنجاح")

        # فتح الملف
        print(f"محاولة فتح الملف: {doc.filename}")
        os.startfile(doc.filename)
        print(f"تم فتح الملف بنجاح")

        # إغلاق الاتصال بقاعدة البيانات
        conn.close()
        print(f"تم إغلاق الاتصال بقاعدة البيانات")

        # عرض رسالة نجاح
        parent.show_message("نجاح", f"تم إنشاء تقرير الغياب الشهري للقسم {section} وشهر {month} بنجاح.", QMessageBox.Information)
        print(f"تم عرض رسالة نجاح للمستخدم")

        return True

    except Exception as e:
        import traceback
        traceback.print_exc()
        parent.show_message("خطأ", f"حدث خطأ أثناء إنشاء تقرير الغياب الشهري:\n{str(e)}", QMessageBox.Critical)
        return False

def print_semester_report(parent, section):
    """
    طباعة تقرير الغياب للأسدس الأول للقسم المحدد

    المعلمات:
    - parent: النافذة الأم (للعرض رسائل الخطأ)
    - section: القسم المحدد

    العائد:
    - True إذا تم إنشاء التقرير بنجاح، False خلاف ذلك
    """
    if not REPORTLAB_AVAILABLE:
        parent.show_message("خطأ", "المكتبات اللازمة لإنشاء التقارير غير متوفرة.", QMessageBox.Critical)
        return False

    if not section:
        parent.show_message("تنبيه", "الرجاء اختيار القسم أولاً.", QMessageBox.Warning)
        return False

    try:
        # استيراد os في نطاق الدالة
        import os

        # تسجيل الخط العربي
        try:
            pdfmetrics.registerFont(TTFont("Arabic", "arial.ttf"))
            print("تم تسجيل الخط العربي بنجاح")
        except Exception as font_error:
            print(f"خطأ في تسجيل الخط العربي: {font_error}")
            # محاولة تسجيل الخط من مسار آخر
            try:
                font_paths = [
                    "arial.ttf",
                    "c:/windows/fonts/arial.ttf",
                    "c:/windows/fonts/arialbd.ttf",
                    "/usr/share/fonts/truetype/msttcorefonts/Arial.ttf"
                ]

                for font_path in font_paths:
                    if os.path.exists(font_path):
                        pdfmetrics.registerFont(TTFont("Arabic", font_path))
                        print(f"تم تسجيل الخط العربي من المسار البديل: {font_path}")
                        break
            except Exception as alt_font_error:
                print(f"فشل تسجيل الخط العربي من المسارات البديلة: {alt_font_error}")

        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(get_database_path())
        cur = conn.cursor()

        # الحصول على بيانات المؤسسة
        cur.execute("SELECT المؤسسة, ImagePath1, السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
        inst = cur.fetchone()
        inst_name, logo_path, school_year = inst if inst else ("المؤسسة", None, "")

        # الحصول على إعدادات البرنامج
        cur.execute("""
            SELECT نقطة_السلوك, معامل_السلوك, نقطة_المواظبة, معامل_المواظبة, المخالفة_الاولى, المخالفة_الثانية, المخالفة_الثالثة
            FROM اعدادات_البرنامج
            LIMIT 1
        """)
        settings = cur.fetchone()
        if not settings:
            settings = (10, 0.5, 10, 0.5, 1, 2, 3)  # قيم افتراضية

        نقطة_السلوك, معامل_السلوك, نقطة_المواظبة, معامل_المواظبة, المخالفة_الاولى, المخالفة_الثانية, المخالفة_الثالثة = settings

        # الحصول على بيانات الغياب للقسم والأسدس الأول من جدول غياب_الأسدس_الأول
        cur.execute("""
            SELECT رت, الاسم_والنسب, رمز_التلميذ,
                   شتنبر, أكتوبر, نونبر, دجنبر, يناير,
                   الغياب_المبرر, المخالفات,
                   مجموع_الغياب_الدوري, مجموع_الغياب_غير_المبرر
            FROM غياب_الأسدس_الأول
            WHERE القسم = ?
            ORDER BY CAST(رت AS INTEGER)
        """, (section,))

        rows = cur.fetchall()

        if not rows:
            parent.show_message("تنبيه", f"لا توجد بيانات غياب للقسم {section} في الأسدس الأول.", QMessageBox.Warning)
            conn.close()
            return False

        # إنشاء ملف PDF
        file_path = f"تقرير_الغياب_الأسدس_الأول_{section}.pdf"
        doc = SimpleDocTemplate(file_path, pagesize=A4, rightMargin=0.2*cm, leftMargin=0.2*cm, topMargin=0.2*cm, bottomMargin=0.2*cm)
        elements = []

        # إضافة شعار المؤسسة والعنوان
        if logo_path and os.path.exists(logo_path):
            img = Image(logo_path)
            img.drawWidth = 250
            img.drawHeight = 80
            elements.append(img)

        elements.append(Spacer(1, 0.3*cm))

        # استخدام الخط العربي المسجل إذا كان متاحًا
        try:
            header_style = ParagraphStyle('header', fontName='Arabic', fontSize=15, alignment=1)
        except:
            header_style = ParagraphStyle('header', fontName='Helvetica-Bold', fontSize=15, alignment=1)

        elements.append(Paragraph(reshape_ar(inst_name), header_style))
        elements.append(Spacer(1, 0.3*cm))

        # عنوان التقرير
        title = f"تقرير الغياب للأسدس الأول - قسم {section} - السنة الدراسية {school_year}"

        try:
            title_style = ParagraphStyle('title', fontName='Arabic', fontSize=14, alignment=1)
        except:
            title_style = ParagraphStyle('title', fontName='Helvetica-Bold', fontSize=14, alignment=1)

        elements.append(Paragraph(reshape_ar(title), title_style))
        elements.append(Spacer(1, 0.5*cm))

        # إنشاء جدول البيانات
        headers = [
            reshape_ar("رت"),
            reshape_ar("الاسم\nوالنسب"),
            reshape_ar("شتنبر"),
            reshape_ar("أكتوبر"),
            reshape_ar("نونبر"),
            reshape_ar("دجنبر"),
            reshape_ar("يناير"),
            reshape_ar("المجموع"),
            reshape_ar("غياب\nمبرر"),
            reshape_ar("غياب\nغير مبرر"),
            reshape_ar("المخالفات"),
            reshape_ar("نقطة\nالسلوك"),
            reshape_ar("نقطة\nالمواظبة")
        ]

        data = [headers]

        for row in rows:
            # طباعة محتويات الصف للتشخيص
            print(f"محتويات الصف: {row}")

            # استخراج البيانات من الصف
            # row[0]: رت
            # row[1]: الاسم والنسب
            # row[2]: رمز_التلميذ
            # row[3]: شتنبر
            # row[4]: أكتوبر
            # row[5]: نونبر
            # row[6]: دجنبر
            # row[7]: يناير
            # row[8]: الغياب_المبرر
            # row[9]: المخالفات
            # row[10]: مجموع_الغياب_الدوري
            # row[11]: مجموع_الغياب_غير_المبرر

            # استخدام المجموع المحسوب مسبقًا
            total = row[10] or 0
            if isinstance(total, str):
                total = int(total) if total.isdigit() else 0

            # استخدام الغياب المبرر المحسوب مسبقًا
            justified_absence = row[8] or 0
            if isinstance(justified_absence, str):
                justified_absence = int(justified_absence) if justified_absence.isdigit() else 0

            # استخدام الغياب غير المبرر المحسوب مسبقًا
            unjustified_absence = row[11] or 0
            if isinstance(unjustified_absence, str):
                unjustified_absence = int(unjustified_absence) if unjustified_absence.isdigit() else 0

            # حساب نقطة السلوك وفقاً للمعادلة المطلوبة
            try:
                # استخدام عدد المخالفات المحسوب مسبقًا
                violations_count = row[9] or 0  # المخالفات في الموضع 9
                if isinstance(violations_count, str):
                    violations_count = int(violations_count) if violations_count.isdigit() else 0

                # طباعة القيم المستخدمة في حساب نقطة السلوك للتشخيص
                print(f"حساب نقطة السلوك للطالب {row[1]}:")
                print(f"  نقطة_السلوك من اعدادات_البرنامج: {نقطة_السلوك}")
                print(f"  عدد المخالفات: {violations_count}")
                print(f"  المخالفة_الاولى: {المخالفة_الاولى}")
                print(f"  المخالفة_الثانية: {المخالفة_الثانية}")
                print(f"  المخالفة_الثالثة: {المخالفة_الثالثة}")

                # حساب نقطة السلوك وفقاً للمعادلة المطلوبة
                if violations_count == 1:
                    behavior_score = نقطة_السلوك - المخالفة_الاولى
                elif violations_count == 2:
                    behavior_score = نقطة_السلوك - (المخالفة_الاولى + المخالفة_الثانية)
                elif violations_count >= 3:
                    behavior_score = نقطة_السلوك - (المخالفة_الاولى + المخالفة_الثانية + المخالفة_الثالثة)
                else:
                    behavior_score = نقطة_السلوك  # لا توجد مخالفات

                behavior_score = max(0, round(behavior_score, 2))  # لا تقل عن صفر
                print(f"  نقطة السلوك المحسوبة: {behavior_score}")
            except (ValueError, TypeError) as e:
                print(f"خطأ في حساب نقطة السلوك: {e}")
                behavior_score = float(نقطة_السلوك)  # قيمة افتراضية في حالة حدوث خطأ

            # تم حساب عدد المخالفات مسبقًا في كتلة حساب نقطة السلوك
            print(f"عدد المخالفات للطالب {row[1]}: {violations_count}")

            # حساب نقطة المواظبة وفقاً للصيغة المطلوبة
            try:
                # طباعة القيم المستخدمة في حساب نقطة المواظبة للتشخيص
                print(f"حساب نقطة المواظبة للطالب {row[1]}:")
                print(f"  نقطة_المواظبة من اعدادات_البرنامج: {نقطة_المواظبة}")
                print(f"  مجموع_الغياب_غير_المبرر من غياب_الأسدس_الأول: {unjustified_absence}")
                print(f"  معامل_المواظبة من اعدادات_البرنامج: {معامل_المواظبة}")

                # حساب نقطة المواظبة وفقاً للصيغة المطلوبة
                attendance_score = float(نقطة_المواظبة) - (float(unjustified_absence) * float(معامل_المواظبة))
                attendance_score = max(0, round(attendance_score, 2))  # لا تقل عن صفر
                print(f"  نقطة المواظبة المحسوبة: {attendance_score}")
            except (ValueError, TypeError) as e:
                print(f"خطأ في حساب نقطة المواظبة: {e}")
                attendance_score = float(نقطة_المواظبة)  # قيمة افتراضية في حالة حدوث خطأ

            data_row = [
                reshape_ar(str(row[0])),  # رت
                reshape_ar(str(row[1])),  # الاسم والنسب
                reshape_ar(str(row[3] or 0)),  # شتنبر
                reshape_ar(str(row[4] or 0)),  # أكتوبر
                reshape_ar(str(row[5] or 0)),  # نونبر
                reshape_ar(str(row[6] or 0)),  # دجنبر
                reshape_ar(str(row[7] or 0)),  # يناير
                reshape_ar(str(total)),  # المجموع
                reshape_ar(str(justified_absence)),  # الغياب المبرر
                reshape_ar(str(unjustified_absence)),  # الغياب غير المبرر
                reshape_ar(str(violations_count)),  # المخالفات
                reshape_ar(str(behavior_score)),  # نقطة السلوك
                reshape_ar(str(attendance_score))  # نقطة المواظبة
            ]
            data.append(data_row)

        # عكس ترتيب البيانات للعرض من اليمين إلى اليسار
        data = [list(reversed(row)) for row in data]

        # تعيين أبعاد الأعمدة حسب الطلب
        col_widths = [
            40,  # نقطة المواظبة
            40,  # نقطة السلوك
            40,  # المخالفات
            40,  # الغياب غير المبرر
            40,  # الغياب المبرر
            40,  # المجموع
            35,  # يناير
            35,  # دجنبر
            35,  # نونبر
            35,  # أكتوبر
            35,  # شتنبر
            100, # الاسم والنسب
            25   # رت
        ]  # عرض كل عمود

        # إنشاء الجدول مع تحديد ارتفاع الصفوف
        # تحديد ارتفاع الصف الأول (العناوين) وباقي الصفوف
        row_heights = [35] + [14] * (len(data) - 1)

        table = Table(data, colWidths=col_widths, repeatRows=1, rowHeights=row_heights)

        # تحديد الخط المستخدم في الجدول
        table_font = 'Arabic' if 'Arabic' in pdfmetrics.getRegisteredFontNames() else 'Helvetica'
        print(f"الخطوط المسجلة: {pdfmetrics.getRegisteredFontNames()}")

        table.setStyle(TableStyle([
            ('GRID', (0,0), (-1,-1), 0.5, colors.black),
            ('FONTNAME', (0,0), (-1,-1), table_font),
            ('FONTSIZE', (0,0), (-1,-1), 10),
            ('BACKGROUND', (0,0), (-1,0), colors.lightgrey),
            ('ALIGN', (0,0), (-1,-1), 'RIGHT'),
            ('VALIGN', (0,0), (-1,-1), 'MIDDLE'),
            # تحسين تنسيق الخلايا
            ('LEFTPADDING', (0,0), (-1,-1), 3),
            ('RIGHTPADDING', (0,0), (-1,-1), 3),
            ('TOPPADDING', (0,0), (-1,-1), 1),
            ('BOTTOMPADDING', (0,0), (-1,-1), 1)
        ]))

        elements.append(table)

        # إضافة تاريخ إنشاء التقرير
        elements.append(Spacer(1, 0.5*cm))
        current_date = datetime.now().strftime("%Y-%m-%d %H:%M")
        date_text = f"تاريخ إنشاء التقرير: {current_date}"

        try:
            date_style = ParagraphStyle('date', fontName='Arabic', fontSize=10, alignment=1)
        except:
            date_style = ParagraphStyle('date', fontName='Helvetica', fontSize=10, alignment=1)

        elements.append(Paragraph(reshape_ar(date_text), date_style))

        # بناء ملف PDF
        doc.build(elements)

        # فتح الملف
        os.startfile(doc.filename)

        # إغلاق الاتصال بقاعدة البيانات
        conn.close()

        # عرض رسالة نجاح
        parent.show_message("نجاح", f"تم إنشاء تقرير الغياب للأسدس الأول للقسم {section} بنجاح.", QMessageBox.Information)

        return True

    except Exception as e:
        import traceback
        traceback.print_exc()
        parent.show_message("خطأ", f"حدث خطأ أثناء إنشاء تقرير الغياب للأسدس الأول:\n{str(e)}", QMessageBox.Critical)
        return False

def print_second_semester_report(parent, section):
    """
    طباعة تقرير الغياب للأسدس الثاني للقسم المحدد

    المعلمات:
    - parent: النافذة الأم (للعرض رسائل الخطأ)
    - section: القسم المحدد

    العائد:
    - True إذا تم إنشاء التقرير بنجاح، False خلاف ذلك
    """
    if not REPORTLAB_AVAILABLE:
        parent.show_message("خطأ", "المكتبات اللازمة لإنشاء التقارير غير متوفرة.", QMessageBox.Critical)
        return False

    if not section:
        parent.show_message("تنبيه", "الرجاء اختيار القسم أولاً.", QMessageBox.Warning)
        return False

    try:
        # استيراد os في نطاق الدالة
        import os

        # تسجيل الخط العربي
        try:
            pdfmetrics.registerFont(TTFont("Arabic", "arial.ttf"))
            print("تم تسجيل الخط العربي بنجاح")
        except Exception as font_error:
            print(f"خطأ في تسجيل الخط العربي: {font_error}")
            # محاولة تسجيل الخط من مسار آخر
            try:
                font_paths = [
                    "arial.ttf",
                    "c:/windows/fonts/arial.ttf",
                    "c:/windows/fonts/arialbd.ttf",
                    "/usr/share/fonts/truetype/msttcorefonts/Arial.ttf"
                ]

                for font_path in font_paths:
                    if os.path.exists(font_path):
                        pdfmetrics.registerFont(TTFont("Arabic", font_path))
                        print(f"تم تسجيل الخط العربي من المسار البديل: {font_path}")
                        break
            except Exception as alt_font_error:
                print(f"فشل تسجيل الخط العربي من المسارات البديلة: {alt_font_error}")

        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(get_database_path())
        cur = conn.cursor()

        # الحصول على بيانات المؤسسة
        cur.execute("SELECT المؤسسة, ImagePath1, السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
        inst = cur.fetchone()
        inst_name, logo_path, school_year = inst if inst else ("المؤسسة", None, "")

        # الحصول على إعدادات البرنامج
        cur.execute("""
            SELECT نقطة_السلوك, معامل_السلوك, نقطة_المواظبة, معامل_المواظبة, المخالفة_الاولى, المخالفة_الثانية, المخالفة_الثالثة
            FROM اعدادات_البرنامج
            LIMIT 1
        """)
        settings = cur.fetchone()
        if not settings:
            settings = (10, 0.5, 10, 0.5, 1, 2, 3)  # قيم افتراضية

        نقطة_السلوك, معامل_السلوك, نقطة_المواظبة, معامل_المواظبة, المخالفة_الاولى, المخالفة_الثانية, المخالفة_الثالثة = settings

        # الحصول على بيانات الغياب للقسم والأسدس الثاني من جدول غياب_الأسدس_الثاني
        cur.execute("""
            SELECT رت, الاسم_والنسب, رمز_التلميذ,
                   فبراير, مارس, أبريل, ماي, يونيو,
                   الغياب_المبرر, المخالفات,
                   مجموع_الغياب_الدوري, مجموع_الغياب_غير_المبرر
            FROM غياب_الأسدس_الثاني
            WHERE القسم = ?
            ORDER BY CAST(رت AS INTEGER)
        """, (section,))

        rows = cur.fetchall()

        if not rows:
            parent.show_message("تنبيه", f"لا توجد بيانات غياب للقسم {section} في الأسدس الثاني.", QMessageBox.Warning)
            conn.close()
            return False

        # إنشاء ملف PDF
        file_path = f"تقرير_الغياب_الأسدس_الثاني_{section}.pdf"
        doc = SimpleDocTemplate(file_path, pagesize=A4, rightMargin=0.2*cm, leftMargin=0.2*cm, topMargin=0.2*cm, bottomMargin=0.2*cm)
        elements = []

        # إضافة شعار المؤسسة والعنوان
        if logo_path and os.path.exists(logo_path):
            img = Image(logo_path)
            img.drawWidth = 250
            img.drawHeight = 80
            elements.append(img)

        elements.append(Spacer(1, 0.3*cm))

        # استخدام الخط العربي المسجل إذا كان متاحًا
        try:
            header_style = ParagraphStyle('header', fontName='Arabic', fontSize=15, alignment=1)
        except:
            header_style = ParagraphStyle('header', fontName='Helvetica-Bold', fontSize=15, alignment=1)

        elements.append(Paragraph(reshape_ar(inst_name), header_style))
        elements.append(Spacer(1, 0.3*cm))

        # عنوان التقرير
        title = f"تقرير الغياب للأسدس الثاني - قسم {section} - السنة الدراسية {school_year}"

        try:
            title_style = ParagraphStyle('title', fontName='Arabic', fontSize=14, alignment=1)
        except:
            title_style = ParagraphStyle('title', fontName='Helvetica-Bold', fontSize=14, alignment=1)

        elements.append(Paragraph(reshape_ar(title), title_style))
        elements.append(Spacer(1, 0.5*cm))

        # إنشاء جدول البيانات
        headers = [
            reshape_ar("رت"),
            reshape_ar("الاسم\nوالنسب"),
            reshape_ar("فبراير"),
            reshape_ar("مارس"),
            reshape_ar("أبريل"),
            reshape_ar("ماي"),
            reshape_ar("يونيو"),
            reshape_ar("المجموع"),
            reshape_ar("غياب\nمبرر"),
            reshape_ar("غياب\nغير مبرر"),
            reshape_ar("المخالفات"),
            reshape_ar("نقطة\nالسلوك"),
            reshape_ar("نقطة\nالمواظبة")
        ]

        data = [headers]

        for row in rows:
            # طباعة محتويات الصف للتشخيص
            print(f"محتويات الصف (الأسدس الثاني): {row}")

            # استخراج البيانات من الصف
            # row[0]: رت
            # row[1]: الاسم والنسب
            # row[2]: رمز_التلميذ
            # row[3]: فبراير
            # row[4]: مارس
            # row[5]: أبريل
            # row[6]: ماي
            # row[7]: يونيو
            # row[8]: الغياب_المبرر
            # row[9]: المخالفات
            # row[10]: مجموع_الغياب_الدوري
            # row[11]: مجموع_الغياب_غير_المبرر

            # استخدام المجموع المحسوب مسبقًا
            total = row[10] or 0
            if isinstance(total, str):
                total = int(total) if total.isdigit() else 0

            # استخدام الغياب المبرر المحسوب مسبقًا
            justified_absence = row[8] or 0
            if isinstance(justified_absence, str):
                justified_absence = int(justified_absence) if justified_absence.isdigit() else 0

            # استخدام الغياب غير المبرر المحسوب مسبقًا
            unjustified_absence = row[11] or 0
            if isinstance(unjustified_absence, str):
                unjustified_absence = int(unjustified_absence) if unjustified_absence.isdigit() else 0

            # حساب نقطة السلوك وفقاً للمعادلة المطلوبة
            try:
                # استخدام عدد المخالفات المحسوب مسبقًا
                violations_count = row[9] or 0  # المخالفات في الموضع 9
                if isinstance(violations_count, str):
                    violations_count = int(violations_count) if violations_count.isdigit() else 0

                # طباعة القيم المستخدمة في حساب نقطة السلوك للتشخيص
                print(f"حساب نقطة السلوك للطالب {row[1]} (الأسدس الثاني):")
                print(f"  نقطة_السلوك من اعدادات_البرنامج: {نقطة_السلوك}")
                print(f"  عدد المخالفات: {violations_count}")
                print(f"  المخالفة_الاولى: {المخالفة_الاولى}")
                print(f"  المخالفة_الثانية: {المخالفة_الثانية}")
                print(f"  المخالفة_الثالثة: {المخالفة_الثالثة}")

                # حساب نقطة السلوك وفقاً للمعادلة المطلوبة
                if violations_count == 1:
                    behavior_score = نقطة_السلوك - المخالفة_الاولى
                elif violations_count == 2:
                    behavior_score = نقطة_السلوك - (المخالفة_الاولى + المخالفة_الثانية)
                elif violations_count >= 3:
                    behavior_score = نقطة_السلوك - (المخالفة_الاولى + المخالفة_الثانية + المخالفة_الثالثة)
                else:
                    behavior_score = نقطة_السلوك  # لا توجد مخالفات

                behavior_score = max(0, round(behavior_score, 2))  # لا تقل عن صفر
                print(f"  نقطة السلوك المحسوبة: {behavior_score}")
            except (ValueError, TypeError) as e:
                print(f"خطأ في حساب نقطة السلوك: {e}")
                behavior_score = float(نقطة_السلوك)  # قيمة افتراضية في حالة حدوث خطأ

            # تم حساب عدد المخالفات مسبقًا في كتلة حساب نقطة السلوك
            print(f"عدد المخالفات للطالب {row[1]} (الأسدس الثاني): {violations_count}")

            # حساب نقطة المواظبة وفقاً للصيغة المطلوبة
            try:
                # طباعة القيم المستخدمة في حساب نقطة المواظبة للتشخيص
                print(f"حساب نقطة المواظبة للطالب {row[1]} (الأسدس الثاني):")
                print(f"  نقطة_المواظبة من اعدادات_البرنامج: {نقطة_المواظبة}")
                print(f"  مجموع_الغياب_غير_المبرر من غياب_الأسدس_الثاني: {unjustified_absence}")
                print(f"  معامل_المواظبة من اعدادات_البرنامج: {معامل_المواظبة}")

                # حساب نقطة المواظبة وفقاً للصيغة المطلوبة
                attendance_score = float(نقطة_المواظبة) - (float(unjustified_absence) * float(معامل_المواظبة))
                attendance_score = max(0, round(attendance_score, 2))  # لا تقل عن صفر
                print(f"  نقطة المواظبة المحسوبة: {attendance_score}")
            except (ValueError, TypeError) as e:
                print(f"خطأ في حساب نقطة المواظبة: {e}")
                attendance_score = float(نقطة_المواظبة)  # قيمة افتراضية في حالة حدوث خطأ

            data_row = [
                reshape_ar(str(row[0])),  # رت
                reshape_ar(str(row[1])),  # الاسم والنسب
                reshape_ar(str(row[3] or 0)),  # فبراير
                reshape_ar(str(row[4] or 0)),  # مارس
                reshape_ar(str(row[5] or 0)),  # أبريل
                reshape_ar(str(row[6] or 0)),  # ماي
                reshape_ar(str(row[7] or 0)),  # يونيو
                reshape_ar(str(total)),  # المجموع
                reshape_ar(str(justified_absence)),  # الغياب المبرر
                reshape_ar(str(unjustified_absence)),  # الغياب غير المبرر
                reshape_ar(str(violations_count)),  # المخالفات
                reshape_ar(str(behavior_score)),  # نقطة السلوك
                reshape_ar(str(attendance_score))  # نقطة المواظبة
            ]
            data.append(data_row)

        # عكس ترتيب البيانات للعرض من اليمين إلى اليسار
        data = [list(reversed(row)) for row in data]

        # تعيين أبعاد الأعمدة حسب الطلب
        col_widths = [
            40,  # نقطة المواظبة
            40,  # نقطة السلوك
            40,  # المخالفات
            40,  # الغياب غير المبرر
            40,  # الغياب المبرر
            40,  # المجموع
            35,  # يونيو
            35,  # ماي
            35,  # أبريل
            35,  # مارس
            35,  # فبراير
            100, # الاسم والنسب
            25   # رت
        ]  # عرض كل عمود

        # إنشاء الجدول مع تحديد ارتفاع الصفوف
        # تحديد ارتفاع الصف الأول (العناوين) وباقي الصفوف
        row_heights = [35] + [14] * (len(data) - 1)

        table = Table(data, colWidths=col_widths, repeatRows=1, rowHeights=row_heights)

        # تحديد الخط المستخدم في الجدول
        table_font = 'Arabic' if 'Arabic' in pdfmetrics.getRegisteredFontNames() else 'Helvetica'
        print(f"الخطوط المسجلة: {pdfmetrics.getRegisteredFontNames()}")

        table.setStyle(TableStyle([
            ('GRID', (0,0), (-1,-1), 0.5, colors.black),
            ('FONTNAME', (0,0), (-1,-1), table_font),
            ('FONTSIZE', (0,0), (-1,-1), 10),
            ('BACKGROUND', (0,0), (-1,0), colors.lightgrey),
            ('ALIGN', (0,0), (-1,-1), 'RIGHT'),
            ('VALIGN', (0,0), (-1,-1), 'MIDDLE'),
            # تحسين تنسيق الخلايا
            ('LEFTPADDING', (0,0), (-1,-1), 3),
            ('RIGHTPADDING', (0,0), (-1,-1), 3),
            ('TOPPADDING', (0,0), (-1,-1), 1),
            ('BOTTOMPADDING', (0,0), (-1,-1), 1)
        ]))

        elements.append(table)

        # إضافة تاريخ إنشاء التقرير
        elements.append(Spacer(1, 0.5*cm))
        current_date = datetime.now().strftime("%Y-%m-%d %H:%M")
        date_text = f"تاريخ إنشاء التقرير: {current_date}"

        try:
            date_style = ParagraphStyle('date', fontName='Arabic', fontSize=10, alignment=1)
        except:
            date_style = ParagraphStyle('date', fontName='Helvetica', fontSize=10, alignment=1)

        elements.append(Paragraph(reshape_ar(date_text), date_style))

        # بناء ملف PDF
        doc.build(elements)

        # فتح الملف
        os.startfile(doc.filename)

        # إغلاق الاتصال بقاعدة البيانات
        conn.close()

        # عرض رسالة نجاح
        parent.show_message("نجاح", f"تم إنشاء تقرير الغياب للأسدس الثاني للقسم {section} بنجاح.", QMessageBox.Information)

        return True

    except Exception as e:
        import traceback
        traceback.print_exc()
        parent.show_message("خطأ", f"حدث خطأ أثناء إنشاء تقرير الغياب للأسدس الثاني:\n{str(e)}", QMessageBox.Critical)
        return False

def print_semester_report_multiple(parent, sections_list):
    """
    طباعة تقارير الأسدس لعدة أقسام

    المعلمات:
    - parent: النافذة الأم (للعرض رسائل الخطأ)
    - sections_list: قائمة الأقسام المطلوب طباعة تقارير لها

    العائد:
    - True إذا تم إنشاء التقارير بنجاح، False خلاف ذلك
    """
    if not sections_list:
        parent.show_message("تنبيه", "لا توجد أقسام محددة للطباعة.", QMessageBox.Warning)
        return False

    success_count = 0
    for section in sections_list:
        if print_semester_report(parent, section):
            success_count += 1

    if success_count > 0:
        parent.show_message("نجاح", f"تم إنشاء {success_count} من {len(sections_list)} تقرير للأسدس.", QMessageBox.Information)
        return True
    else:
        parent.show_message("تنبيه", "لم يتم إنشاء أي تقرير للأسدس.", QMessageBox.Warning)
        return False

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("النافذة الرئيسية")
        self.showMaximized()  # تأخذ كامل الشاشة مع الحفاظ على أزرار التكبير والتصغير  # تجعل النافذة تأخذ كامل الشاشة بدقة الشاشة
        self.setStyleSheet("font-family: Calibri; font-size: 14px;")

        self.init_ui()

    def init_ui(self):
        # اللون الخلفي العام
        palette = self.palette()
        palette.setColor(QPalette.Window, QColor("#f0f4f7"))
        self.setPalette(palette)

        # الحاوية الرئيسية
        main_widget = QWidget()
        main_layout = QVBoxLayout(main_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # شريط علوي للأزرار
        top_bar = QFrame()
        top_bar.setStyleSheet("background-color: #004d40;")
        top_bar.setFixedHeight(60)
        top_layout = QHBoxLayout(top_bar)
        top_layout.setContentsMargins(20, 10, 20, 10)
        top_layout.setSpacing(15)
        top_layout.setDirection(QBoxLayout.RightToLeft)  # لجعل الأزرار من اليمين لليسار

        # زر تسجيل خروج (بلون مميز)
        logout_btn = QPushButton("تسجيل خروج")
        logout_btn.setCursor(Qt.PointingHandCursor)
        logout_btn.setStyleSheet('''
            QPushButton {
                background-color: #c0392b;
                color: white;
                border-radius: 8px;
                padding: 6px 14px;
            }
            QPushButton:hover {
                background-color: #e74c3c;
            }
        ''')
        top_layout.addWidget(logout_btn)

        # أزرار أخرى
        buttons = [
            "بطاقة اللوائح", "إخبار بنشاط", "طلبات الشواهد المدرسية",
            "مسك الغياب", "نافذة بحث", "اللوائح والأقسام",
            "البنية التربوية", "تهيئة البرنامج"
        ]
        for label in buttons:
            btn = QPushButton(label)
            btn.setCursor(Qt.PointingHandCursor)
            btn.setStyleSheet('''
                QPushButton {
                    background-color: transparent;
                    color: white;
                    border: none;
                    padding: 6px 10px;
                }
                QPushButton:hover {
                    background-color: #00695c;
                    border-radius: 6px;
                }
            ''')
            top_layout.addWidget(btn)

        # مسافة فارغة لترك فراغ بعد الأزرار
        top_layout.addSpacerItem(QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum))

        main_layout.addWidget(top_bar)

        # جسم النافذة الرئيسي (يمكن وضع محتوى هنا)
        content_frame = QFrame()
        content_frame.setStyleSheet("background-color: white; border-top: 1px solid #ccc;")
        main_layout.addWidget(content_frame)

        self.setCentralWidget(main_widget)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())
