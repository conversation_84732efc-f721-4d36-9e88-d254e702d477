#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نافذة مسك المخالفات - النسخة الحديثة باستخدام Python + HTML
محولة من sub11_window.py لتستخدم منهجية Python + HTML الحديثة
"""

import sys
import os
import sqlite3
import json
from datetime import datetime
import json
from database_config import get_database_path, get_database_connection
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from PyQt5.QtWebEngineWidgets import QWebEngineView
from PyQt5.QtSql import *
from PyQt5.QtPrintSupport import QPrinter, QPrintDialog

# استيراد الوحدات المطلوبة
try:
    from custom_messages import show_custom_message
except ImportError:
    def show_custom_message(parent, message, title, msg_type):
        if msg_type == "warning":
            QMessageBox.warning(parent, title, message)
        elif msg_type == "error":
            QMessageBox.critical(parent, title, message)
        else:
            QMessageBox.information(parent, title, message)

try:
    from database_utils import get_db_manager
except ImportError:
    def get_db_manager(db_path=None):
        """إنشاء مدير قاعدة بيانات بسيط إذا لم تكن وحدة database_utils متوفرة"""
        if db_path is None:
            db_path = get_database_path()
        try:
            import sqlite3
            import os
            
            # التحقق من وجود ملف قاعدة البيانات
            if not os.path.exists(db_path):
                print(f"ملف قاعدة البيانات غير موجود: {db_path}")
                return None
            
            class SimpleDatabaseManager:
                def __init__(self, db_path):
                    self.db_path = db_path
                
                def execute_query(self, query, params=None):
                    """تنفيذ استعلام قاعدة البيانات"""
                    try:
                        conn = sqlite3.connect(self.db_path)
                        cursor = conn.cursor()
                        
                        if params:
                            cursor.execute(query, params)
                        else:
                            cursor.execute(query)
                        
                        if query.strip().upper().startswith('SELECT'):
                            result = cursor.fetchall()
                        else:
                            conn.commit()
                            result = cursor.rowcount
                        
                        conn.close()
                        return True, result, None
                        
                    except Exception as e:
                        print(f"خطأ في تنفيذ الاستعلام: {e}")
                        return False, None, str(e)
            
            return SimpleDatabaseManager(db_path)
            
        except Exception as e:
            print(f"خطأ في إنشاء مدير قاعدة البيانات: {e}")
            return None

# تصدير الكلاسات المتاحة للاستيراد
__all__ = ['StudentViolationsWindow']

class StudentViolationsWindow(QMainWindow):
    """نافذة مسك المخالفات - تستخدم منهجية Python + HTML الحديثة"""
    
    def __init__(self, student_code=None, student_name=None, db_path=None, parent=None):
        super().__init__(parent)

        # المتغيرات الأساسية
        self.student_code = student_code
        self.student_name = student_name or "غير محدد"
        self.db_path = db_path if db_path is not None else get_database_path()
        self.parent_window = parent
        self.current_student = None

        # تهيئة القيم الافتراضية
        self.school_year = "2024/2025"
        self.semester = "الأول"
        
        # إعداد قاعدة البيانات
        print(f"🔧 إعداد مدير قاعدة البيانات...")
        print(f"📁 مسار قاعدة البيانات: {db_path}")
        self.db_manager = get_db_manager(db_path) if get_db_manager else None
        
        if self.db_manager:
            print("✅ تم إنشاء مدير قاعدة البيانات بنجاح")
        else:
            print("❌ لم يتم إنشاء مدير قاعدة البيانات")
        
        # إعداد النافذة
        self.setupUI()
        
        # تحميل البيانات الأولية
        self.load_initial_data()

        # تحميل بيانات التلميذ إذا توفر
        if self.student_code:
            self.load_student_data()

        # إعداد جسر JavaScript للتفاعل مع الأساتذة
        self.setup_javascript_bridge()

        # إضافة قناة للتواصل مع JavaScript للبحث
        self.setup_search_bridge()

        # فتح النافذة في كامل الشاشة بعد تحميل المحتوى
        QTimer.singleShot(100, self.open_fullscreen)
    
    def setupUI(self):
        """إعداد واجهة المستخدم"""
        # إعدادات النافذة الأساسية
        self.setWindowTitle(f" مسك المخالفات - {self.student_name}")
        self.setLayoutDirection(Qt.RightToLeft)
        
        # تعيين الحد الأدنى لحجم النافذة
        self.setMinimumSize(1200, 800)
        
        # إضافة أيقونة للنافذة
        try:
            self.setWindowIcon(QIcon("01.ico"))
        except:
            pass
        
        # تطبيق نمط احترافي للنافذة
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #fff3e0,
                    stop: 1 #ffcc80
                );
            }
        """)
        
        # إنشاء الواجهة المركزية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # إنشاء منطقة عرض المحتوى HTML
        self.web_view = QWebEngineView()
        main_layout.addWidget(self.web_view)
        
        # إضافة أزرار التحكم
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)
        buttons_layout.setContentsMargins(10, 5, 10, 10)
        
        # زر الحفظ
        save_button = QPushButton("💾 حفظ المخالفة")
        save_button.setFont(QFont("Calibri", 14, QFont.Bold))
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                padding: 12px 25px;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                font-family: 'Calibri';
            }
            QPushButton:hover {
                background-color: #45a049;
                transform: translateY(-2px);
            }
        """)
        save_button.clicked.connect(self.save_violation)
        
        # زر المسح
        clear_button = QPushButton("🗑️ مسح البيانات")
        clear_button.setFont(QFont("Calibri", 14, QFont.Bold))
        clear_button.setStyleSheet("""
            QPushButton {
                background-color: #ff9800;
                color: white;
                padding: 12px 25px;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                font-family: 'Calibri';
            }
            QPushButton:hover {
                background-color: #e68900;
                transform: translateY(-2px);
            }
        """)
        clear_button.clicked.connect(self.clear_form)
        
        # زر الإغلاق
        close_button = QPushButton("❌ إغلاق")
        close_button.setFont(QFont("Calibri", 14, QFont.Bold))
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                padding: 12px 25px;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                font-family: 'Calibri';
            }
            QPushButton:hover {
                background-color: #d32f2f;
                transform: translateY(-2px);
            }
        """)
        close_button.clicked.connect(self.close)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(save_button)
        buttons_layout.addWidget(clear_button)
        buttons_layout.addWidget(close_button)
        
        main_layout.addLayout(buttons_layout)
        
        # إضافة شريط الحالة
        self.status_bar = self.statusBar()
        self.status_bar.setFont(QFont("Calibri", 12))
        self.status_bar.setStyleSheet("""
            QStatusBar {
                background-color: #f5f5f5;
                color: #333;
                border-top: 1px solid #ddd;
                font-family: 'Calibri';
                font-size: 12px;
                padding: 5px 10px;
            }
        """)
        self.status_bar.showMessage("جاهز لتسجيل المخالفات")
    
    def load_initial_data(self):
        """تحميل البيانات الأولية للنافذة"""
        try:
            # تحميل بيانات السنة الدراسية والأسدس
            print("🔄 بدء تحميل البيانات الأولية...")
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # فحص وجود جدول بيانات_المؤسسة
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='بيانات_المؤسسة'")
            table_exists = cursor.fetchone()

            if table_exists:
                cursor.execute("SELECT السنة_الدراسية, الأسدس FROM بيانات_المؤسسة LIMIT 1")
                result = cursor.fetchone()

                if result:
                    self.school_year = str(result[0]) if result[0] else "2024/2025"
                    self.semester = str(result[1]) if result[1] else "الأول"

            conn.close()
            print(f"✅ تم تحميل بيانات المؤسسة: السنة الدراسية {self.school_year} - الأسدس {self.semester}")

            # تحميل البيانات من قاعدة البيانات
            levels = self.get_levels()
            subjects = self.get_subjects()
            violations = self.get_violations()
            procedures = self.get_procedures()

            # إنشاء محتوى HTML وعرضه
            html_content = self.create_html_content(levels, subjects, violations, procedures)
            self.web_view.setHtml(html_content)

            self.status_bar.showMessage("تم تحميل البيانات الأولية بنجاح")

        except Exception as e:
            print(f"❌ خطأ في تحميل البيانات الأولية: {e}")
            self.school_year = "2024/2025"
            self.semester = "الأول"
            self.status_bar.showMessage(f"خطأ في تحميل البيانات: {str(e)}")
    
    def get_levels(self):
        """جلب المستويات من قاعدة البيانات"""
        try:
            if not self.db_manager:
                return ["الأولى ثانوي", "الثانية ثانوي", "الثالثة ثانوي"]
            
            # استعلام قاعدة البيانات
            success, result, error = self.db_manager.execute_query(
                "SELECT DISTINCT المستوى FROM البنية_التربوية ORDER BY المستوى"
            )
            
            if success and result:
                return [row[0] for row in result if row[0]]
            else:
                return ["الأولى ثانوي", "الثانية ثانوي", "الثالثة ثانوي"]
                
        except Exception as e:
            print(f"خطأ في جلب المستويات: {e}")
            return ["الأولى ثانوي", "الثانية ثانوي", "الثالثة ثانوي"]

    def setup_search_bridge(self):
        """إعداد قناة التواصل مع JavaScript للبحث"""
        try:
            print("🔧 إعداد قناة التواصل مع JavaScript للبحث...")

            # إعداد مؤقت للتحقق من طلبات البحث
            self.search_timer = QTimer()
            self.search_timer.timeout.connect(self.check_search_requests)
            self.search_timer.start(500)

        except Exception as e:
            print(f"❌ خطأ في إعداد قناة JavaScript للبحث: {e}")

    def check_search_requests(self):
        """فحص طلبات البحث من JavaScript"""
        try:
            js_code = """
                (function() {
                    if (window.pendingSearchData) {
                        const searchCode = window.pendingSearchData;
                        window.pendingSearchData = null;
                        return searchCode;
                    }
                    return '';
                })();
            """

            def handle_search_result(result):
                if result and result.strip():
                    print(f"🔍 تم استلام طلب البحث: {result}")
                    self.search_student_by_code(result.strip())

            self.web_view.page().runJavaScript(js_code, handle_search_result)

        except Exception as e:
            print(f"❌ خطأ في فحص طلبات البحث: {e}")

    def search_student_by_code(self, student_code):
        """البحث عن التلميذ برمزه"""
        try:
            print(f"🔍 البحث عن التلميذ برمزه: {student_code}")

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # الاستعلام المركزي
            central_query = """
            SELECT
                s.الرمز,
                s.الاسم_والنسب,
                l.المستوى,
                l.القسم,
                l.رت as الرقم_الترتيبي
            FROM السجل_العام s
            LEFT JOIN اللوائح l ON s.الرمز = l.الرمز
            WHERE s.الرمز = ? AND l.السنة_الدراسية = ?
            LIMIT 1
            """

            cursor.execute(central_query, (student_code, self.school_year))
            result = cursor.fetchone()

            if not result:
                # استعلام بديل بدون شرط السنة الدراسية
                cursor.execute("""
                    SELECT
                        s.الرمز,
                        s.الاسم_والنسب,
                        l.المستوى,
                        l.القسم,
                        l.رت as الرقم_الترتيبي
                    FROM السجل_العام s
                    LEFT JOIN اللوائح l ON s.الرمز = l.الرمز
                    WHERE s.الرمز = ?
                    ORDER BY l.السنة_الدراسية DESC
                    LIMIT 1
                """, (student_code,))
                result = cursor.fetchone()

            conn.close()

            if result:
                code = result[0] if result[0] else "غير محدد"
                name = result[1] if result[1] else "غير محدد"
                level = result[2] if result[2] else "غير محدد"
                class_name = result[3] if result[3] else "غير محدد"
                student_id = result[4] if result[4] else "غير محدد"

                print(f"✅ تم العثور على التلميذ: {name}")

                # تعيين معلومات التلميذ في HTML
                safe_code = str(code).replace("'", "\\'").replace('"', '\\"')
                safe_name = str(name).replace("'", "\\'").replace('"', '\\"')
                safe_id = str(student_id).replace("'", "\\'").replace('"', '\\"')
                safe_level = str(level).replace("'", "\\'").replace('"', '\\"')
                safe_class = str(class_name).replace("'", "\\'").replace('"', '\\"')

                js_code = f"""
                    try {{
                        document.getElementById('student_code').value = '{safe_code}';
                        document.getElementById('student_name').value = '{safe_name}';
                        document.getElementById('student_rt').value = '{safe_id}';
                        document.getElementById('level').value = '{safe_level}';
                        document.getElementById('section').value = '{safe_class}';

                        // تعيين نفس القيمة في مربع البحث
                        document.getElementById('student_search').value = '{safe_code}';

                        showAlert('تم العثور على التلميذ: {safe_name}', 'success');
                    }} catch(error) {{
                        console.error('خطأ في تعيين معلومات التلميذ:', error);
                        showAlert('حدث خطأ في تحديد معلومات التلميذ', 'danger');
                    }}
                """
                self.web_view.page().runJavaScript(js_code)

                # حفظ البيانات في الكائن الحالي
                self.current_student = {
                    'code': code,
                    'name': name,
                    'id': student_id,
                    'level': level,
                    'class': class_name
                }

                # تحديث متغيرات الكلاس
                self.student_code = code
                self.student_name = name

                return True

            else:
                print(f"❌ لم يتم العثور على التلميذ برمز: {student_code}")
                js_code = f"""
                    showAlert('لم يتم العثور على تلميذ برمز: {student_code} في قاعدة البيانات', 'danger');
                """
                self.web_view.page().runJavaScript(js_code)
                return False

        except Exception as e:
            print(f"❌ خطأ في البحث عن التلميذ: {e}")
            js_code = f"showAlert('حدث خطأ أثناء البحث: {str(e)}', 'danger');"
            self.web_view.page().runJavaScript(js_code)
            return False

    def get_subjects(self):
        """جلب المواد من قاعدة البيانات - نفس الجدول الذي يحتوي على الأساتذة"""
        print("📚 بدء جلب المواد من قاعدة البيانات...")
        try:
            if not self.db_manager:
                print("❌ مدير قاعدة البيانات غير متوفر")
                return []

            # استعلام قاعدة البيانات من جدول الأساتذة لضمان التطابق
            success, result, error = self.db_manager.execute_query(
                "SELECT DISTINCT المادة FROM الأساتذة ORDER BY المادة"
            )

            if success and result:
                subjects = [row[0] for row in result if row[0]]
                print(f"✅ تم جلب {len(subjects)} مادة من جدول الأساتذة: {subjects}")
                return subjects
            else:
                print(f"❌ فشل في جلب المواد من قاعدة البيانات: {error}")
                return []

        except Exception as e:
            print(f"❌ خطأ في جلب المواد: {e}")
            return []

    def get_violations(self):
        """جلب أنواع المخالفات من جدول تعديل_المسميات (ID من 7 إلى 16)"""
        print("⚠️ بدء جلب أنواع المخالفات من قاعدة البيانات...")
        try:
            if not self.db_manager:
                print("❌ تعذر الاتصال بقاعدة البيانات - لا توجد بيانات افتراضية")
                return []

            # استعلام قاعدة البيانات من جدول تعديل_المسميات للمخالفات (ID من 7 إلى 16)
            success, result, error = self.db_manager.execute_query(
                "SELECT العنوان FROM تعديل_المسميات WHERE ID BETWEEN 7 AND 16 ORDER BY ID"
            )

            if success and result:
                violations = [row[0] for row in result if row[0]]
                print(f"✅ تم جلب {len(violations)} نوع مخالفة من جدول تعديل_المسميات: {violations}")
                return violations
            else:
                print(f"❌ فشل في جلب المخالفات من قاعدة البيانات: {error}")
                return []

        except Exception as e:
            print(f"❌ خطأ في جلب المخالفات: {e}")
            return []

    def get_procedures(self):
        """جلب الإجراءات المتخذة من جدول تعديل_المسميات (ID من 17 إلى 20)"""
        print("📋 بدء جلب الإجراءات المتخذة من قاعدة البيانات...")
        try:
            if not self.db_manager:
                print("❌ تعذر الاتصال بقاعدة البيانات - لا توجد بيانات افتراضية")
                return []

            # استعلام قاعدة البيانات من جدول تعديل_المسميات للإجراءات (ID من 17 إلى 20)
            success, result, error = self.db_manager.execute_query(
                "SELECT العنوان FROM تعديل_المسميات WHERE ID BETWEEN 17 AND 20 ORDER BY ID"
            )

            if success and result:
                procedures = [row[0] for row in result if row[0]]
                print(f"✅ تم جلب {len(procedures)} إجراء من جدول تعديل_المسميات: {procedures}")
                return procedures
            else:
                print(f"❌ فشل في جلب الإجراءات من قاعدة البيانات: {error}")
                return []

        except Exception as e:
            print(f"❌ خطأ في جلب الإجراءات: {e}")
            return []

    def get_teachers(self):
        """جلب الأساتذة من قاعدة البيانات"""
        try:
            if not self.db_manager:
                return []

            # استعلام قاعدة البيانات
            success, result, error = self.db_manager.execute_query(
                "SELECT DISTINCT اسم_الأستاذ FROM الأساتذة ORDER BY اسم_الأستاذ"
            )

            if success and result:
                return [row[0] for row in result if row[0]]
            else:
                return []

        except Exception as e:
            print(f"خطأ في جلب الأساتذة: {e}")
            return []

    def update_teachers_for_subject(self, subject):
        """تحديث قائمة الأساتذة بناءً على المادة المحددة"""
        print(f"👨‍🏫 بدء تحديث قائمة الأساتذة للمادة: {subject}")
        try:
            if not subject or not self.db_manager:
                print("❌ لا توجد مادة محددة أو مدير قاعدة البيانات غير متوفر")
                # إذا لم تكن هناك مادة محددة أو لا يوجد مدير قاعدة بيانات، قم بمسح القائمة
                js_code = """
                const teacherSelect = document.getElementById('teacher');
                teacherSelect.innerHTML = '<option value="">اختر الأستاذ</option>';
                """
                self.web_view.page().runJavaScript(js_code)
                return []

            # استعلام قاعدة البيانات لجلب الأساتذة حسب المادة المحددة
            success, result, error = self.db_manager.execute_query(
                "SELECT DISTINCT اسم_الأستاذ FROM الأساتذة WHERE المادة = ? ORDER BY اسم_الأستاذ",
                (subject,)
            )

            if success and result:
                teachers = [row[0] for row in result if row[0]]
                print(f"✅ تم العثور على {len(teachers)} أستاذ للمادة '{subject}': {teachers}")
                
                # تحديث قائمة الأساتذة في HTML
                teachers_json = json.dumps(teachers)
                js_code = f"""
                const teacherSelect = document.getElementById('teacher');
                teacherSelect.innerHTML = '<option value="">اختر الأستاذ</option>';
                
                const teachers = {teachers_json};
                teachers.forEach(teacher => {{
                    const option = document.createElement('option');
                    option.value = teacher;
                    option.textContent = teacher;
                    teacherSelect.appendChild(option);
                }});
                """
                
                self.web_view.page().runJavaScript(js_code)
                print(f"✅ تم تحديث قائمة الأساتذة في HTML")
                return teachers
            else:
                print(f"❌ لم يتم العثور على أساتذة للمادة: {subject} - خطأ: {error}")
                # مسح القائمة إذا لم توجد نتائج
                js_code = """
                const teacherSelect = document.getElementById('teacher');
                teacherSelect.innerHTML = '<option value="">لا يوجد أساتذة لهذه المادة</option>';
                """
                self.web_view.page().runJavaScript(js_code)
                return []

        except Exception as e:
            print(f"❌ خطأ في تحديث الأساتذة: {e}")
            # في حالة وجود خطأ، قم بمسح القائمة
            js_code = """
            const teacherSelect = document.getElementById('teacher');
            teacherSelect.innerHTML = '<option value="">حدث خطأ في تحميل الأساتذة</option>';
            """
            self.web_view.page().runJavaScript(js_code)
            return []

    def create_html_content(self, levels, subjects, violations, procedures):
        """إنشاء محتوى HTML للنافذة"""
        # جلب الأساتذة أيضاً
        teachers = self.get_teachers()
        
        html_content = f"""
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>مسك المخالفات</title>
            <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
            <style>
                :root {{
                    --primary-color: #667eea;
                    --secondary-color: #764ba2;
                    --success-color: #4CAF50;
                    --warning-color: #ff9800;
                    --danger-color: #f44336;
                    --info-color: #2196F3;
                    --light-gray: #f8f9fa;
                    --dark-gray: #343a40;
                    --border-radius: 12px;
                    --shadow: 0 4px 20px rgba(0,0,0,0.1);
                    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }}

                * {{
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }}

                body {{
                    font-family: 'Calibri', 'Segoe UI', 'Tahoma', 'Geneva', 'Verdana', sans-serif;
                    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
                    min-height: 100vh;
                    padding: 15px;
                    direction: rtl;
                    line-height: 1.6;
                }}

                .container {{
                    max-width: 1200px;
                    margin: 0 auto;
                    background: white;
                    border-radius: var(--border-radius);
                    box-shadow: var(--shadow);
                    overflow: hidden;
                    animation: slideIn 0.5s ease-out;
                }}

                @keyframes slideIn {{
                    from {{
                        opacity: 0;
                        transform: translateY(30px);
                    }}
                    to {{
                        opacity: 1;
                        transform: translateY(0);
                    }}
                }}

                .header {{
                    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
                    color: white;
                    padding: 25px;
                    text-align: center;
                    position: relative;
                    overflow: hidden;
                }}

                .header::before {{
                    content: '';
                    position: absolute;
                    top: -50%;
                    left: -50%;
                    width: 200%;
                    height: 200%;
                    background: repeating-linear-gradient(
                        45deg,
                        transparent,
                        transparent 2px,
                        rgba(255,255,255,0.1) 2px,
                        rgba(255,255,255,0.1) 4px
                    );
                    animation: movePattern 20s linear infinite;
                }}

                @keyframes movePattern {{
                    0% {{ transform: translate(-50%, -50%) rotate(0deg); }}
                    100% {{ transform: translate(-50%, -50%) rotate(360deg); }}
                }}

                .header h1 {{
                    font-family: 'Calibri', sans-serif;
                    font-size: 30px;
                    font-weight: bold;
                    margin-bottom: 10px;
                    position: relative;
                    z-index: 1;
                }}

                .header p {{
                    font-family: 'Calibri', sans-serif;
                    font-size: 18px;
                    opacity: 0.9;
                    position: relative;
                    z-index: 1;
                }}

                .search-section {{
                    background: #e8f4fd;
                    padding: 20px;
                    border-bottom: 3px solid #3498db;
                }}

                .search-container {{
                    display: flex;
                    align-items: center;
                    gap: 15px;
                    justify-content: center;
                    flex-wrap: nowrap;
                }}

                .search-container label {{
                    font-family: 'Calibri', sans-serif;
                    font-size: 17px;
                    font-weight: bold;
                    color: #1e3a8a;
                }}

                .search-input {{
                    font-family: 'Calibri', sans-serif;
                    font-size: 17px;
                    font-weight: bold;
                    color: #1f2937;
                    padding: 12px 20px;
                    border: 2px solid #3498db;
                    border-radius: 10px;
                    min-width: 200px;
                    background: white;
                    transition: all 0.3s ease;
                }}

                .search-input:focus {{
                    border-color: #2980b9;
                    outline: none;
                    box-shadow: 0 0 15px rgba(52, 152, 219, 0.4);
                }}

                .search-btn {{
                    font-family: 'Calibri', sans-serif;
                    font-size: 17px;
                    font-weight: bold;
                    padding: 12px 24px;
                    background: linear-gradient(45deg, #3498db, #2980b9);
                    color: white;
                    border: none;
                    border-radius: 10px;
                    cursor: pointer;
                    transition: all 0.3s ease;
                }}

                .search-btn:hover {{
                    background: linear-gradient(45deg, #2980b9, #1f4e79);
                    transform: translateY(-2px);
                    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
                }}

                .form-container {{
                    padding: 30px;
                    background: var(--light-gray);
                }}

                .form-section {{
                    background: white;
                    border-radius: var(--border-radius);
                    padding: 25px;
                    margin-bottom: 20px;
                    box-shadow: var(--shadow);
                    border-left: 4px solid var(--primary-color);
                }}

                .section-title {{
                    display: flex;
                    align-items: center;
                    gap: 10px;
                    font-family: 'Calibri', sans-serif;
                    font-size: 20px;
                    font-weight: bold;
                    color: #1a237e;
                    margin-bottom: 20px;
                    padding-bottom: 10px;
                    border-bottom: 2px solid #eee;
                }}

                .form-row {{
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                    gap: 20px;
                    margin-bottom: 20px;
                    align-items: end;
                }}

                .form-group {{
                    position: relative;
                }}

                .form-group.full-width {{
                    grid-column: 1 / -1;
                }}

                label {{
                    display: block;
                    margin-bottom: 8px;
                    font-family: 'Calibri', sans-serif;
                    font-size: 17px;
                    font-weight: 600;
                    color: #1a237e;
                }}

                .required {{
                    color: var(--danger-color);
                }}

                input, select, textarea {{
                    width: 100%;
                    padding: 12px 15px;
                    border: 2px solid #e0e0e0;
                    border-radius: var(--border-radius);
                    font-family: 'Calibri', sans-serif;
                    font-size: 17px;
                    font-weight: 600;
                    color: #212121;
                    transition: var(--transition);
                    background: white;
                }}

                input:focus, select:focus, textarea:focus {{
                    outline: none;
                    border-color: var(--primary-color);
                    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                    transform: translateY(-2px);
                }}

                input:valid, select:valid {{
                    border-color: var(--success-color);
                }}

                textarea {{
                    resize: vertical;
                    min-height: 120px;
                    font-family: inherit;
                }}

                .search-btn {{
                    background: linear-gradient(135deg, var(--success-color), #45a049);
                    color: white;
                    border: none;
                    padding: 12px 20px;
                    border-radius: var(--border-radius);
                    cursor: pointer;
                    font-family: 'Calibri', sans-serif;
                    font-size: 16px;
                    font-weight: bold;
                    transition: var(--transition);
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    justify-content: center;
                    min-width: 120px;
                }}

                .search-btn:hover {{
                    transform: translateY(-3px);
                    box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);
                }}

                .search-btn:active {{
                    transform: translateY(0);
                }}

                .message {{
                    padding: 15px 20px;
                    border-radius: var(--border-radius);
                    margin: 15px 0;
                    display: none;
                    font-family: 'Calibri', sans-serif;
                    font-size: 16px;
                    font-weight: 500;
                    animation: messageSlide 0.3s ease-out;
                }}

                @keyframes messageSlide {{
                    from {{
                        opacity: 0;
                        transform: translateX(20px);
                    }}
                    to {{
                        opacity: 1;
                        transform: translateX(0);
                    }}
                }}

                .message.success {{
                    background: linear-gradient(135deg, #d4edda, #c3e6cb);
                    color: #155724;
                    border-left: 4px solid var(--success-color);
                }}

                .message.error {{
                    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
                    color: #721c24;
                    border-left: 4px solid var(--danger-color);
                }}

                .message.warning {{
                    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
                    color: #856404;
                    border-left: 4px solid var(--warning-color);
                }}

                .loading {{
                    display: none;
                    text-align: center;
                    padding: 20px;
                }}

                .spinner {{
                    border: 4px solid #f3f3f3;
                    border-top: 4px solid var(--primary-color);
                    border-radius: 50%;
                    width: 40px;
                    height: 40px;
                    animation: spin 1s linear infinite;
                    margin: 0 auto 10px;
                }}

                @keyframes spin {{
                    0% {{ transform: rotate(0deg); }}
                    100% {{ transform: rotate(360deg); }}
                }}

                .student-info {{
                    background: linear-gradient(135deg, #e3f2fd, #bbdef
                    border: 2px solid #2196F3;
                    border-radius: var(--border-radius);
                    padding: 15px;
                    margin: 15px 0;
                    display: none;
                }}

                .student-info.show {{
                    display: block;
                    animation: fadeIn 0.5s ease-out;
                }}

                @keyframes fadeIn {{
                    from {{ opacity: 0; }}
                    to {{ opacity: 1; }}
                }}

                .input-icon {{
                    position: relative;
                }}

                .input-icon i {{
                    position: absolute;
                    left: 12px;
                    top: 50%;
                    transform: translateY(-50%);
                    color: #888;
                    z-index: 1;
                }}

                .input-icon input {{
                    padding-left: 40px;
                }}

                /* تحسينات الاستجابة */
                @media (max-width: 768px) {{
                    .container {{
                        margin: 10px;
                        border-radius: 8px;
                    }}

                    .form-container {{
                        padding: 15px;
                    }}

                    .form-row {{
                        grid-template-columns: 1fr;
                        gap: 15px;
                    }}

                    .header h1 {{
                        font-size: 22px;
                    }}
                }}

                /* تأثيرات إضافية */
                .pulse {{
                    animation: pulse 2s infinite;
                }}

                @keyframes pulse {{
                    0% {{ box-shadow: 0 0 0 0 var(--primary-color); }}
                    70% {{ box-shadow: 0 0 0 10px rgba(102, 126, 234, 0); }}
                    100% {{ box-shadow: 0 0 0 0 rgba(102, 126, 234, 0); }}
                }}                    <!-- تنسيق مربعات النص للمخالفات والإجراءات -->
                    textarea#violations_text, textarea#procedures_text {{
                        background-color: #f8f9fa;
                        border: 2px solid #dee2e6;
                        color: #495057;
                        font-family: 'Calibri', sans-serif;
                        font-size: 16px;
                        line-height: 1.5;
                        resize: vertical;
                        min-height: 120px;
                    }}

                    textarea#violations_text:focus, textarea#procedures_text:focus {{
                        background-color: white;
                        border-color: var(--primary-color);
                        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                    }}

                    /* تنسيق عام للحقول */
                    .form-row {{
                        animation: slideIn 0.3s ease-out;
                    }}

                    @keyframes slideIn {{
                        from {{
                            opacity: 0;
                            transform: translateY(-10px);
                        }}
                        to {{
                            opacity: 1;
                            transform: translateY(0);
                        }}
                    }}
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1> نموذج مسك المخالفات</h1>
                        <p>تسجيل وإدارة مخالفات التلاميذ</p>
                    </div>

                    <!-- قسم البحث عن التلميذ -->
                    <div class="search-section">
                        <div class="search-container">
                            <label for="student_search">🔍 البحث برمز التلميذ:</label>
                            <input type="text" id="student_search" class="search-input"
                                   placeholder="أدخل رمز التلميذ..."
                                   onkeypress="handleEnterKey(event)">
                            <button type="button" class="search-btn" onclick="searchStudent()">
                                🔍 بحث
                            </button>
                        </div>
                    </div>

                <div class="form-container">
                    <div id="message" class="message"></div>

                    <form id="violationForm">
                        <!-- بيانات التلميذ -->
                        <div class="form-row">
                            <div class="form-group">
                                <label for="student_code">رمز التلميذ:</label>
                                <input type="text" id="student_code" name="student_code" value="{self.student_code or ''}" required onchange="loadStudentData(this.value)">
                            </div>
                            <div class="form-group">
                                <label for="student_rt">رت:</label>
                                <input type="text" id="student_rt" name="student_rt" readonly>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="student_name">اسم التلميذ:</label>
                                <input type="text" id="student_name" name="student_name" value="{self.student_name if self.student_name != 'غير محدد' else ''}" readonly>
                            </div>
                            <div class="form-group">
                                <label for="level">المستوى:</label>
                                <input type="text" id="level" name="level" readonly>
                            </div>
                            <div class="form-group">
                                <label for="section">القسم:</label>
                                <input type="text" id="section" name="section" readonly>
                            </div>
                        </div>

                        <!-- تفاصيل المخالفة -->
                        <div class="form-row">
                            <div class="form-group">
                                <label for="violation_date">تاريخ المخالفة:</label>
                                <input type="date" id="violation_date" name="violation_date" value="{datetime.now().strftime('%Y-%m-%d')}" required>
                            </div>
                            <div class="form-group">
                                <label for="violation_time">وقت المخالفة:</label>
                                <input type="time" id="violation_time" name="violation_time" value="{datetime.now().strftime('%H:%M')}" required>
                            </div>
                            <div class="form-group">
                                <label for="subject">المادة:</label>
                                <select id="subject" name="subject" onchange="updateTeachers(this.value)">
                                    <option value="">اختر المادة</option>
                                    {''.join([f'<option value="{subject}">{subject}</option>' for subject in subjects])}
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="teacher">الأستاذ:</label>
                                <select id="teacher" name="teacher">
                                    <option value="">اختر الأستاذ</option>
                                    {''.join([f'<option value="{teacher}">{teacher}</option>' for teacher in teachers])}
                                </select>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="violation_type">نوع المخالفة:</label>
                                <select id="violation_type" name="violation_type" onchange="addViolationToText()">
                                    <option value="">اختر نوع المخالفة</option>
                                    {''.join([f'<option value="{violation}">{violation}</option>' for violation in violations])}
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="procedure">الإجراء المتخذ:</label>
                                <select id="procedure" name="procedure" onchange="addProcedureToText()">
                                    <option value="">اختر الإجراء</option>
                                    {''.join([f'<option value="{procedure}">{procedure}</option>' for procedure in procedures])}
                                </select>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group full-width">
                                <label for="violations_text">المخالفات:</label>
                                <textarea id="violations_text" name="violations_text" placeholder="ستظهر المخالفات المحددة هنا... كل مخالفة في سطر منفصل" rows="4" readonly></textarea>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group full-width">
                                <label for="procedures_text">الإجراءات المتخذة:</label>
                                <textarea id="procedures_text" name="procedures_text" placeholder="ستظهر الإجراءات المحددة هنا... كل إجراء في سطر منفصل" rows="4" readonly></textarea>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group full-width">
                                <label for="notes">ملاحظات إضافية:</label>
                                <textarea id="notes" name="notes" placeholder="أي ملاحظات إضافية..."></textarea>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <script>
                // دالة تحميل بيانات التلميذ
                function loadStudentData(studentCode) {{
                    if (!studentCode) {{
                        // مسح البيانات إذا كان الحقل فارغاً
                        const elements = ['student_rt', 'student_name', 'level', 'section'];
                        elements.forEach(id => {{
                            const element = document.getElementById(id);
                            if (element) element.value = '';
                        }});
                        return;
                    }}

                    // إرسال طلب البحث إلى Python
                    window.loadStudentDataRequest = studentCode;
                    console.log(`طلب تحميل بيانات التلميذ: ${{studentCode}}`);
                }}

                // دالة البحث عن التلميذ (محذوفة - لا نحتاجها بعد الآن)
                // function searchStudent() {{ ... }}

                // دالة تحديث الأقسام حسب المستوى (محذوفة - الآن الحقول readonly)
                // function updateSections(level, selectedSection = '') {{ ... }}

                // دالة تحديث الأساتذة حسب المادة المحددة
                function updateTeachers(subject, selectedTeacher = '') {{
                    const teacherSelect = document.getElementById('teacher');
                    if (!teacherSelect) {{
                        console.error('عنصر teacher غير موجود');
                        return;
                    }}
                    
                    teacherSelect.innerHTML = '<option value="">جاري التحميل...</option>';

                    if (subject) {{
                        // إرسال طلب لـ Python لجلب الأساتذة حسب المادة من قاعدة البيانات
                        window.updateTeachersForSubject = subject;
                        console.log(`طلب تحديث الأساتذة للمادة: ${{subject}}`);
                    }} else {{
                        // إذا لم تكن هناك مادة محددة، اعرض رسالة فارغة
                        teacherSelect.innerHTML = '<option value="">اختر المادة أولاً</option>';
                    }}
                }}

                // دالة عرض الرسائل
                function showMessage(message, type) {{
                    const messageDiv = document.getElementById('message');
                    messageDiv.textContent = message;
                    messageDiv.className = `message ${{type}}`;
                    messageDiv.style.display = 'block';

                    setTimeout(() => {{
                        messageDiv.style.display = 'none';
                    }}, 5000);
                }}

                // دالة مسح النموذج
                function clearForm() {{
                    const form = document.getElementById('violationForm');
                    if (form) form.reset();
                    
                    // إعادة تعيين القيم الافتراضية مع التحقق من وجود العناصر
                    const studentCodeElement = document.getElementById('student_code');
                    if (studentCodeElement) studentCodeElement.value = '{self.student_code or ''}';
                    
                    const studentNameElement = document.getElementById('student_name');
                    if (studentNameElement) studentNameElement.value = '{self.student_name if self.student_name != 'غير محدد' else ''}';
                    
                    const violationDateElement = document.getElementById('violation_date');
                    if (violationDateElement) violationDateElement.value = '{datetime.now().strftime('%Y-%m-%d')}';
                    
                    const violationTimeElement = document.getElementById('violation_time');
                    if (violationTimeElement) violationTimeElement.value = '{datetime.now().strftime('%H:%M')}';
                    
                    // إعادة تعيين قائمة الأساتذة
                    const teacherSelect = document.getElementById('teacher');
                    if (teacherSelect) teacherSelect.innerHTML = '<option value="">اختر الأستاذ</option>';
                    
                    // مسح مربعات النص للمخالفات والإجراءات
                    const violationsText = document.getElementById('violations_text');
                    const proceduresText = document.getElementById('procedures_text');
                    
                    if (violationsText) violationsText.value = '';
                    if (proceduresText) proceduresText.value = '';
                    
                    showMessage('تم مسح البيانات', 'success');
                }}

                // دالة إضافة المخالفة إلى مربع النص تلقائياً
                function addViolationToText() {{
                    const violationSelect = document.getElementById('violation_type');
                    const violationsText = document.getElementById('violations_text');
                    
                    if (!violationSelect || !violationsText) {{
                        console.error('عناصر المخالفات غير موجودة');
                        return;
                    }}
                    
                    if (violationSelect.value) {{
                        const selectedViolation = violationSelect.options[violationSelect.selectedIndex].text;
                        
                        // التحقق من عدم وجود المخالفة مسبقاً
                        const currentViolations = violationsText.value.split('\\n').filter(v => v.trim());
                        if (currentViolations.includes(selectedViolation)) {{
                            showMessage('هذه المخالفة مضافة مسبقاً', 'warning');
                            violationSelect.value = '';
                            return;
                        }}
                        
                        // إضافة المخالفة إلى مربع النص
                        let currentText = violationsText.value;
                        if (currentText.trim()) {{
                            currentText += '\\n' + selectedViolation;
                        }} else {{
                            currentText = selectedViolation;
                        }}
                        violationsText.value = currentText;
                        
                        // إعادة تعيين القائمة المنسدلة
                        violationSelect.value = '';
                        
                        showMessage('تم إضافة المخالفة', 'success');
                    }}
                }}

                // دالة إضافة الإجراء إلى مربع النص تلقائياً
                function addProcedureToText() {{
                    const procedureSelect = document.getElementById('procedure');
                    const proceduresText = document.getElementById('procedures_text');
                    
                    if (!procedureSelect || !proceduresText) {{
                        console.error('عناصر الإجراءات غير موجودة');
                        return;
                    }}
                    
                    if (procedureSelect.value) {{
                        const selectedProcedure = procedureSelect.options[procedureSelect.selectedIndex].text;
                        
                        // التحقق من عدم وجود الإجراء مسبقاً
                        const currentProcedures = proceduresText.value.split('\\n').filter(p => p.trim());
                        if (currentProcedures.includes(selectedProcedure)) {{
                            showMessage('هذا الإجراء مضاف مسبقاً', 'warning');
                            procedureSelect.value = '';
                            return;
                        }}
                        
                        // إضافة الإجراء إلى مربع النص
                        let currentText = proceduresText.value;
                        if (currentText.trim()) {{
                            currentText += '\\n' + selectedProcedure;
                        }} else {{
                            currentText = selectedProcedure;
                        }}
                        proceduresText.value = currentText;
                        
                        // إعادة تعيين القائمة المنسدلة
                        procedureSelect.value = '';
                        
                        showMessage('تم إضافة الإجراء', 'success');
                    }}
                }}

                // دالة جمع بيانات النموذج المحدثة
                function getFormData() {{
                    const form = document.getElementById('violationForm');
                    const htmlFormData = new FormData(form);
                    const data = {{}};

                    for (let [key, value] of htmlFormData.entries()) {{
                        data[key] = value;
                    }}

                    // جمع المخالفات من مربع النص - كل مخالفة في سطر منفصل
                    const violationsText = document.getElementById('violations_text').value;
                    data.all_violations = violationsText.split('\\n').filter(v => v.trim()).join('\\n');

                    // جمع الإجراءات من مربع النص - كل إجراء في سطر منفصل
                    const proceduresText = document.getElementById('procedures_text').value;
                    data.all_procedures = proceduresText.split('\\n').filter(p => p.trim()).join('\\n');

                    return data;
                }}

                // تحميل بيانات التلميذ تلقائياً عند تحميل الصفحة
                window.addEventListener('DOMContentLoaded', function() {{
                    const studentCode = document.getElementById('student_code');
                    if (studentCode && studentCode.value) {{
                        loadStudentData(studentCode.value);
                    }}
                    
                    // ربط حدث تغيير كود التلميذ
                    if (studentCode) {{
                        studentCode.addEventListener('input', function() {{
                            loadStudentData(this.value);
                        }});
                    }}
                    
                    // ربط حدث تغيير المادة لتحديث الأساتذة
                    const subjectSelect = document.getElementById('subject');
                    if (subjectSelect) {{
                        subjectSelect.addEventListener('change', function() {{
                            updateTeachers(this.value);
                        }});
                    }}
                    
                    // ربط أحداث القوائم المنسدلة للمخالفات والإجراءات
                    const violationSelect = document.getElementById('violation_type');
                    const procedureSelect = document.getElementById('procedure');
                    
                    if (violationSelect) {{
                        violationSelect.addEventListener('change', addViolationToText);
                    }}
                    
                    if (procedureSelect) {{
                        procedureSelect.addEventListener('change', addProcedureToText);
                    }}
                }});

                // متغير عام لتخزين طلبات البحث
                window.pendingSearchData = null;

                function handleEnterKey(event) {{
                    if (event.key === 'Enter') {{
                        searchStudent();
                    }}
                }}

                function searchStudent() {{
                    const studentCode = document.getElementById('student_search').value.trim();
                    if (!studentCode) {{
                        showAlert('يرجى إدخال رمز التلميذ', 'warning');
                        return;
                    }}

                    showAlert('جاري البحث في قاعدة البيانات...', 'warning');

                    // تعيين علامة طلب البحث للمعالجة
                    window.pendingSearchData = studentCode;
                }}

                function showAlert(message, type) {{
                    const messageDiv = document.getElementById('message');
                    if (messageDiv) {{
                        messageDiv.textContent = message;
                        messageDiv.className = `message ${{type}}`;
                        messageDiv.style.display = 'block';

                        setTimeout(() => {{
                            messageDiv.style.display = 'none';
                        }}, 5000);
                    }}
                }}
            </script>
        </body>
        </html>
        """

        return html_content

    def load_student_data(self):
        """تحميل بيانات التلميذ المحدد"""
        print(f"👨‍🎓 بدء تحميل بيانات التلميذ: {self.student_code}")
        try:
            if not self.student_code or not self.db_manager:
                print("❌ لا يوجد رمز تلميذ أو مدير قاعدة البيانات غير متوفر")
                return

            # البحث عن بيانات التلميذ
            success, result, error = self.db_manager.execute_query(
                """SELECT l.الرمز, s.الاسم_والنسب, l.المستوى, l.القسم, l.رت
                   FROM اللوائح l
                   JOIN السجل_العام s ON l.الرمز = s.الرمز
                   WHERE l.الرمز = ? AND l.السنة_الدراسية = ?""",
                (self.student_code, "2024/2025")
            )

            if success and result:
                student_data = {
                    'code': result[0][0],
                    'name': result[0][1],
                    'level': result[0][2],
                    'section': result[0][3],
                    'rt': result[0][4]
                }
                
                print(f"✅ تم العثور على بيانات التلميذ: {student_data}")

                # تحديث البيانات في النموذج HTML باستخدام JavaScript
                js_code = f"""
                try {{
                    const elements = [
                        ['student_code', '{student_data['code']}'],
                        ['student_rt', '{student_data['rt']}'],
                        ['student_name', '{student_data['name']}'],
                        ['level', '{student_data['level']}'],
                        ['section', '{student_data['section']}']
                    ];
                    
                    elements.forEach(([id, value]) => {{
                        const element = document.getElementById(id);
                        if (element) {{
                            element.value = value;
                        }} else {{
                            console.warn('العنصر غير موجود:', id);
                        }}
                    }});
                    
                    console.log('تم تحديث بيانات التلميذ بنجاح');
                }} catch(error) {{
                    console.error('خطأ في تحديث بيانات التلميذ:', error);
                }}
                """

                self.web_view.page().runJavaScript(js_code)
                self.status_bar.showMessage(f"تم تحميل بيانات التلميذ: {student_data['name']}")
                print(f"✅ تم تحديث البيانات في واجهة HTML")

            else:
                print(f"❌ لم يتم العثور على بيانات التلميذ '{self.student_code}' - خطأ: {error}")

        except Exception as e:
            print(f"❌ خطأ في تحميل بيانات التلميذ: {e}")
            self.status_bar.showMessage(f"خطأ في تحميل بيانات التلميذ: {str(e)}")

    def save_violation(self):
        """حفظ المخالفة في قاعدة البيانات"""
        try:
            # جمع بيانات النموذج من HTML
            js_code = """
            try {
                const violationFormData = getFormData();
                violationFormData;
            } catch(error) {
                console.error('خطأ في جمع بيانات النموذج:', error);
                null;
            }
            """

            def handle_form_data(form_data):
                try:
                    if not form_data:
                        self.show_message("لا توجد بيانات للحفظ", "error")
                        return

                    # معالجة المخالفات والإجراءات المتعددة
                    form_data = self.process_multiple_violations(form_data)

                    # التحقق من البيانات المطلوبة (تحديث للمخالفات المتعددة)
                    required_fields = ['student_code', 'student_name']
                    for field in required_fields:
                        if not form_data.get(field):
                            self.show_message(f"يرجى ملء حقل {field}", "warning")
                            return

                    # التحقق من وجود مخالفة واحدة على الأقل
                    if not form_data.get('all_violations'):
                        self.show_message("يرجى إضافة مخالفة واحدة على الأقل", "warning")
                        return

                    # التحقق من وجود إجراء واحد على الأقل
                    if not form_data.get('all_procedures'):
                        self.show_message("يرجى إضافة إجراء واحد على الأقل", "warning")
                        return

                    # حفظ المخالفة في قاعدة البيانات
                    if self.db_manager:
                        # جلب السنة الدراسية والأسدس من جدول بيانات_المؤسسة
                        academic_year = self.school_year  # استخدام القيمة المحملة مسبقاً
                        semester = self.semester  # استخدام القيمة المحملة مسبقاً
                        
                        # محاولة جلب أحدث البيانات من قاعدة البيانات
                        try:
                            success_academic, result_academic, _ = self.db_manager.execute_query(
                                "SELECT السنة_الدراسية, الأسدس FROM بيانات_المؤسسة LIMIT 1"
                            )
                            
                            if success_academic and result_academic:
                                academic_year = result_academic[0][0] if result_academic[0][0] else self.school_year
                                semester = result_academic[0][1] if result_academic[0][1] else self.semester
                                print(f"✅ تم جلب البيانات من قاعدة البيانات: السنة الدراسية={academic_year}, الأسدس={semester}")
                            else:
                                print(f"⚠️ تعذر جلب البيانات من قاعدة البيانات، سيتم استخدام القيم الافتراضية: السنة الدراسية={academic_year}, الأسدس={semester}")
                        except Exception as e:
                            print(f"❌ خطأ في جلب البيانات من بيانات_المؤسسة: {e}")
                            print(f"🔄 سيتم استخدام القيم الافتراضية: السنة الدراسية={academic_year}, الأسدس={semester}")
                        
                        # تجهيز التاريخ والوقت المدمجين
                        violation_datetime = f"{form_data.get('violation_date')} {form_data.get('violation_time')}"
                        
                        success, result, error = self.db_manager.execute_query(
                            """INSERT INTO المخالفات
                               (التاريخ, رمز_التلميذ, اسم_التلميذ, المستوى, القسم, المادة, الأستاذ,
                                الملاحظات, الإجراءات, تاريخ_التسجيل, السنة_الدراسية, الأسدس, رت)
                               VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                            (
                                violation_datetime,  # التاريخ والوقت مدمجين
                                form_data.get('student_code'),
                                form_data.get('student_name'),
                                form_data.get('level'),
                                form_data.get('section'),
                                form_data.get('subject'),
                                form_data.get('teacher'),
                                # حفظ المخالفات - كل مخالفة في سطر منفصل مع الملاحظات
                                form_data.get('all_violations') + (f"\n\nملاحظات: {form_data.get('notes')}" if form_data.get('notes') else ""),
                                # حفظ الإجراءات - كل إجراء في سطر منفصل
                                form_data.get('all_procedures'),
                                datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                                academic_year,  # السنة الدراسية من جدول بيانات_المؤسسة
                                semester,       # الأسدس من جدول بيانات_المؤسسة
                                form_data.get('student_rt') or form_data.get('rt') or ""  # رت مع احتياطي
                            )
                        )

                        if success:
                            # تجهيز ملخص المخالفات والإجراءات للعرض
                            violations_display = form_data.get('all_violations', 'غير محدد').replace('\n', ' - ')
                            procedures_display = form_data.get('all_procedures', 'غير محدد').replace('\n', ' - ')
                            
                            violation_summary = f"المخالفة: {violations_display}"
                            procedure_summary = f"الإجراء: {procedures_display}"
                            
                            # عرض رسالة في واجهة HTML
                            self.show_message(f"تم حفظ المخالفة بنجاح ✅\n{violation_summary}\n{procedure_summary}", "success")
                            self.status_bar.showMessage("تم حفظ المخالفة بنجاح")
                            
                            # عرض رسالة منبثقة لتأكيد نجاح العملية
                            from PyQt5.QtWidgets import QMessageBox
                            success_msg = QMessageBox(self)
                            success_msg.setIcon(QMessageBox.Information)
                            success_msg.setWindowTitle("نجح الحفظ")
                            success_msg.setText("✅ تم حفظ المخالفة بنجاح")
                            success_msg.setInformativeText(f"""
تفاصيل المخالفة المحفوظة:

👤 التلميذ: {form_data.get('student_name')}
📝 {violation_summary}
⚖️ {procedure_summary}
📅 التاريخ: {form_data.get('violation_date')} في {form_data.get('violation_time')}
                            """)
                            success_msg.setStandardButtons(QMessageBox.Ok)
                            success_msg.setDefaultButton(QMessageBox.Ok)
                            
                            # تطبيق نمط عربي للرسالة
                            success_msg.setStyleSheet("""
                                QMessageBox {
                                    font-family: 'Calibri';
                                    font-size: 14px;
                                    background-color: #f8f9fa;
                                    color: #2c3e50;
                                }
                                QMessageBox QLabel {
                                    color: #2c3e50;
                                    font-weight: bold;
                                }
                                QMessageBox QPushButton {
                                    background-color: #28a745;
                                    color: white;
                                    border: none;
                                    padding: 8px 16px;
                                    border-radius: 4px;
                                    font-weight: bold;
                                    min-width: 80px;
                                }
                                QMessageBox QPushButton:hover {
                                    background-color: #218838;
                                }
                            """)
                            success_msg.exec_()
                            
                            print(f"✅ تم حفظ المخالفة للتلميذ: {form_data.get('student_name')}")
                            print(f"📝 المخالفات المحفوظة: {form_data.get('all_violations', 'غير محدد').replace('\n', ' | ')}")
                            print(f"⚖️ الإجراءات المحفوظة: {form_data.get('all_procedures', 'غير محدد').replace('\n', ' | ')}")
                        else:
                            self.show_message(f"خطأ في حفظ المخالفة: {error}", "error")
                            print(f"❌ فشل في حفظ المخالفة: {error}")
                    else:
                        # وضع التجريب - عرض رسالة منبثقة
                        self.show_message("تم حفظ المخالفة (وضع التجريب)", "success")
                        
                        from PyQt5.QtWidgets import QMessageBox
                        test_msg = QMessageBox(self)
                        test_msg.setIcon(QMessageBox.Information)
                        test_msg.setWindowTitle("وضع التجريب")
                        test_msg.setText("✅ تم حفظ المخالفة (وضع التجريب)")
                        test_msg.setInformativeText(f"""
تفاصيل المخالفة (لم يتم حفظها فعلياً):

👤 التلميذ: {form_data.get('student_name')}
📝 المخالفة: {form_data.get('all_violations', 'غير محدد').replace('\n', ' - ')}
⚖️ الإجراء: {form_data.get('all_procedures', 'غير محدد').replace('\n', ' - ')}
📅 التاريخ: {form_data.get('violation_date')} في {form_data.get('violation_time')}

ملاحظة: لم يتم الحفظ الفعلي في قاعدة البيانات
                        """)
                        test_msg.setStandardButtons(QMessageBox.Ok)
                        test_msg.setDefaultButton(QMessageBox.Ok)
                        
                        # تطبيق نمط عربي للرسالة
                        test_msg.setStyleSheet("""
                            QMessageBox {
                                font-family: 'Calibri';
                                font-size: 14px;
                                background-color: #fff3cd;
                                color: #856404;
                            }
                            QMessageBox QLabel {
                                color: #856404;
                                font-weight: bold;
                            }
                            QMessageBox QPushButton {
                                background-color: #ffc107;
                                color: #212529;
                                border: none;
                                padding: 8px 16px;
                                border-radius: 4px;
                                font-weight: bold;
                                min-width: 80px;
                            }
                            QMessageBox QPushButton:hover {
                                background-color: #e0a800;
                            }
                        """)
                        test_msg.exec_()

                except Exception as e:
                    print(f"خطأ في معالجة بيانات النموذج: {e}")
                    self.show_message(f"خطأ في معالجة البيانات: {str(e)}", "error")

            self.web_view.page().runJavaScript(js_code, handle_form_data)

        except Exception as e:
            print(f"خطأ في حفظ المخالفة: {e}")
            self.show_message(f"خطأ في حفظ المخالفة: {str(e)}", "error")

    def clear_form(self):
        """مسح بيانات النموذج"""
        try:
            js_code = """
            clearForm();
            """
            self.web_view.page().runJavaScript(js_code)

        except Exception as e:
            print(f"خطأ في مسح النموذج: {e}")

    def show_message(self, message, msg_type="success"):
        """عرض رسالة في النافذة"""
        try:
            js_code = f"""
            showMessage('{message}', '{msg_type}');
            """
            self.web_view.page().runJavaScript(js_code)
            self.status_bar.showMessage(message)

        except Exception as e:
            print(f"خطأ في عرض الرسالة: {e}")

    def open_fullscreen(self):
        """فتح النافذة في كامل الشاشة"""
        try:
            # فتح النافذة في كامل الشاشة
            self.showMaximized()
            
            # التأكد من أن النافذة في المقدمة
            self.raise_()
            self.activateWindow()
            
            # تحديث شريط الحالة
            self.status_bar.showMessage("تم فتح النافذة في كامل الشاشة - جاهز لتسجيل المخالفات")
            
            print("✅ تم فتح النافذة في كامل الشاشة بنجاح")
            
        except Exception as e:
            print(f"خطأ في فتح النافذة في كامل الشاشة: {e}")
    
    def showEvent(self, event):
        """تخصيص سلوك عرض النافذة لضمان فتحها بكامل الشاشة"""
        super().showEvent(event)
        
        # التأكد من فتح النافذة في كامل الشاشة في كل مرة يتم عرضها
        if not self.isMaximized():
            QTimer.singleShot(50, self.showMaximized)

    def resizeEvent(self, event):
        """معالجة تغيير حجم النافذة للحفاظ على الشاشة الكاملة"""
        super().resizeEvent(event)
        
        # إذا لم تكن النافذة في وضع كامل الشاشة، أعدها إلى هذا الوضع
        if not self.isMaximized() and self.isVisible():
            QTimer.singleShot(100, self.showMaximized)

    def keyPressEvent(self, event):
        """معالجة ضغط المفاتيح - إضافة اختصارات مفيدة"""
        try:
            # F11 للتبديل بين كامل الشاشة والنافذة العادية
            if event.key() == Qt.Key_F11:
                if self.isMaximized():
                    self.showNormal()
                else:
                    self.showMaximized()
                return
            
            # Ctrl+S للحفظ
            elif event.key() == Qt.Key_S and event.modifiers() == Qt.ControlModifier:
                self.save_violation()
                return
            
            # Ctrl+N لمسح النموذج (جديد)
            elif event.key() == Qt.Key_N and event.modifiers() == Qt.ControlModifier:
                self.clear_form()
                return
            
            # Escape للإغلاق
            elif event.key() == Qt.Key_Escape:
                self.close()
                return
        
        except Exception as e:
            print(f"خطأ في معالجة ضغط المفاتيح: {e}")
        
        # تمرير الحدث للكلاس الأب
        super().keyPressEvent(event)

    # دوال مساعدة للرسائل والتفاعل بالعربية
    def show_arabic_message(self, message, title="رسالة", msg_type="info"):
        """عرض رسالة تأكيد باللغة العربية"""
        try:
            if msg_type == "success":
                icon = QMessageBox.Information 
                title = title or "نجح الأمر"
            elif msg_type == "warning":
                icon = QMessageBox.Warning
                title = title or "تنبيه"
            elif msg_type == "error":
                icon = QMessageBox.Critical
                title = title or "خطأ"
            else:
                icon = QMessageBox.Information
                title = title or "معلومات"
            
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle(title)
            msg_box.setText(message)
            msg_box.setIcon(icon)
            msg_box.setLayoutDirection(Qt.RightToLeft)
            msg_box.setFont(QFont("Calibri", 12))
            
            # تخصيص النمط
            msg_box.setStyleSheet("""
                QMessageBox {
                    background-color: white;
                    font-family: 'Calibri';
                    font-size: 14px;
                }
                QMessageBox QLabel {
                    color: #333;
                    font-family: 'Calibri';
                    font-size: 14px;
                }
                QMessageBox QPushButton {
                    background-color: #4CAF50;
                    color: white;
                    padding: 8px 16px;
                    border: none;
                    border-radius: 4px;
                    font-family: 'Calibri';
                    font-size: 12px;
                    font-weight: bold;
                    min-width: 80px;
                }
                QMessageBox QPushButton:hover {
                    background-color: #45a049;
                }
            """)
            
            # تعيين نص الأزرار بالعربية
            if icon == QMessageBox.Information:
                msg_box.setStandardButtons(QMessageBox.Ok)
                msg_box.button(QMessageBox.Ok).setText("موافق")
            elif icon == QMessageBox.Warning or icon == QMessageBox.Critical:
                msg_box.setStandardButtons(QMessageBox.Ok)
                msg_box.button(QMessageBox.Ok).setText("موافق")
            
            msg_box.exec_()
            
        except Exception as e:
            print(f"خطأ في عرض الرسالة: {e}")

    def closeEvent(self, event):
        """معالجة حدث إغلاق النافذة بدون تأكيد"""
        try:
            # إيقاف مؤقت البحث إذا كان موجوداً
            if hasattr(self, 'search_timer') and self.search_timer:
                self.search_timer.stop()
                print("✅ تم إيقاف مؤقت البحث")

            # إغلاق النافذة مباشرة بدون تأكيد
            print("👋 إغلاق نافذة المخالفات...")
            event.accept()
                
        except Exception as e:
            print(f"خطأ في معالجة إغلاق النافذة: {e}")
            event.accept()  # إغلاق في حالة وجود خطأ

    def save_to_database(self, form_data):
        """حفظ بيانات المخالفة في قاعدة البيانات"""
        try:
            if not self.db_manager:
                return False

            # تحضير البيانات للحفظ مع المخالفات والإجراءات المتعددة
            violation_data = {
                "date": form_data.get('violation_date'),
                "student_code": form_data.get('student_code'),
                "student_name": form_data.get('student_name'),
                "level": form_data.get('level'),
                "section": form_data.get('section'),
                "subject": form_data.get('subject'),
                "teacher": form_data.get('teacher'),
                # استخدام المخالفات والإجراءات المدمجة إذا توفرت، وإلا استخدام الأساسية
                "violation_type": form_data.get('all_violations') or form_data.get('violation_type'),
                "procedure": form_data.get('all_procedures') or form_data.get('procedure'),
                "notes": form_data.get('notes'),
                "school_year": "2024/2025",  # يمكن جلبها من إعدادات النظام
                "semester": "الأول"  # يمكن جلبها من إعدادات النظام
            }

            print(f"📝 حفظ المخالفة - المخالفات: {violation_data['violation_type']}")
            print(f"📝 حفظ المخالفة - الإجراءات: {violation_data['procedure']}")

            # استخدام طريقة حفظ المخالفة في مدير قاعدة البيانات
            success = self.db_manager.save_violation(violation_data)
            return success

        except Exception as e:
            print(f"خطأ في حفظ البيانات في قاعدة البيانات: {e}")
            return False

    def setup_javascript_bridge(self):
        """إعداد الجسر بين JavaScript و Python"""
        try:
            # إعداد معالج للاستجابة لطلبات JavaScript
            def handle_js_requests():
                # فحص طلبات تحديث الأساتذة
                js_code_teachers = """
                if (window.updateTeachersForSubject) {{
                    const subject = window.updateTeachersForSubject;
                    window.updateTeachersForSubject = null;
                    subject;
                }} else {{
                    null;
                }}
                """
                
                def check_teacher_update(subject):
                    if subject:
                        print(f"طلب تحديث الأساتذة للمادة: {subject}")
                        self.update_teachers_for_subject(subject)
                
                self.web_view.page().runJavaScript(js_code_teachers, check_teacher_update)
                
                # فحص طلبات تحميل بيانات التلميذ
                js_code_student = """
                if (window.loadStudentDataRequest) {{
                    const studentCode = window.loadStudentDataRequest;
                    window.loadStudentDataRequest = null;
                    studentCode;
                }} else {{
                    null;
                }}
                """
                
                def check_student_load(student_code):
                    if student_code:
                        print(f"طلب تحميل بيانات التلميذ: {student_code}")
                        self.student_code = student_code
                        self.load_student_data()
                
                self.web_view.page().runJavaScript(js_code_student, check_student_load)
            
            # تشغيل فحص دوري كل ثانية
            from PyQt5.QtCore import QTimer
            self.js_timer = QTimer()
            self.js_timer.timeout.connect(handle_js_requests)
            self.js_timer.start(1000)  # فحص كل ثانية
            
        except Exception as e:
            print(f"خطأ في إعداد جسر JavaScript: {e}")

    def process_multiple_violations(self, form_data):
        """معالجة المخالفات والإجراءات المتعددة - النظام الجديد يأخذ البيانات من مربعات النص مباشرة"""
        try:
            # في النظام الجديد، البيانات موجودة بالفعل في all_violations و all_procedures
            # نحتاج فقط لمعالجة فواصل الأسطر
            
            if form_data.get('all_violations'):
                # تحويل \n إلى فاصل أسطر حقيقي
                form_data['all_violations'] = form_data['all_violations'].replace('\\n', '\n')
            
            if form_data.get('all_procedures'):
                # تحويل \n إلى فاصل أسطر حقيقي
                form_data['all_procedures'] = form_data['all_procedures'].replace('\\n', '\n')
            
            print(f"✅ تم معالجة المخالفات: {form_data.get('all_violations', 'غير محدد')}")
            print(f"✅ تم معالجة الإجراءات: {form_data.get('all_procedures', 'غير محدد')}")
            
            return form_data
            
        except Exception as e:
            print(f"❌ خطأ في معالجة المخالفات المتعددة: {e}")
            return form_data
