import re

# قراءة الملف
try:
    with open('sub37_window.py', 'r', encoding='utf-8') as f:
        content = f.read()
    print(f"تم قراءة الملف - الحجم: {len(content)} حرف")
    
    # عدد الاستخدامات قبل الاستبدال
    old_count = content.count('sqlite3.connect("data.db")')
    print(f"عدد الاستخدامات قبل الاستبدال: {old_count}")
    
    # استبدال جميع استخدامات sqlite3.connect("data.db") 
    content = content.replace('sqlite3.connect("data.db")', 'sqlite3.connect(get_database_path())')
    
    # عدد الاستخدامات بعد الاستبدال
    new_count = content.count('sqlite3.connect("data.db")')
    replaced_count = content.count('sqlite3.connect(get_database_path())')
    print(f"عدد الاستخدامات بعد الاستبدال: {new_count}")
    print(f"عدد الاستخدامات الجديدة: {replaced_count}")
    
    # كتابة الملف المحدث
    with open('sub37_window.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("تم تحديث جميع استخدامات sqlite3.connect في sub37_window.py بنجاح")
    
except Exception as e:
    print(f"خطأ: {e}")
