"""
نافذة حذف الفروض الممسوكة - نافذة مستقلة
"""

import sys
import os
import sqlite3
import traceback
from datetime import datetime
from database_config import get_database_path, get_database_connection
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QComboBox, QPushButton,
    QTableWidget, QTableWidgetItem, QHeaderView, QAbstractItemView, 
    QCheckBox, QWidget, QDialogButtonBox, QApplication
)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont

# استيراد وحدة رسائل التأكيد المخصصة
from sub100_window import ConfirmationDialogs

class DeleteExamsDialog(QDialog):
    """نافذة حذف الفروض الممسوكة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("حذف الفروض الممسوكة")
        self.setFixedSize(500, 300)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # تنسيق النافذة
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
                border: 2px solid #2c3e50;
                border-radius: 15px;
            }
            QLabel {
                font-family: Calibri;
                font-size: 16pt;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: transparent;
            }
            QComboBox {
                border: 2px solid #34495e;
                border-radius: 8px;
                padding: 10px;
                min-height: 35px;
                font-family: Calibri;
                font-size: 13pt;
                font-weight: bold;
                background-color: white;
                color: #2c3e50;
            }
            QComboBox:focus {
                border-color: #3498db;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox QAbstractItemView {
                border: 2px solid #34495e;
                background-color: white;
                selection-background-color: #3498db;
                selection-color: white;
                font-family: Calibri;
                font-size: 13pt;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-family: Calibri;
                font-size: 13pt;
                font-weight: bold;
                min-height: 20px;
                max-height: 20px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
            QPushButton#cancel_button {
                background-color: #95a5a6;
            }
            QPushButton#cancel_button:hover {
                background-color: #7f8c8d;
            }
        """)
        
        self.init_ui()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        # الحصول على السنة الدراسية والأسدس الحاليين
        self.current_academic_year, self.current_semester = self.get_current_academic_info()
        
        # تخطيط النافذة
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(5)

        # عنوان النافذة مع عرض السنة والأسدس
        title_text = "اختر الأستاذ المراد حذف فروضه:"
        if self.current_academic_year and self.current_semester:
            title_text += f"\n({self.current_academic_year} - {self.current_semester})"
        
        title_label = QLabel(title_text)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # قائمة الأساتذة
        self.teacher_combo = QComboBox()
        layout.addWidget(self.teacher_combo)

        # أزرار الإجراءات في وسط النافذة
        buttons_layout = QHBoxLayout()
        buttons_layout.addStretch()
        
        ok_button = QPushButton("متابعة")
        ok_button.clicked.connect(self.show_teacher_exams)
        buttons_layout.addWidget(ok_button)
        
        cancel_button = QPushButton("إلغاء")
        cancel_button.setObjectName("cancel_button")
        cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_button)
        
        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)
        
        # تحميل البيانات بعد إعداد النافذة
        QTimer.singleShot(100, self.load_teachers_delayed)
        
    def load_teachers_delayed(self):
        """تحميل الأساتذة مع تأخير للسماح بعرض النافذة أولاً"""
        self.load_teachers()
        
    def get_current_academic_info(self):
        """الحصول على السنة الدراسية والأسدس الحاليين"""
        try:
            conn = sqlite3.connect(get_database_path())
            cursor = conn.cursor()
            cursor.execute("SELECT السنة_الدراسية, الأسدس FROM بيانات_المؤسسة LIMIT 1")
            result = cursor.fetchone()
            conn.close()
            
            if result:
                return result[0], result[1]
            else:
                return None, None
        except Exception as e:
            print(f"خطأ في جلب السنة الدراسية والأسدس: {e}")
            return None, None
            
    def load_teachers(self):
        """تحميل الأساتذة من قاعدة البيانات مع التصفية"""
        try:
            conn = sqlite3.connect(get_database_path())
            cursor = conn.cursor()
            
            # التحقق من وجود جدول مسك_أوراق_الفروض
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='مسك_أوراق_الفروض'")
            if not cursor.fetchone():
                conn.close()
                message = "لا توجد فروض مسجلة للحذف"
                if self.current_academic_year and self.current_semester:
                    message += f" في السنة الدراسية {self.current_academic_year} والأسدس {self.current_semester}"
                message += "."
                ConfirmationDialogs.show_custom_info_message(self, message, "لا توجد فروض")
                self.close()
                return
            
            # استعلام مفلتر حسب السنة الدراسية والأسدس
            if self.current_academic_year and self.current_semester:
                cursor.execute("""
                    SELECT DISTINCT الأستاذ FROM مسك_أوراق_الفروض
                    WHERE الأستاذ IS NOT NULL AND الأستاذ != ''
                    AND السنة_الدراسية = ? AND الأسدس = ?
                    ORDER BY الأستاذ
                """, (self.current_academic_year, self.current_semester))
            else:
                # في حالة عدم توفر السنة والأسدس، عرض جميع الأساتذة
                cursor.execute("""
                    SELECT DISTINCT الأستاذ FROM مسك_أوراق_الفروض
                    WHERE الأستاذ IS NOT NULL AND الأستاذ != ''
                    ORDER BY الأستاذ
                """)
            
            teachers = [row[0] for row in cursor.fetchall()]
            conn.close()

            if not teachers:
                message = "لا توجد فروض مسجلة للحذف"
                if self.current_academic_year and self.current_semester:
                    message += f" في السنة الدراسية {self.current_academic_year} والأسدس {self.current_semester}"
                message += "."
                ConfirmationDialogs.show_custom_info_message(self, message, "لا توجد فروض")
                self.close()
                return

            self.teacher_combo.addItems(teachers)
            
        except sqlite3.Error as e:
            # خطأ قاعدة بيانات محدد
            if "no such table" in str(e).lower():
                message = "لا توجد فروض مسجلة للحذف."
                ConfirmationDialogs.show_custom_info_message(self, message, "لا توجد فروض")
                self.close()
            else:
                ConfirmationDialogs.show_custom_error_message(self, f"خطأ في الوصول لقاعدة البيانات:\n{str(e)}", "خطأ")
                self.close()
            return
        except Exception as e:
            ConfirmationDialogs.show_custom_error_message(self, f"خطأ في تحميل بيانات الأساتذة:\n{str(e)}", "خطأ")
            self.close()
            return
            
    def show_teacher_exams(self):
        """عرض فروض الأستاذ المحدد"""
        selected_teacher = self.teacher_combo.currentText()
        if selected_teacher:
            self.accept()
            exams_dialog = TeacherExamsDialog(selected_teacher, self.current_academic_year, self.current_semester, self.parent())
            exams_dialog.exec_()


class TeacherExamsDialog(QDialog):
    """نافذة عرض وحذف فروض الأستاذ"""
    
    def __init__(self, teacher_name, academic_year=None, semester=None, parent=None):
        super().__init__(parent)
        self.teacher_name = teacher_name
        self.academic_year = academic_year
        self.semester = semester
        
        # تحديث عنوان النافذة لعرض معلومات التصفية
        title = f"فروض الأستاذ: {teacher_name}"
        if academic_year and semester:
            title += f" - {academic_year} - {semester}"
        
        self.setWindowTitle(title)
        self.setFixedSize(1200, 700)
        self.setLayoutDirection(Qt.RightToLeft)

        # تنسيق النافذة
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
                border: 2px solid #2c3e50;
                border-radius: 15px;
            }
            QLabel#title_label {
                font-family: Calibri;
                font-size: 16pt;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: transparent;
            }
            QLabel#count_label {
                font-family: Calibri;
                font-size: 13pt;
                font-weight: bold;
                color: #e74c3c;
                padding: 5px;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-family: Calibri;
                font-size: 13pt;
                font-weight: bold;
                min-height: 25px;
                max-height: 25px;
                min-width: 130px;
            }
            QPushButton:hover {
                background-color: #2980b9;
                transform: translateY(-2px);
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
            QPushButton#delete_button {
                background-color: #e74c3c;
                min-width: 150px;
            }
            QPushButton#delete_button:hover {
                background-color: #c0392b;
            }
            QPushButton#cancel_button {
                background-color: #95a5a6;
            }
            QPushButton#cancel_button:hover {
                background-color: #7f8c8d;
            }
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #ecf0f1;
                selection-background-color: #3498db;
                selection-color: white;
                border: 2px solid #34495e;
                border-radius: 10px;
                font-family: Calibri;
                font-size: 13pt;
                font-weight: bold;
                color: #2c3e50;
            }
            QTableWidget::item {
                padding: 10px;
                border-bottom: 1px solid #ecf0f1;
            }
            QTableWidget QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 12px;
                font-family: Calibri;
                font-size: 14pt;
                font-weight: bold;
                border: none;
                border-radius: 0px;
            }
            QCheckBox::indicator {
                width: 20px;
                height: 20px;
                border-radius: 3px;
            }
            QCheckBox::indicator:checked {
                background-color: #e74c3c;
                border: 2px solid #c0392b;
            }
            QCheckBox::indicator:unchecked {
                background-color: white;
                border: 2px solid #bdc3c7;
            }
            QCheckBox::indicator:hover {
                border-color: #3498db;
            }
        """)
        
        self.init_ui()
        self.load_teacher_exams_data()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        # تخطيط النافذة
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # عنوان النافذة
        title = f"فروض الأستاذ: {self.teacher_name}"
        if self.academic_year and self.semester:
            title += f" - {self.academic_year} - {self.semester}"
            
        title_label = QLabel(title)
        title_label.setObjectName("title_label")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # جميع الأزرار في الأعلى في صف واحد
        all_buttons_layout = QHBoxLayout()
        all_buttons_layout.setSpacing(30)
        all_buttons_layout.addStretch()
        
        # أزرار التحديد
        select_all_button = QPushButton("تحديد الكل")
        select_all_button.setFixedHeight(25)
        select_all_button.setMinimumWidth(90)
        select_all_button.clicked.connect(lambda: self.select_all_exams(True))
        all_buttons_layout.addWidget(select_all_button)
        
        unselect_all_button = QPushButton("إلغاء تحديد الكل")
        unselect_all_button.setFixedHeight(25)
        unselect_all_button.setMinimumWidth(90)
        unselect_all_button.clicked.connect(lambda: self.select_all_exams(False))
        all_buttons_layout.addWidget(unselect_all_button)
        
        # أزرار الإجراءات
        delete_button = QPushButton("حذف المحدد")
        delete_button.setObjectName("delete_button")
        delete_button.setFixedHeight(25)
        delete_button.setMinimumWidth(90)
        delete_button.clicked.connect(self.delete_selected_exams)
        all_buttons_layout.addWidget(delete_button)
        
        cancel_button = QPushButton("إغلاق")
        cancel_button.setObjectName("cancel_button")
        cancel_button.setFixedHeight(25)
        cancel_button.setMinimumWidth(90)
        cancel_button.clicked.connect(self.reject)
        all_buttons_layout.addWidget(cancel_button)
        
        # عداد السجلات المحددة
        self.selected_count_label = QLabel("المحدد: 0 من 0")
        self.selected_count_label.setObjectName("count_label")
        all_buttons_layout.addWidget(self.selected_count_label)
        
        all_buttons_layout.addStretch()
        layout.addLayout(all_buttons_layout)

        # إنشاء الجدول
        self.exams_table = QTableWidget()
        self.exams_table.setColumnCount(7)
        self.exams_table.setHorizontalHeaderLabels([
            "تحديد", "التاريخ", "المادة", "المستوى", "القسم", "نوع الفرض", "الأسدس"
        ])
        
        # تعيين خصائص الجدول
        self.exams_table.setAlternatingRowColors(True)
        self.exams_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.exams_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        
        # تعيين عرض الأعمدة
        header = self.exams_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # تحديد
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # التاريخ
        header.setSectionResizeMode(2, QHeaderView.Stretch)           # المادة
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # المستوى
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # القسم
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # نوع الفرض
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # الأسدس
        
        layout.addWidget(self.exams_table)
        
    def load_teacher_exams_data(self):
        """تحميل بيانات فروض الأستاذ المحدد مع التصفية"""
        try:
            conn = sqlite3.connect(get_database_path())
            cursor = conn.cursor()
            
            # جلب فروض الأستاذ المحدد مع التصفية حسب السنة الدراسية والأسدس
            if self.academic_year and self.semester:
                cursor.execute("""
                    SELECT id, التاريخ, المادة, المستوى, القسم, نوع_الفرض, الأسدس
                    FROM مسك_أوراق_الفروض
                    WHERE الأستاذ = ? 
                    AND السنة_الدراسية = ? 
                    AND الأسدس = ?
                    ORDER BY التاريخ DESC, المادة, القسم
                """, (self.teacher_name, self.academic_year, self.semester))
            else:
                cursor.execute("""
                    SELECT id, التاريخ, المادة, المستوى, القسم, نوع_الفرض, الأسدس
                    FROM مسك_أوراق_الفروض
                    WHERE الأستاذ = ?
                    ORDER BY التاريخ DESC, المادة, القسم
                """, (self.teacher_name,))
            
            exams_data = cursor.fetchall()
            conn.close()
            
            # التحقق من وجود بيانات
            if not exams_data:
                # عرض رسالة عدم وجود فروض
                self.exams_table.setRowCount(1)
                self.exam_checkboxes = []
                
                # إنشاء رسالة مفصلة
                if self.academic_year and self.semester:
                    no_data_message = f"لا توجد فروض مسجلة لهذا الأستاذ في السنة الدراسية {self.academic_year} - الأسدس {self.semester}"
                else:
                    no_data_message = "لا توجد فروض مسجلة لهذا الأستاذ"
                    
                no_data_item = QTableWidgetItem(no_data_message)
                no_data_item.setTextAlignment(Qt.AlignCenter)
                no_data_item.setFont(QFont("Calibri", 14, QFont.Bold))
                no_data_item.setForeground(Qt.gray)
                self.exams_table.setSpan(0, 0, 1, 7)
                self.exams_table.setItem(0, 0, no_data_item)
                self.exams_table.setRowHeight(0, 60)
                
                # تحديث العداد
                self.selected_count_label.setText("المحدد: 0 من 0")
                return
            
            # تعيين عدد الصفوف
            self.exams_table.setRowCount(len(exams_data))
            
            # تخزين مربعات الاختيار
            self.exam_checkboxes = []
            
            for row, exam in enumerate(exams_data):
                # إنشاء مربع اختيار
                checkbox_widget = QWidget()
                checkbox_layout = QHBoxLayout(checkbox_widget)
                checkbox_layout.setContentsMargins(0, 0, 0, 0)
                checkbox_layout.setAlignment(Qt.AlignCenter)
                
                checkbox = QCheckBox()
                checkbox.setProperty("exam_id", exam[0])  # تخزين ID الفرض
                checkbox.toggled.connect(self.update_selected_count)
                checkbox_layout.addWidget(checkbox)
                
                self.exams_table.setCellWidget(row, 0, checkbox_widget)
                self.exam_checkboxes.append(checkbox)
                
                # إضافة باقي البيانات مع تطبيق خط Calibri 13 أسود غامق
                for col, data in enumerate(exam[1:], 1):  # تخطي ID
                    item = QTableWidgetItem(str(data) if data else "")
                    item.setTextAlignment(Qt.AlignCenter)
                    item.setFont(QFont("Calibri", 13, QFont.Bold))
                    self.exams_table.setItem(row, col, item)
                
                # تعيين ارتفاع الصف
                self.exams_table.setRowHeight(row, 40)
            
            # تحديث العداد
            self.update_selected_count()
            
        except sqlite3.Error as e:
            # خطأ قاعدة بيانات محدد
            if "no such table" in str(e).lower():
                # الجدول غير موجود - هذا يعني عدم وجود فروض مسجلة
                self.exams_table.setRowCount(1)
                self.exam_checkboxes = []
                
                no_data_message = "لا توجد فروض مسجلة لهذا الأستاذ"
                no_data_item = QTableWidgetItem(no_data_message)
                no_data_item.setTextAlignment(Qt.AlignCenter)
                no_data_item.setFont(QFont("Calibri", 14, QFont.Bold))
                no_data_item.setForeground(Qt.gray)
                self.exams_table.setSpan(0, 0, 1, 7)
                self.exams_table.setItem(0, 0, no_data_item)
                self.exams_table.setRowHeight(0, 60)
                self.selected_count_label.setText("المحدد: 0 من 0")
            else:
                # خطأ حقيقي في قاعدة البيانات
                error_message = f"خطأ في الوصول لقاعدة البيانات:\n{str(e)}"
                ConfirmationDialogs.show_custom_error_message(self, error_message, "خطأ")
                traceback.print_exc()
        except Exception as e:
            error_message = f"حدث خطأ أثناء تحميل فروض الأستاذ:\n{str(e)}"
            ConfirmationDialogs.show_custom_error_message(self, error_message, "خطأ")
            traceback.print_exc()
            
    def select_all_exams(self, select):
        """تحديد أو إلغاء تحديد جميع الفروض"""
        try:
            if hasattr(self, 'exam_checkboxes') and self.exam_checkboxes:
                for checkbox in self.exam_checkboxes:
                    checkbox.setChecked(select)
        except AttributeError:
            pass
            
    def update_selected_count(self):
        """تحديث عداد الفروض المحددة"""
        try:
            if hasattr(self, 'exam_checkboxes') and self.exam_checkboxes:
                selected_count = sum(1 for checkbox in self.exam_checkboxes if checkbox.isChecked())
                total_count = len(self.exam_checkboxes)
                self.selected_count_label.setText(f"المحدد: {selected_count} من {total_count}")
            else:
                self.selected_count_label.setText("المحدد: 0 من 0")
        except:
            pass
            
    def delete_selected_exams(self):
        """حذف الفروض المحددة"""
        try:
            # التحقق من وجود مربعات اختيار
            if not hasattr(self, 'exam_checkboxes') or not self.exam_checkboxes:
                message = "لا توجد فروض مسجلة لهذا الأستاذ للحذف."
                if hasattr(self, 'academic_year') and hasattr(self, 'semester') and self.academic_year and self.semester:
                    message = f"لا توجد فروض مسجلة لهذا الأستاذ في السنة الدراسية {self.academic_year} - الأسدس {self.semester}."
                ConfirmationDialogs.show_custom_info_message(self, message, "لا توجد فروض")
                return
            
            # جمع IDs الفروض المحددة
            selected_exam_ids = []
            selected_exam_details = []
            
            for checkbox in self.exam_checkboxes:
                if checkbox.isChecked():
                    exam_id = checkbox.property("exam_id")
                    if exam_id:
                        selected_exam_ids.append(exam_id)
                        
                        # جمع تفاصيل الفرض
                        row = self.exam_checkboxes.index(checkbox)
                        exam_details = {
                            'المادة': self.exams_table.item(row, 2).text(),
                            'القسم': self.exams_table.item(row, 4).text(),
                            'نوع_الفرض': self.exams_table.item(row, 5).text()
                        }
                        selected_exam_details.append(exam_details)
            
            if not selected_exam_ids:
                ConfirmationDialogs.show_custom_warning_message(self, "يرجى تحديد فرض واحد على الأقل للحذف.", "لا توجد فروض محددة")
                return
            
            # إنشاء رسالة تأكيد
            confirm_message = f"هل أنت متأكد من حذف {len(selected_exam_ids)} فرض للأستاذ {self.teacher_name}؟\n\n"
            confirm_message += "الفروض المحددة للحذف:\n"
            confirm_message += "-" * 40 + "\n"
            
            for i, details in enumerate(selected_exam_details[:8]):  # عرض أول 8 فروض
                confirm_message += f"{i+1}. {details['المادة']} - {details['القسم']} - {details['نوع_الفرض']}\n"
            
            if len(selected_exam_details) > 8:
                confirm_message += f"... و {len(selected_exam_details) - 8} فرض آخر\n"
            
            confirm_message += "\nهذا الإجراء لا يمكن التراجع عنه!"
            
            # عرض رسالة التأكيد
            if ConfirmationDialogs.show_custom_confirmation_dialog(self, confirm_message, "تأكيد حذف الفروض"):
                # تنفيذ الحذف
                conn = sqlite3.connect(get_database_path())
                cursor = conn.cursor()
                
                placeholders = ','.join('?' * len(selected_exam_ids))
                cursor.execute(f"DELETE FROM مسك_أوراق_الفروض WHERE id IN ({placeholders})", selected_exam_ids)
                
                deleted_count = cursor.rowcount
                conn.commit()
                conn.close()
                
                # عرض رسالة النجاح
                success_message = f"""<html>
<body dir="rtl" style="text-align: center;">
<p style="font-size: 14pt; font-weight: bold; color: #e74c3c;">تم حذف الفروض بنجاح</p>
<hr style="border: 1px solid #ccc; margin: 10px 0;">
<p style="text-align: right; font-size: 13pt;"><b>الأستاذ:</b> {self.teacher_name}</p>
<p style="text-align: center; font-size: 14pt;"><b>عدد الفروض المحذوفة:</b> {deleted_count}</p>
</body>
</html>"""
                
                ConfirmationDialogs.show_custom_success_message(self, success_message, "نجاح حذف الفروض")
                
                # إعادة تحميل البيانات
                self.load_teacher_exams_data()
                
        except Exception as e:
            error_message = f"حدث خطأ أثناء حذف الفروض:\n{str(e)}"
            ConfirmationDialogs.show_custom_error_message(self, error_message, "خطأ")
            traceback.print_exc()


# دالة مساعدة لفتح نافذة حذف الفروض
def show_delete_exams_dialog(parent=None):
    """عرض نافذة حذف الفروض الممسوكة"""
    dialog = DeleteExamsDialog(parent)
    dialog.exec_()


# اختبار النافذة
if __name__ == "__main__":
    app = QApplication(sys.argv)
    show_delete_exams_dialog()
    sys.exit(app.exec_())
