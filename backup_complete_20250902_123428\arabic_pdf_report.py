"""
تقارير PDF مع دعم كامل للغة العربية وخطوط واضحة
"""

import os
import sys
import sqlite3
from datetime import datetime
import subprocess
from database_config import get_database_path, get_database_connection

try:
    from fpdf import FPDF
    import arabic_reshaper
    from bidi.algorithm import get_display
except ImportError:
    print("المكتبات المطلوبة غير متوفرة. جاري تثبيتها...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "fpdf2", "arabic-reshaper", "python-bidi"])
        from fpdf import FPDF
        import arabic_reshaper
        from bidi.algorithm import get_display
        print("تم تثبيت المكتبات بنجاح!")
    except Exception as e:
        print(f"فشل تثبيت المكتبات: {e}")
        sys.exit(1)

class ArabicPDF(FPDF):
    """فئة مخصصة لإنشاء ملفات PDF مع دعم اللغة العربية"""

    def __init__(self, orientation='P', unit='mm', format='A4'):
        # تعيين الهوامش - 1.2 سم من جميع الجوانب، 1.5 سم من الأعلى
        super().__init__(orientation, unit, format)
        self.set_margins(12, 15, 12)  # left, top, right margins in mm
        self.set_auto_page_break(True, 12)  # bottom margin

        self.current_page = 0
        self._setup_arabic_fonts()
        self.institution_data = None
        self.main_title = "سجل طلبات الشهادات المدرسية"
        self.subtitle = None
        self.dark_blue = (0, 51, 153)  # تعريف اللون الأزرق الغامق - RGB (0, 51, 153)

    def _setup_arabic_fonts(self):
        """إعداد الخطوط العربية"""
        fonts_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "fonts")
        if not os.path.exists(fonts_dir):
            os.makedirs(fonts_dir)

        arabic_fonts = [
            ["fonts/amiri-regular.ttf", "Amiri"],
            ["fonts/arial.ttf", "Arial"],
            ["fonts/calibri.ttf", "Calibri"],
            ["fonts/tahoma.ttf", "Tahoma"]
        ]

        self.arabic_font_added = False

        for font_path, font_name in arabic_fonts:
            full_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), font_path)
            if os.path.exists(full_path):
                try:
                    self.add_font(font_name, '', full_path, uni=True)
                    print(f"تم تحميل الخط العربي: {font_name}")
                    self.arabic_font_name = font_name
                    self.arabic_font_added = True
                    break
                except Exception as e:
                    print(f"خطأ في تحميل الخط {font_name}: {e}")

        if not self.arabic_font_added:
            amiri_path = os.path.join(fonts_dir, "amiri-regular.ttf")
            try:
                print("لم يتم العثور على خطوط عربية. جاري تنزيل خط Amiri...")
                import urllib.request
                amiri_url = "https://github.com/alif-type/amiri/raw/master/AmiriQuran.ttf"
                urllib.request.urlretrieve(amiri_url, amiri_path)
                self.add_font("Amiri", '', amiri_path, uni=True)
                print("تم تنزيل وتحميل خط Amiri بنجاح!")
                self.arabic_font_name = "Amiri"
                self.arabic_font_added = True
            except Exception as e:
                print(f"فشل تنزيل أو تحميل خط Amiri: {e}")
                self.arabic_font_name = "Helvetica"

        calibri_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "fonts", "calibri.ttf")
        calibri_bold_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "fonts", "calibrib.ttf")

        if os.path.exists(calibri_path):
            try:
                self.add_font('Calibri', '', calibri_path, uni=True)
                self.arabic_font_name = 'Calibri'
                self.arabic_font_added = True
                print("تم تحميل خط Calibri بنجاح")

                if os.path.exists(calibri_bold_path):
                    self.add_font('Calibri-Bold', '', calibri_bold_path, uni=True)
                    print("تم تحميل خط Calibri Bold بنجاح")
            except Exception as e:
                print(f"خطأ في تحميل خط Calibri: {e}")

    def load_institution_data(self):
        """تحميل بيانات المؤسسة من قاعدة البيانات"""
        try:
            db_path = get_database_path()
            if os.path.exists(db_path):
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()

                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='بيانات_المؤسسة'")
                if cursor.fetchone():
                    # تعديل الاستعلام لاسترجاع عمود السنة_الدراسية أيضاً
                    cursor.execute("SELECT ImagePath1, المؤسسة, السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
                    row = cursor.fetchone()
                    if row:
                        self.institution_data = {
                            "logo_path": row[0],
                            "name": row[1],
                            "academic_year": row[2] if len(row) > 2 and row[2] else "السنة الدراسية غير محددة"
                        }
                        print(f"تم تحميل بيانات المؤسسة: {self.institution_data['name']}, السنة الدراسية: {self.institution_data['academic_year']}")
                else:
                    print("جدول بيانات المؤسسة غير موجود")

                conn.close()
        except Exception as e:
            print(f"خطأ في تحميل بيانات المؤسسة: {e}")

    def header(self):
        """ترويسة لكل صفحة تتضمن شعار المؤسسة واسمها والعنوان"""
        self.current_page += 1

        if self.institution_data is None:
            self.load_institution_data()

        center_x = self.w / 2
        current_y = 5  # بداية قريبة جداً من حافة الورقة (5 مم فقط من الأعلى)

        # 1. إضافة الشعار أولاً (في الأعلى)
        if self.institution_data and self.institution_data["logo_path"]:
            logo_path = self.institution_data["logo_path"]
            if os.path.exists(logo_path):
                logo_width = 70
                logo_height = 30
                logo_x = center_x - (logo_width / 2)
                self.image(logo_path, x=logo_x, y=current_y, w=logo_width, h=logo_height)
                current_y += logo_height + 5
            else:
                print(f"ملف الشعار غير موجود: {logo_path}")

        # 2. إضافة اسم المؤسسة
        if self.institution_data and self.institution_data["name"]:
            # تعيين اللون الأزرق الغامق
            self.set_text_color(*self.dark_blue)

            # استخدام خط Calibri 17
            font_to_use = 'Calibri' if hasattr(self, 'fonts') and 'Calibri' in self.fonts else self.arabic_font_name
            self.set_font(font_to_use, '', 17)

            institution_name = self.institution_data["name"]
            self.set_y(current_y)  # تعيين الموضع Y بعد الشعار
            self.cell(0, 10, self.arabic_text(institution_name), 0, 1, 'C')
            current_y += 10  # إضافة ارتفاع خلية اسم المؤسسة

        # 3. إضافة العنوان الرئيسي (مع دمج السنة الدراسية)
        # استخدام خط Calibri 16
        font_to_use = 'Calibri' if hasattr(self, 'fonts') and 'Calibri' in self.fonts else self.arabic_font_name
        self.set_font(font_to_use, '', 16)

        # العنوان المدمج مع السنة الدراسية
        title = self.main_title
        if self.current_page > 1:
            title += " (تابع)"
        self.set_y(current_y)  # تعيين الموضع Y بعد اسم المؤسسة
        self.cell(0, 10, self.arabic_text(title), 0, 1, 'C')
        current_y += 10  # إضافة ارتفاع خلية العنوان

        # إعادة تعيين لون النص للأسود
        self.set_text_color(0, 0, 0)

        # تعيين موضع المحتوى بدقة
        self.set_y(current_y + 5)  # إضافة هامش صغير قبل بداية الجدول

    def footer(self):
        """تذييل لكل صفحة"""
        self.set_y(-15)
        if self.arabic_font_added:
            self.set_font(self.arabic_font_name, '', 8)
        else:
            self.set_font('Helvetica', 'I', 8)
        footer_text = f'صفحة {self.current_page} - {datetime.now().strftime("%Y/%m/%d %H:%M")}'
        self.cell(0, 10, self.arabic_text(footer_text), 0, 0, 'C')

    def arabic_text(self, text):
        """معالجة النص العربي ليظهر بشكل صحيح"""
        if text:
            try:
                reshaped_text = arabic_reshaper.reshape(str(text))
                bidi_text = get_display(reshaped_text)
                return bidi_text
            except Exception as e:
                print(f"خطأ في معالجة النص العربي: {e}")
                return str(text)
        return ''

    def add_table_headers(self, headers, col_widths):
        """إضافة رأس الجدول - بخط Calibri 14 أبيض غامق"""
        # تعيين لون أزرق داكن للخلفية
        self.set_fill_color(0, 51, 153)  # لون أزرق داكن للرأس
        self.set_text_color(255, 255, 255)  # نص أبيض

        # استخدام خط Calibri 14
        font_to_use = 'Calibri-Bold' if hasattr(self, 'fonts') and 'Calibri-Bold' in self.fonts else self.arabic_font_name
        self.set_font(font_to_use, '', 14)
        self.set_line_width(0.3)

        for i, (header, width) in enumerate(zip(headers, col_widths)):
            self.cell(width, 10, self.arabic_text(header), 1, 0, 'C', True)
        self.ln()
        self.set_text_color(0, 0, 0)

    def add_table_row(self, row_data, col_widths, fill=False):
        """إضافة صف في الجدول - بخط Calibri 12 أسود غامق"""
        # استخدام خط Calibri 12
        font_to_use = 'Calibri-Bold' if hasattr(self, 'fonts') and 'Calibri-Bold' in self.fonts else self.arabic_font_name
        self.set_font(font_to_use, '', 12)
        if fill:
            self.set_fill_color(240, 240, 240)  # لون رمادي فاتح
        else:
            self.set_fill_color(255, 255, 255)  # لون أبيض

        for i, (cell, width) in enumerate(zip(row_data, col_widths)):
            cell_text = self.arabic_text(cell) if cell is not None else ''
            self.cell(width, 8, cell_text, 1, 0, 'C', fill)
        self.ln()

    def add_signature_section(self, delivered_count=0):
        """إضافة قسم توقيع الحراسة العامة وتاريخ الطبع"""
        # إضافة فراغ قبل قسم التوقيع
        self.ln(20)
        # الانتقال إلى الجزء السفلي من الصفحة مع ترك هامش
        remaining_height = self.h - self.get_y() - 20
        if remaining_height < 60:  # إذا لم يكن هناك مساحة كافية، أضف صفحة جديدة
            self.add_page()

        # إنشاء جدول أنيق للتوقيع والتاريخ
        self.set_line_width(0.5)
        self.set_draw_color(0, 51, 153)  # لون أزرق داكن للإطار

        # تقسيم العرض إلى قسمين
        half_width = (self.w - 24) / 2  # 24 هو مجموع الهوامش اليمنى واليسرى

        # العنوان الرئيسي للقسم
        font_to_use = 'Calibri-Bold' if hasattr(self, 'fonts') and 'Calibri-Bold' in self.fonts else self.arabic_font_name
        self.set_font(font_to_use, '', 14)
        self.set_fill_color(240, 240, 240)
        self.cell(half_width*2, 10, self.arabic_text("معلومات التقرير"), 1, 1, 'C', True)

        # مربع توقيع الحراسة العامة
        self.set_font(font_to_use, '', 12)
        self.cell(half_width, 10, self.arabic_text("توقيع الحراسة العامة"), 1, 0, 'C', True)

        # مربع تاريخ الطبع
        date_text = f"تاريخ الطبع: {datetime.now().strftime('%Y/%m/%d %H:%M')}"
        self.cell(half_width, 10, self.arabic_text(date_text), 1, 1, 'C', True)

        # مساحة للتوقيع
        self.cell(half_width, 30, "", 1, 0, 'C')

        # معلومات إضافية عن التقرير
        # استخدام السنة الدراسية في معلومات التوقيع
        academic_year = ""
        if self.institution_data and "academic_year" in self.institution_data:
            academic_year = self.institution_data["academic_year"]
        else:
            academic_year = "السنة الدراسية غير محددة"

        signature_info = f"عدد الصفحات: {self.current_page}\nالسنة الدراسية: {academic_year}\nعدد الشواهد المسلمة: {delivered_count}"
        self.multi_cell(half_width, 10, self.arabic_text(signature_info), 1, 'C') # تم تعديل ارتفاع الخلية لـ 10 لاستيعاب 3 أسطر

def download_arabic_fonts():
    """تنزيل الخطوط العربية إذا لم تكن موجودة"""
    fonts_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "fonts")
    if not os.path.exists(fonts_dir):
        os.makedirs(fonts_dir)
    fonts = {
        "amiri-regular.ttf": "https://github.com/alif-type/amiri/raw/master/AmiriQuran.ttf"
    }
    for font_name, font_url in fonts.items():
        font_path = os.path.join(fonts_dir, font_name)
        if not os.path.exists(font_path):
            try:
                print(f"جاري تنزيل الخط {font_name}...")
                import urllib.request
                urllib.request.urlretrieve(font_url, font_path)
                print(f"تم تنزيل الخط {font_name} بنجاح!")
            except Exception as e:
                print(f"فشل تنزيل الخط {font_name}: {e}")
    return os.path.exists(os.path.join(fonts_dir, list(fonts.keys())[0]))

def create_certificates_report(records=None, output_dir=None, auto_open=False):
    """إنشاء تقرير الشهادات المدرسية باستخدام FPDF المحسنة"""
    download_arabic_fonts()

    # استخدام المجلد المحدد إذا تم تمريره، وإلا استخدام المجلد الافتراضي
    if output_dir and os.path.exists(output_dir):
        reports_dir = output_dir
    else:
        reports_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "طلبات_الشواهد_المدرسية")
        if not os.path.exists(reports_dir):
            os.makedirs(reports_dir)

    current_datetime = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_path = os.path.join(reports_dir, f"طلبات_الشهادات_المدرسية_{current_datetime}.pdf")
    if records is None:
        try:
            db_path = get_database_path()
            if os.path.exists(db_path):
                print("جاري استرجاع البيانات من قاعدة البيانات...")
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT الرقم, القسم, الرمز, الاسم_والنسب,
                           تاريخ_الطلب, تاريخ_التسليم, ملاحظات
                    FROM الشهادة_المدرسية
                    ORDER BY id ASC
                """)
                records = cursor.fetchall()
                conn.close()
                if not records:
                    print("لا توجد بيانات في قاعدة البيانات. سيتم استخدام بيانات تجريبية.")
                    records = generate_test_data()
            else:
                print("قاعدة البيانات غير موجودة. سيتم استخدام بيانات تجريبية.")
                records = generate_test_data()
        except Exception as e:
            print(f"خطأ في قراءة قاعدة البيانات: {e}")
            records = generate_test_data()

    pdf = ArabicPDF('L', 'mm', 'A4')
    pdf.set_auto_page_break(False, margin=12)

    # تحميل بيانات المؤسسة لضمان تحميل السنة الدراسية
    if pdf.institution_data is None:
        pdf.load_institution_data()

    # دمج السنة الدراسية مع العنوان الرئيسي
    academic_year = ""
    if pdf.institution_data and "academic_year" in pdf.institution_data:
        academic_year = pdf.institution_data["academic_year"]
    else:
        academic_year = "السنة الدراسية غير محددة"

    # إنشاء العنوان المدمج
    pdf.main_title = f"سجل طلبات الشهادات المدرسية للسنة الدراسية {academic_year}"
    pdf.subtitle = None  # لا حاجة للعنوان الفرعي الآن

    pdf.add_page()

    headers_logical = ["الترتيب", "القسم", "الرمز", "الاسم والنسب", "تاريخ الطلب", "تاريخ التسليم", "ملاحظات"]
    col_widths_logical = [20, 30, 30, 80, 30, 30, 40]

    display_headers = headers_logical[::-1]
    display_col_widths = col_widths_logical[::-1]

    table_total_width = sum(col_widths_logical)
    printable_page_width = pdf.w - pdf.l_margin - pdf.r_margin
    centering_offset = (printable_page_width - table_total_width) / 2
    if centering_offset < 0:
        centering_offset = 0

    pdf.set_x(pdf.l_margin + centering_offset)
    pdf.add_table_headers(display_headers, display_col_widths)

    current_page_actual_height = pdf.h
    header_actual_consumption = 65
    footer_margin_space = 15
    data_row_height = 8

    available_height_for_table_content = current_page_actual_height - header_actual_consumption - footer_margin_space

    if available_height_for_table_content > 0:
        rows_per_page = int((available_height_for_table_content - 10) / data_row_height) - 1
        if rows_per_page < 0:
            rows_per_page = 0
    else:
        rows_per_page = 0

    print(f"إنشاء تقرير بـ {len(records)} سجل - حد أقصى {rows_per_page} صفوف في كل صفحة (ارتفاع الصفحة: {pdf.h}مم, عرض الجدول: {table_total_width}مم, إزاحة التوسيط: {centering_offset}مم)")

    row_count = 0
    page_number = 1

    for i, record_logical in enumerate(records):
        if row_count >= rows_per_page and rows_per_page > 0:
            page_number += 1
            pdf.add_page()
            pdf.set_x(pdf.l_margin + centering_offset)
            pdf.add_table_headers(display_headers, display_col_widths)
            row_count = 0
            print(f"إنشاء صفحة جديدة رقم {page_number}")

        pdf.set_x(pdf.l_margin + centering_offset)

        display_record = record_logical[::-1]
        pdf.add_table_row(display_record, display_col_widths, fill=(i % 2 == 0))
        row_count += 1

    # حساب عدد الشهادات المسلمة
    # تاريخ التسليم هو العنصر السادس (index 5) في record_logical
    delivered_count = 0
    if records: # التأكد من أن القائمة ليست فارغة
        for record in records:
            if record[5] and str(record[5]).strip(): # إذا كان تاريخ التسليم موجودًا وليس فارغًا
                delivered_count += 1

    pdf.add_signature_section(delivered_count=delivered_count)

    print(f"تم إنشاء التقرير بنجاح: {page_number} صفحة تحتوي على {len(records)} سجل, منها {delivered_count} شهادة مسلمة.")

    pdf.output(output_path)
    print(f"تم إنشاء التقرير بنجاح: {output_path}")

    # فتح الملف تلقائيًا فقط إذا كانت المعلمة auto_open تساوي True
    if auto_open:
        try:
            # نظام ويندوز فقط لأن التطبيق يعمل على ويندوز
            os.startfile(output_path)
        except Exception as e:
            print(f"خطأ في فتح الملف: {e}")

    return output_path

def generate_test_data():
    """توليد بيانات تجريبية للاختبار"""
    test_data = []
    for i in range(1, 100):
        record_logical = [
            str(i),                         # ت.ر (الرقم)
            f"القسم {i%5+1}",              # القسم
            f"R{i:03d}",                   # الرمز
            f"اسم الطالب رقم {i}",         # الاسم والنسب
            f"2024-05-{i%30+1:02d}",       # تاريخ الطلب
            None if i % 3 != 0 else f"2024-06-{i%30+1:02d}",  # تاريخ التسليم
            "بدون ملاحظات" if i % 4 != 0 else "تم التسليم",   # ملاحظات
        ]
        test_data.append(record_logical)
    return test_data

def print_certificates_requests(parent=None):
    """طباعة سجلات طلبات الشهادات المدرسية - وظيفة متوافقة مع الدالة الأصلية في print9.py"""
    try:
        # إنشاء المجلد الرئيسي على سطح المكتب
        desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
        main_folder = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
        if not os.path.exists(main_folder):
            os.makedirs(main_folder)

        # إنشاء المجلد الفرعي لتقارير طلبات الشواهد المدرسية
        reports_dir = os.path.join(main_folder, "تقارير طلبات الشواهد المدرسية")
        if not os.path.exists(reports_dir):
            os.makedirs(reports_dir)

        # إنشاء مجلد احتياطي في حالة فشل الوصول إلى سطح المكتب
        backup_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "طلبات_الشواهد_المدرسية")
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)

        # التحقق من إمكانية الكتابة في المجلد على سطح المكتب
        try:
            test_file = os.path.join(reports_dir, "test.txt")
            with open(test_file, 'w') as f:
                f.write("test")
            os.remove(test_file)
        except Exception as e:
            print(f"تعذر الكتابة في مجلد سطح المكتب: {e}")
            reports_dir = backup_dir

        current_datetime = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_path = os.path.join(reports_dir, f"طلبات_الشهادات_المدرسية_{current_datetime}.pdf")
        try:
            db_path = get_database_path()
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT الرقم, القسم, الرمز, الاسم_والنسب,
                       تاريخ_الطلب, تاريخ_التسليم, ملاحظات
                FROM الشهادة_المدرسية
                ORDER BY id ASC
            """)

            records = cursor.fetchall()
            conn.close()

            if not records:
                print("لا توجد سجلات للطباعة")
                return False, file_path, reports_dir
        except Exception as e:
            print(f"خطأ في قراءة قاعدة البيانات: {e}")
            return False, "", ""

        # استخدام المجلد المحدد وعدم فتح الملف تلقائيًا
        output_path = create_certificates_report(records, reports_dir, auto_open=False)
        if output_path and os.path.exists(output_path):
            print(f"تمت طباعة {len(records)} سجل بنجاح")
            print(f"الملف تم حفظه في: {output_path}")

            # نكتفي بإرجاع النتيجة الناجحة فقط بدون عرض أي رسائل
            # سيتم التعامل مع عرض الرسائل المخصصة في sub19_window.py
            return True, output_path, reports_dir
        else:
            print("فشل في إنشاء التقرير")
            return False, "", ""
    except Exception as e:
        print(f"خطأ أثناء طباعة طلبات الشهادات المدرسية: {e}")
        if parent:
            try:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.critical(parent, "خطأ", f"حدث خطأ أثناء طباعة طلبات الشهادات المدرسية:\n{str(e)}")
            except ImportError:
                pass
        return False, "", ""

if __name__ == "__main__":
    print("===== إنشاء تقرير الشهادات المدرسية باستخدام FPDF المحسنة =====")
    create_certificates_report()
